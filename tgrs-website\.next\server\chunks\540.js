exports.id=540,exports.ids=[540],exports.modules={6652:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,3219,23)),Promise.resolve().then(r.t.bind(r,4863,23)),Promise.resolve().then(r.t.bind(r,5155,23)),Promise.resolve().then(r.t.bind(r,802,23)),Promise.resolve().then(r.t.bind(r,9350,23)),Promise.resolve().then(r.t.bind(r,8530,23)),Promise.resolve().then(r.t.bind(r,8921,23))},4796:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6959,23)),Promise.resolve().then(r.t.bind(r,3875,23)),Promise.resolve().then(r.t.bind(r,8903,23)),Promise.resolve().then(r.t.bind(r,7174,23)),Promise.resolve().then(r.t.bind(r,4178,23)),Promise.resolve().then(r.t.bind(r,7190,23)),Promise.resolve().then(r.t.bind(r,1365,23))},2353:(e,t,r)=>{Promise.resolve().then(r.bind(r,4417))},5505:(e,t,r)=>{Promise.resolve().then(r.bind(r,5165))},5165:(e,t,r)=>{"use strict";r.d(t,{default:()=>i});var a=r(5512),n=r(7323);function i(){return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(n.default,{src:"https://www.googletagmanager.com/gtag/js?id=GA_MEASUREMENT_ID",strategy:"afterInteractive"}),(0,a.jsx)(n.default,{id:"google-analytics",strategy:"afterInteractive",children:`
          window.dataLayer = window.dataLayer || [];
          function gtag(){dataLayer.push(arguments);}
          gtag('js', new Date());
          gtag('config', 'GA_MEASUREMENT_ID', {
            page_title: document.title,
            page_location: window.location.href,
            custom_map: {
              'custom_parameter_1': 'server_type',
              'custom_parameter_2': 'user_language'
            }
          });
          
          // Track Telugu gaming events
          gtag('event', 'page_view', {
            'custom_parameter_1': 'telugu_fivem',
            'custom_parameter_2': 'telugu'
          });
        `}),(0,a.jsx)(n.default,{id:"microsoft-clarity",strategy:"afterInteractive",children:`
          (function(c,l,a,r,i,t,y){
            c[a]=c[a]||function(){(c[a].q=c[a].q||[]).push(arguments)};
            t=l.createElement(r);t.async=1;t.src="https://www.clarity.ms/tag/"+i;
            y=l.getElementsByTagName(r)[0];y.parentNode.insertBefore(t,y);
          })(window, document, "clarity", "script", "CLARITY_PROJECT_ID");
        `}),(0,a.jsx)(n.default,{id:"facebook-pixel",strategy:"afterInteractive",children:`
          !function(f,b,e,v,n,t,s)
          {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
          n.callMethod.apply(n,arguments):n.queue.push(arguments)};
          if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
          n.queue=[];t=b.createElement(e);t.async=!0;
          t.src=v;s=b.getElementsByTagName(e)[0];
          s.parentNode.insertBefore(t,s)}(window, document,'script',
          'https://connect.facebook.net/en_US/fbevents.js');
          fbq('init', 'FACEBOOK_PIXEL_ID');
          fbq('track', 'PageView');
          
          // Track Telugu gaming interest
          fbq('trackCustom', 'TeluguGamingInterest', {
            server_type: 'fivem',
            language: 'telugu',
            community: 'tgrs'
          });
        `}),(0,a.jsx)(n.default,{id:"hotjar",strategy:"afterInteractive",children:`
          (function(h,o,t,j,a,r){
            h.hj=h.hj||function(){(h.hj.q=h.hj.q||[]).push(arguments)};
            h._hjSettings={hjid:HOTJAR_ID,hjsv:6};
            a=o.getElementsByTagName('head')[0];
            r=o.createElement('script');r.async=1;
            r.src=t+h._hjSettings.hjid+j+h._hjSettings.hjsv;
            a.appendChild(r);
          })(window,document,'https://static.hotjar.com/c/hotjar-','.js?sv=');
        `}),(0,a.jsx)(n.default,{id:"telugu-gaming-tracking",strategy:"afterInteractive",children:`
          // Track Telugu gaming specific events
          function trackTeluguGamingEvent(eventName, properties = {}) {
            // Google Analytics
            if (typeof gtag !== 'undefined') {
              gtag('event', eventName, {
                event_category: 'Telugu Gaming',
                event_label: 'TGRS',
                ...properties
              });
            }
            
            // Facebook Pixel
            if (typeof fbq !== 'undefined') {
              fbq('trackCustom', eventName, {
                server: 'tgrs',
                language: 'telugu',
                ...properties
              });
            }
            
            // Console log for debugging
            console.log('Telugu Gaming Event:', eventName, properties);
          }
          
          // Track page interactions
          document.addEventListener('DOMContentLoaded', function() {
            // Track Discord link clicks
            document.querySelectorAll('a[href*="discord.gg"]').forEach(link => {
              link.addEventListener('click', () => {
                trackTeluguGamingEvent('discord_click', {
                  source: 'website',
                  page: window.location.pathname
                });
              });
            });
            
            // Track VIP interest
            document.querySelectorAll('a[href*="/vip"]').forEach(link => {
              link.addEventListener('click', () => {
                trackTeluguGamingEvent('vip_interest', {
                  source: 'navigation',
                  page: window.location.pathname
                });
              });
            });
            
            // Track server join attempts
            document.querySelectorAll('a[href*="cfx.re"]').forEach(link => {
              link.addEventListener('click', () => {
                trackTeluguGamingEvent('server_join_attempt', {
                  source: 'website',
                  page: window.location.pathname
                });
              });
            });
          });
          
          // Track scroll depth for engagement
          let maxScroll = 0;
          window.addEventListener('scroll', () => {
            const scrollPercent = Math.round((window.scrollY / (document.body.scrollHeight - window.innerHeight)) * 100);
            if (scrollPercent > maxScroll && scrollPercent % 25 === 0) {
              maxScroll = scrollPercent;
              trackTeluguGamingEvent('scroll_depth', {
                depth: scrollPercent,
                page: window.location.pathname
              });
            }
          });
        `})]})}},1354:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>u,metadata:()=>o});var a=r(2740),n=r(9886),i=r.n(n),s=r(5938),g=r.n(s);r(1135);var l=r(4417);let o={title:{default:"TGRS - Telugu Gaming Roleplay Server | Best FiveM Telugu Community",template:"%s | TGRS - Telugu Gaming Roleplay Server"},description:"Join TGRS - The #1 Telugu FiveM roleplay server! Experience immersive GTA 5 roleplay with Telugu community, custom scripts, realistic economy, and 24/7 uptime. Best Telugu gaming server for FiveM enthusiasts.",keywords:["FiveM","FiveM server","FiveM Telugu","Telugu FiveM","FiveM roleplay","FiveM RP","GTA 5 roleplay","GTA V RP","GTA roleplay server","GTA 5 server","GTA 5 Telugu","Telugu GTA 5","GTA Telugu","Telugu GTA","GTA 5 Telugu server","Telugu GTA 5 server","Telugu gaming","Telugu gamers","Telugu gaming community","Telugu gaming server","Telugu online gaming","Telugu multiplayer games","Telugu game server","Telugu","Telugu esports","Telugu gaming clan","Telugu gaming group","Telugu gaming discord","Telugu gaming YouTube","Telugu gaming streamers","Telugu gaming content","Telugu gaming news","Telugu gaming updates","Telugu gaming events","SAMP Telugu","Telugu SAMP","SAMP Telugu server","Telugu SAMP server","San Andreas Telugu","Telugu San Andreas","SAMP roleplay Telugu","Telugu SAMP community","SAMP Telugu players","Telugu SAMP gaming","Telugu server","server Telugu","Telugu gaming server","Telugu roleplay server","Telugu multiplayer server","Telugu community server","Telugu FiveM server","Telugu GTA server","Telugu game server","Telugu online server","Hyderabad gaming","Telangana gaming","Andhra Pradesh gaming","South India gaming","Indian gaming server","Indian FiveM","India roleplay server","Hyderabad FiveM","Telangana FiveM","Andhra Pradesh FiveM","Hyderabad Telugu gaming","Telangana Telugu gaming","Andhra Pradesh Telugu gaming","Vijayawada gaming","Visakhapatnam gaming","Warangal gaming","Guntur gaming","Nellore gaming","roleplay server","RP server","gaming community","online gaming","multiplayer gaming","gaming server","game server","gaming clan","esports","competitive gaming","casual gaming","gaming hub","gaming platform","gaming network","gaming portal","Telugu culture gaming","Telugu tradition gaming","Telugu language gaming","Telugu speaking gamers","Telugu gaming culture","Telugu gaming tradition","Telugu gaming heritage","Telugu gaming pride","Telugu gaming unity","custom scripts","realistic economy","24/7 uptime","active community","VIP features","custom cars","immersive roleplay","professional staff","Telugu scripts","Telugu features","Telugu customization","TGRS","Telugu Gaming Roleplay Server","Telugu community","gaming friends","Discord server","gaming discord","Telugu discord","Telugu gaming family","Telugu gaming brotherhood","Telugu gaming sisterhood","Telugu gaming unity","Telugu FPS","Telugu racing","Telugu simulation","Telugu adventure","Telugu action games","Telugu strategy games","Telugu RPG","Telugu MMO","Telugu battle royale","Telugu survival games","Telugu sandbox games","తెలుగు గేమింగ్","తెలుగు గేమర్స్","తెలుగు సర్వర్","తెలుగు కమ్యూనిటీ","Telugu gaming 2024","Telugu gaming 2025","best Telugu gaming","top Telugu gaming","popular Telugu gaming","famous Telugu gaming"],authors:[{name:"TGRS Team",url:"https://tgrs.com"}],creator:"TGRS Development Team",publisher:"TGRS",formatDetection:{email:!1,address:!1,telephone:!1},metadataBase:new URL("https://tgrs.com"),alternates:{canonical:"/",languages:{"en-US":"/","te-IN":"/"}},icons:{icon:[{url:"/assets/favicon-16x16.png",sizes:"16x16",type:"image/png"},{url:"/assets/favicon-32x32.png",sizes:"32x32",type:"image/png"},{url:"/assets/favicon-96x96.png",sizes:"96x96",type:"image/png"}],shortcut:"/assets/favicon-32x32.png",apple:"/assets/favicon-96x96.png"},openGraph:{title:"TGRS - #1 Telugu FiveM Roleplay Server | Best Telugu Gaming Community",description:"Join TGRS - The premier Telugu FiveM roleplay server! Experience immersive GTA 5 roleplay with Telugu community, custom scripts, realistic economy, and 24/7 uptime. Best Telugu gaming server for FiveM enthusiasts.",url:"https://tgrs.com",siteName:"TGRS - Telugu Gaming Roleplay Server",locale:"en_US",type:"website",images:[{url:"/assets/tgrs-og-image.jpg",width:1200,height:630,alt:"TGRS - Telugu Gaming Roleplay Server - Best FiveM Telugu Community"},{url:"/assets/tgrs-logo.png",width:512,height:512,alt:"TGRS Logo"}]},twitter:{card:"summary_large_image",title:"TGRS - #1 Telugu FiveM Roleplay Server",description:"Join the best Telugu FiveM community! Immersive roleplay, custom scripts, 24/7 uptime. Experience GTA 5 like never before with TGRS!",images:["/assets/tgrs-og-image.jpg"],creator:"@TGRS_Official",site:"@TGRS_Official"},robots:{index:!0,follow:!0,googleBot:{index:!0,follow:!0,"max-video-preview":-1,"max-image-preview":"large","max-snippet":-1}},verification:{google:"your-google-verification-code",yandex:"your-yandex-verification-code",yahoo:"your-yahoo-verification-code"}};function u({children:e}){return(0,a.jsxs)("html",{lang:"en",className:"scroll-smooth",children:[(0,a.jsxs)("head",{children:[(0,a.jsx)("link",{rel:"stylesheet",href:"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css",integrity:"sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw==",crossOrigin:"anonymous",referrerPolicy:"no-referrer"}),(0,a.jsx)("script",{type:"application/ld+json",dangerouslySetInnerHTML:{__html:JSON.stringify({"@context":"https://schema.org","@type":"Organization",name:"TGRS - Telugu Gaming Roleplay Server",alternateName:"TGRS",url:"https://tgrs.com",logo:"https://tgrs.com/assets/tgrs-logo.png",description:"The premier Telugu FiveM roleplay server with immersive experiences and unique features",foundingDate:"2024",sameAs:["https://discord.gg/GAMravHDnB","https://www.youtube.com/@GTA5RPTGRSCITY"],contactPoint:{"@type":"ContactPoint",contactType:"customer service",url:"https://discord.gg/GAMravHDnB"}})}}),(0,a.jsx)("script",{type:"application/ld+json",dangerouslySetInnerHTML:{__html:JSON.stringify({"@context":"https://schema.org","@type":"SoftwareApplication",name:"TGRS FiveM Server",applicationCategory:"GameApplication",operatingSystem:"Windows, Mac, Linux",description:"Telugu FiveM roleplay server offering immersive GTA 5 roleplay experience with custom scripts and active community",offers:{"@type":"Offer",price:"0",priceCurrency:"USD",availability:"https://schema.org/InStock"},aggregateRating:{"@type":"AggregateRating",ratingValue:"4.8",ratingCount:"500",bestRating:"5"},provider:{"@type":"Organization",name:"TGRS"}})}}),(0,a.jsx)("script",{type:"application/ld+json",dangerouslySetInnerHTML:{__html:JSON.stringify({"@context":"https://schema.org","@type":"WebSite",name:"TGRS - Telugu Gaming Roleplay Server",url:"https://tgrs.com",description:"Official website of TGRS - The premier Telugu FiveM roleplay server",inLanguage:"en-US",potentialAction:{"@type":"SearchAction",target:"https://tgrs.com/search?q={search_term_string}","query-input":"required name=search_term_string"}})}}),(0,a.jsx)("link",{rel:"preconnect",href:"https://fonts.googleapis.com"}),(0,a.jsx)("link",{rel:"preconnect",href:"https://fonts.gstatic.com",crossOrigin:""}),(0,a.jsx)("link",{rel:"preconnect",href:"https://cdnjs.cloudflare.com"}),(0,a.jsx)("link",{rel:"dns-prefetch",href:"//discord.gg"}),(0,a.jsx)("link",{rel:"dns-prefetch",href:"//youtube.com"}),(0,a.jsx)("link",{rel:"dns-prefetch",href:"//www.youtube.com"})]}),(0,a.jsxs)("body",{className:`${i().variable} ${g().variable} antialiased`,suppressHydrationWarning:!0,children:[(0,a.jsx)(l.default,{}),e]})]})}},4417:(e,t,r)=>{"use strict";r.d(t,{default:()=>a});let a=(0,r(6760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\TGRS Website\\\\tgrs-website\\\\src\\\\components\\\\Analytics.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\TGRS Website\\tgrs-website\\src\\components\\Analytics.tsx","default")},1135:()=>{}};