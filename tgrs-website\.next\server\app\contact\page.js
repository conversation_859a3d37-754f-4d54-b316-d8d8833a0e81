(()=>{var e={};e.id=977,e.ids=[977],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},9551:e=>{"use strict";e.exports=require("url")},628:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>a.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>h,tree:()=>d});var t=r(260),n=r(8203),o=r(5155),a=r.n(o),i=r(7292),l={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);r.d(s,l);let d=["",{children:["contact",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,6450)),"C:\\Users\\<USER>\\Desktop\\TGRS Website\\tgrs-website\\src\\app\\contact\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,1354)),"C:\\Users\\<USER>\\Desktop\\TGRS Website\\tgrs-website\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,9937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,9116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,1485,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["C:\\Users\\<USER>\\Desktop\\TGRS Website\\tgrs-website\\src\\app\\contact\\page.tsx"],m={require:r,loadChunk:()=>Promise.resolve()},h=new t.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/contact/page",pathname:"/contact",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},2935:(e,s,r)=>{Promise.resolve().then(r.bind(r,6450))},7663:(e,s,r)=>{Promise.resolve().then(r.bind(r,2622))},2622:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>l});var t=r(5512),n=r(8009),o=r(6519),a=r(8784),i=r(2149);function l(){let[e,s]=(0,n.useState)({name:"",email:"",discord:"",subject:"",message:""}),[r,l]=(0,n.useState)(!1),[d,c]=(0,n.useState)(null),m=e=>{let{name:r,value:t}=e.target;s(e=>({...e,[r]:t}))};return(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(o.default,{}),(0,t.jsxs)("div",{className:"pt-24 pb-12 md:pt-32 md:pb-20 relative bg-black",children:[(0,t.jsx)("div",{className:"absolute inset-0 pattern-circuit opacity-5"}),(0,t.jsx)("div",{className:"container mx-auto px-4 md:px-6 relative z-10",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsxs)("h1",{className:"text-4xl md:text-5xl font-display font-bold text-white mb-6 wave-heading-bg",children:[(0,t.jsx)("span",{className:"text-neon-orange",children:"CONTACT"})," US"]}),(0,t.jsx)("p",{className:"text-gray-400 max-w-3xl mx-auto",children:"Have questions or feedback? We'd love to hear from you!"})]})})]}),(0,t.jsx)("div",{className:"section-divider"}),(0,t.jsx)("section",{className:"py-20 relative bg-black/50",children:(0,t.jsx)("div",{className:"container mx-auto px-4 md:px-6",children:(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-12",children:[(0,t.jsxs)("div",{className:"glass-enhanced rounded-xl p-6 border border-neon-orange/30 hover:border-neon-orange/70 transition-all duration-500 hover:shadow-neon-strong group relative pattern-circuit",children:[(0,t.jsx)("div",{className:"absolute inset-0 bg-black/20 rounded-xl"}),(0,t.jsxs)("div",{className:"relative z-10",children:[(0,t.jsx)("h2",{className:"text-2xl font-display font-bold text-white mb-6",children:"Send Us a Message"}),"success"===d&&(0,t.jsx)("div",{className:"mb-6 p-4 rounded-lg bg-green-500/20 border border-green-500/50 text-green-400",children:"Your message has been sent successfully! We'll get back to you soon."}),"error"===d&&(0,t.jsx)("div",{className:"mb-6 p-4 rounded-lg bg-red-500/20 border border-red-500/50 text-red-400",children:"There was an error sending your message. Please try again later."}),(0,t.jsxs)("form",{onSubmit:e=>{e.preventDefault(),l(!0),setTimeout(()=>{l(!1),c("success"),s({name:"",email:"",discord:"",subject:"",message:""}),setTimeout(()=>{c(null)},5e3)},1500)},children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"name",className:"block text-white mb-2",children:"Name"}),(0,t.jsx)("input",{type:"text",id:"name",name:"name",value:e.name,onChange:m,required:!0,className:"w-full bg-black/50 border border-neon-orange/30 rounded-lg px-4 py-2 text-white focus:outline-none focus:border-neon-orange transition-colors",placeholder:"Your name"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"email",className:"block text-white mb-2",children:"Email"}),(0,t.jsx)("input",{type:"email",id:"email",name:"email",value:e.email,onChange:m,required:!0,className:"w-full bg-black/50 border border-neon-orange/30 rounded-lg px-4 py-2 text-white focus:outline-none focus:border-neon-orange transition-colors",placeholder:"Your email"})]})]}),(0,t.jsxs)("div",{className:"mb-6",children:[(0,t.jsx)("label",{htmlFor:"discord",className:"block text-white mb-2",children:"Discord Username (optional)"}),(0,t.jsx)("input",{type:"text",id:"discord",name:"discord",value:e.discord,onChange:m,className:"w-full bg-black/50 border border-neon-orange/30 rounded-lg px-4 py-2 text-white focus:outline-none focus:border-neon-orange transition-colors",placeholder:"Your Discord username"})]}),(0,t.jsxs)("div",{className:"mb-6",children:[(0,t.jsx)("label",{htmlFor:"subject",className:"block text-white mb-2",children:"Subject"}),(0,t.jsxs)("select",{id:"subject",name:"subject",value:e.subject,onChange:m,required:!0,className:"w-full bg-black/50 border border-neon-orange/30 rounded-lg px-4 py-2 text-white focus:outline-none focus:border-neon-orange transition-colors",children:[(0,t.jsx)("option",{value:"",disabled:!0,children:"Select a subject"}),(0,t.jsx)("option",{value:"general",children:"General Inquiry"}),(0,t.jsx)("option",{value:"support",children:"Technical Support"}),(0,t.jsx)("option",{value:"feedback",children:"Feedback"}),(0,t.jsx)("option",{value:"report",children:"Report an Issue"}),(0,t.jsx)("option",{value:"join",children:"How to Join"})]})]}),(0,t.jsxs)("div",{className:"mb-6",children:[(0,t.jsx)("label",{htmlFor:"message",className:"block text-white mb-2",children:"Message"}),(0,t.jsx)("textarea",{id:"message",name:"message",value:e.message,onChange:m,required:!0,rows:5,className:"w-full bg-black/50 border border-neon-orange/30 rounded-lg px-4 py-2 text-white focus:outline-none focus:border-neon-orange transition-colors",placeholder:"Your message"})]}),(0,t.jsx)("button",{type:"submit",disabled:r,className:"px-6 py-3 rounded-full bg-gradient-to-r from-neon-orange to-yellow-500 text-white font-medium hover:shadow-neon transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed",children:r?"Sending...":"Send Message"})]})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"glass rounded-lg p-8 border border-neon-blue/20 mb-8",children:[(0,t.jsx)("h2",{className:"text-2xl font-display font-bold text-white mb-6",children:"Connect With Us"}),(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-start",children:[(0,t.jsx)("div",{className:"mr-4 mt-1",children:(0,t.jsx)("svg",{className:"w-6 h-6 text-neon-blue",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"})})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-white font-semibold mb-1",children:"Email"}),(0,t.jsx)("p",{className:"text-gray-400",children:"<EMAIL>"})]})]}),(0,t.jsxs)("div",{className:"flex items-start",children:[(0,t.jsx)("div",{className:"mr-4 mt-1",children:(0,t.jsx)("svg",{className:"w-6 h-6 text-neon-blue",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-white font-semibold mb-1",children:"Support Hours"}),(0,t.jsx)("p",{className:"text-gray-400",children:"Our team is available 7 days a week from 10 AM to 10 PM IST."})]})]}),(0,t.jsxs)("div",{className:"flex items-start",children:[(0,t.jsx)("div",{className:"mr-4 mt-1",children:(0,t.jsx)("svg",{className:"w-6 h-6 text-neon-blue",fill:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{d:"M20.317 4.3698a19.7913 19.7913 0 00-4.8851-1.5152.0741.0741 0 00-.0785.0371c-.211.3753-.4447.8648-.6083 1.2495-1.8447-.2762-3.68-.2762-5.4868 0-.1636-.3933-.4058-.8742-.6177-1.2495a.077.077 0 00-.0785-.037 19.7363 19.7363 0 00-4.8852 1.515.0699.0699 0 00-.0321.0277C.5334 9.0458-.319 13.5799.0992 18.0578a.0824.0824 0 00.0312.0561c2.0528 1.5076 4.0413 2.4228 5.9929 3.0294a.0777.0777 0 00.0842-.0276c.4616-.6304.8731-1.2952 1.226-1.9942a.076.076 0 00-.0416-.1057c-.6528-.2476-1.2743-.5495-1.8722-.8923a.077.077 0 01-.0076-.1277c.1258-.0943.2517-.1923.3718-.2914a.0743.0743 0 01.0776-.0105c3.9278 1.7933 8.18 1.7933 12.0614 0a.0739.0739 0 01.0785.0095c.1202.099.246.1981.3728.2924a.077.077 0 01-.0066.1276 12.2986 12.2986 0 01-1.873.8914.0766.0766 0 00-.0407.1067c.3604.698.7719 1.3628 1.225 1.9932a.076.076 0 00.0842.0286c1.961-.6067 3.9495-1.5219 6.0023-3.0294a.077.077 0 00.0313-.0552c.5004-5.177-.8382-9.6739-3.5485-13.6604a.061.061 0 00-.0312-.0286zM8.02 15.3312c-1.1825 0-2.1569-1.0857-2.1569-2.419 0-1.3332.9555-2.4189 2.157-2.4189 1.2108 0 2.1757 1.0952 2.1568 2.419 0 1.3332-.9555 2.4189-2.1569 2.4189zm7.9748 0c-1.1825 0-2.1569-1.0857-2.1569-2.419 0-1.3332.9554-2.4189 2.1569-2.4189 1.2108 0 2.1757 1.0952 2.1568 2.419 0 1.3332-.946 2.4189-2.1568 2.4189Z"})})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-white font-semibold mb-1",children:"Discord"}),(0,t.jsx)("p",{className:"text-gray-400 mb-2",children:"Join our Discord server for faster support and community interaction."}),(0,t.jsx)(i.A,{href:"#",variant:"primary",size:"sm",children:"Join Discord"})]})]})]})]}),(0,t.jsxs)("div",{className:"glass rounded-lg p-8 border border-neon-blue/20",children:[(0,t.jsx)("h2",{className:"text-2xl font-display font-bold text-white mb-6",children:"Frequently Asked Questions"}),(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-white font-semibold mb-2",children:"How do I join the server?"}),(0,t.jsx)("p",{className:"text-gray-400",children:"To join our server, first join our Discord community, read the rules, and follow the instructions in the #how-to-join channel."})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-white font-semibold mb-2",children:"Is the server whitelist only?"}),(0,t.jsx)("p",{className:"text-gray-400",children:"Yes, we have a whitelist process to ensure quality roleplay. You'll need to complete a simple application in our Discord."})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-white font-semibold mb-2",children:"Do I need to speak Telugu to join?"}),(0,t.jsx)("p",{className:"text-gray-400",children:"While our server is focused on the Telugu community, we welcome everyone who respects our community and culture. Both Telugu and English are used on the server."})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-white font-semibold mb-2",children:"How can I report a player?"}),(0,t.jsx)("p",{className:"text-gray-400",children:"You can report players through our Discord in the #report-player channel or by contacting a staff member directly."})]})]})]})]})]})})}),(0,t.jsx)(a.A,{})]})}},2149:(e,s,r)=>{"use strict";r.d(s,{A:()=>a});var t=r(5512);r(8009);var n=r(8531),o=r.n(n);let a=({children:e,href:s,variant:r="primary",size:n="md",className:a="",onClick:i})=>{let l=`inline-flex items-center justify-center font-medium transition-all duration-300 rounded-full ${{primary:"bg-gradient-to-r from-neon-orange to-neon-red text-white hover:shadow-neon",secondary:"bg-neon-orange text-white hover:bg-neon-orange/90 hover:shadow-neon-orange",outline:"border border-neon-orange text-neon-orange hover:bg-neon-orange/10",ghost:"text-white hover:text-neon-orange hover:bg-white/5"}[r]} ${{sm:"text-xs px-3 py-1.5",md:"text-sm px-4 py-2",lg:"text-base px-6 py-3"}[n]} ${a}`;return s?s.startsWith("http")||s.startsWith("https")?(0,t.jsx)("a",{href:s,className:l,target:"_blank",rel:"noopener noreferrer",children:e}):(0,t.jsx)(o(),{href:s,className:l,children:e}):(0,t.jsx)("button",{className:l,onClick:i,children:e})}},6450:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>t});let t=(0,r(6760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\TGRS Website\\\\tgrs-website\\\\src\\\\app\\\\contact\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\TGRS Website\\tgrs-website\\src\\app\\contact\\page.tsx","default")}};var s=require("../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[638,413,666,540,662,403],()=>r(628));module.exports=t})();