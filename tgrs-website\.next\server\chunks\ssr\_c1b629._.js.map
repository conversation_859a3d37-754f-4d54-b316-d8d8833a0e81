{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/TGRS%20Website/tgrs-website/src/components/Navbar.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/Navbar.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Navbar.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA6R,GAC1T,2DACA"}}, {"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 21, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/TGRS%20Website/tgrs-website/src/components/Navbar.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/Navbar.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Navbar.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAyQ,GACtS,uCACA"}}, {"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 35, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": ""}}, {"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 45, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/TGRS%20Website/tgrs-website/src/components/Footer.tsx"], "sourcesContent": ["import Link from \"next/link\";\nimport Image from \"next/image\";\n\nconst Footer = () => {\n  return (\n    <footer className=\"bg-black/50 pattern-dots-footer border-t border-neon-orange/30 pt-12 pb-6 relative\">\n      <div className=\"container mx-auto px-4 md:px-6\">\n        <div className=\"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-6 sm:gap-8 mb-6 sm:mb-8\">\n          {/* Logo and About */}\n          <div className=\"col-span-1 sm:col-span-2 md:col-span-1\">\n            <Link href=\"/\" className=\"flex items-center mb-4\">\n              <div className=\"relative h-8 w-8 sm:h-10 sm:w-10 mr-3\">\n                <Image\n                  src=\"/assets/tgrs-logo.png\"\n                  alt=\"TGRS Logo\"\n                  width={40}\n                  height={40}\n                  className=\"w-full h-full object-contain\"\n                />\n              </div>\n              <span className=\"text-white font-display font-bold text-lg sm:text-xl tracking-wider\">\n                TGRS\n              </span>\n            </Link>\n            <p className=\"text-gray-400 text-xs sm:text-sm mb-4\">\n              The premier Telugu community FiveM roleplay server with immersive experiences and unique features.\n            </p>\n            <div className=\"flex space-x-3 sm:space-x-4\">\n              {/* YouTube */}\n              <a href=\"https://www.youtube.com/@GTA5RPTGRSCITY\" target=\"_blank\" rel=\"noopener noreferrer\" className=\"text-white hover:text-red-500 transition-colors\" aria-label=\"YouTube\">\n                <svg className=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\" aria-hidden=\"true\">\n                  <path d=\"M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z\"/>\n                </svg>\n              </a>\n              {/* Discord */}\n              <a href=\"https://discord.gg/GAMravHDnB\" target=\"_blank\" rel=\"noopener noreferrer\" className=\"text-white hover:text-[#5865F2] transition-colors\" aria-label=\"Discord\">\n                <svg className=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\" aria-hidden=\"true\">\n                  <path d=\"M20.317 4.3698a19.7913 19.7913 0 00-4.8851-1.5152.0741.0741 0 00-.0785.0371c-.211.3753-.4447.8648-.6083 1.2495-1.8447-.2762-3.68-.2762-5.4868 0-.1636-.3933-.4058-.8742-.6177-1.2495a.077.077 0 00-.0785-.037 19.7363 19.7363 0 00-4.8852 1.515.0699.0699 0 00-.0321.0277C.5334 9.0458-.319 13.5799.0992 18.0578a.0824.0824 0 00.0312.0561c2.0528 1.5076 4.0413 2.4228 5.9929 3.0294a.0777.0777 0 00.0842-.0276c.4616-.6304.8731-1.2952 1.226-1.9942a.076.076 0 00-.0416-.1057c-.6528-.2476-1.2743-.5495-1.8722-.8923a.077.077 0 01-.0076-.1277c.1258-.0943.2517-.1923.3718-.2914a.0743.0743 0 01.0776-.0105c3.9278 1.7933 8.18 1.7933 12.0614 0a.0739.0739 0 01.0785.0095c.1202.099.246.1981.3728.2924a.077.077 0 01-.0066.1276 12.2986 12.2986 0 01-1.873.8914.0766.0766 0 00-.0407.1067c.3604.698.7719 1.3628 1.225 1.9932a.076.076 0 00.0842.0286c1.961-.6067 3.9495-1.5219 6.0023-3.0294a.077.077 0 00.0313-.0552c.5004-5.177-.8382-9.6739-3.5485-13.6604a.061.061 0 00-.0312-.0286zM8.02 15.3312c-1.1825 0-2.1569-1.0857-2.1569-2.419 0-1.3332.9555-2.4189 2.157-2.4189 1.2108 0 2.1757 1.0952 2.1568 2.419-.0002 1.3332-.9555 2.4189-2.1569 2.4189zm7.9748 0c-1.1825 0-2.1569-1.0857-2.1569-2.419 0-1.3332.9554-2.4189 2.1569-2.4189 1.2108 0 2.1757 1.0952 2.1568 2.419 0 1.3332-.9554 2.4189-2.1568 2.4189Z\"/>\n                </svg>\n              </a>\n              {/* Website */}\n              <a href=\"/\" className=\"text-white hover:text-neon-orange transition-colors\" aria-label=\"Website\">\n                <svg className=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\" aria-hidden=\"true\">\n                  <path d=\"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-1 17.93c-3.94-.49-7-3.85-7-7.93 0-.62.08-1.21.21-1.79L9 15v1c0 1.1.9 2 2 2v1.93zm6.9-2.54c-.26-.81-1-1.39-1.9-1.39h-1v-3c0-.55-.45-1-1-1H8v-2h2c.55 0 1-.45 1-1V7h2c1.1 0 2-.9 2-2v-.41c2.93 1.19 5 4.06 5 7.41 0 2.08-.8 3.97-2.1 5.39z\"/>\n                </svg>\n              </a>\n            </div>\n\n            {/* Developer Credits */}\n            <div className=\"mt-6 sm:mt-8\">\n              <div className=\"relative inline-block group\">\n                {/* Main content */}\n                <div className=\"relative flex items-center justify-center space-x-2 py-2 rounded-lg transition-all duration-300\">\n                  <span className=\"text-gray-400 text-xs sm:text-sm font-medium\">Made with</span>\n\n                  {/* Animated heart */}\n                  <div className=\"relative\">\n                    <span className=\"text-red-500 text-sm sm:text-base animate-pulse group-hover:animate-bounce transition-all duration-300\">\n                      ❤️\n                    </span>\n                    {/* Heart particles on hover */}\n                    <div className=\"absolute inset-0 opacity-0 group-hover:opacity-100 transition-all duration-500 pointer-events-none\">\n                      <span className=\"absolute -top-1 -left-1 text-pink-400 text-xs animate-ping\">💖</span>\n                      <span className=\"absolute -top-1 -right-1 text-red-400 text-xs animate-ping\" style={{animationDelay: '0.2s'}}>💕</span>\n                      <span className=\"absolute -bottom-1 left-0 text-pink-300 text-xs animate-ping\" style={{animationDelay: '0.4s'}}>💗</span>\n                    </div>\n                  </div>\n\n                  <span className=\"text-gray-400 text-xs sm:text-sm font-medium\">by</span>\n\n                  {/* Developer name with special styling */}\n                  <a\n                    href=\"https://discord.com/users/223121046830579715\"\n                    target=\"_blank\"\n                    rel=\"noopener noreferrer\"\n                    className=\"relative group/dev\"\n                  >\n                    {/* Background glow */}\n                    <div className=\"absolute inset-0 bg-gradient-to-r from-neon-orange/30 to-neon-red/30 rounded-md blur-sm opacity-0 group-hover/dev:opacity-100 transition-all duration-300\"></div>\n\n                    {/* Text with gradient */}\n                    <span className=\"relative bg-gradient-to-r from-neon-orange via-yellow-400 to-neon-orange bg-clip-text text-transparent font-bold text-xs sm:text-sm hover:from-yellow-300 hover:via-neon-orange hover:to-yellow-300 transition-all duration-300 cursor-pointer\">\n                      Macpie\n                    </span>\n\n                    {/* Hover indicator */}\n                    <div className=\"absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-to-r from-neon-orange to-neon-red group-hover/dev:w-full transition-all duration-300\"></div>\n\n                    {/* Discord icon on hover */}\n                    <div className=\"absolute -top-6 left-1/2 transform -translate-x-1/2 opacity-0 group-hover/dev:opacity-100 transition-all duration-300 pointer-events-none\">\n                      <div className=\"bg-[#5865F2] text-white px-2 py-1 rounded text-xs whitespace-nowrap flex items-center space-x-1\">\n                        <svg className=\"w-3 h-3\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                          <path d=\"M20.317 4.3698a19.7913 19.7913 0 00-4.8851-1.5152.0741.0741 0 00-.0785.0371c-.211.3753-.4447.8648-.6083 1.2495-1.8447-.2762-3.68-.2762-5.4868 0-.1636-.3933-.4058-.8742-.6177-1.2495a.077.077 0 00-.0785-.037 19.7363 19.7363 0 00-4.8852 1.515.0699.0699 0 00-.0321.0277C.5334 9.0458-.319 13.5799.0992 18.0578a.0824.0824 0 00.0312.0561c2.0528 1.5076 4.0413 2.4228 5.9929 3.0294a.0777.0777 0 00.0842-.0276c.4616-.6304.8731-1.2952 1.226-1.9942a.076.076 0 00-.0416-.1057c-.6528-.2476-1.2743-.5495-1.8722-.8923a.077.077 0 01-.0076-.1277c.1258-.0943.2517-.1923.3718-.2914a.0743.0743 0 01.0776-.0105c3.9278 1.7933 8.18 1.7933 12.0614 0a.0739.0739 0 01.0785.0095c.1202.099.246.1981.3728.2924a.077.077 0 01-.0066.1276 12.2986 12.2986 0 01-1.873.8914.0766.0766 0 00-.0407.1067c.3604.698.7719 1.3628 1.225 1.9932a.076.076 0 00.0842.0286c1.961-.6067 3.9495-1.5219 6.0023-3.0294a.077.077 0 00.0313-.0552c.5004-5.177-.8382-9.6739-3.5485-13.6604a.061.061 0 00-.0312-.0286zM8.02 15.3312c-1.1825 0-2.1569-1.0857-2.1569-2.419 0-1.3332.9555-2.4189 2.157-2.4189 1.2108 0 2.1757 1.0952 2.1568 2.419 0 1.3332-.9555 2.4189-2.1569 2.4189zm7.9748 0c-1.1825 0-2.1569-1.0857-2.1569-2.419 0-1.3332.9554-2.4189 2.1569-2.4189 1.2108 0 2.1757 1.0952 2.1568 2.419 0 1.3332-.946 2.4189-2.1568 2.4189Z\"/>\n                        </svg>\n                        <span>Add Friend</span>\n                      </div>\n                      {/* Tooltip arrow */}\n                      <div className=\"absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-2 border-r-2 border-t-2 border-transparent border-t-[#5865F2]\"></div>\n                    </div>\n                  </a>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Quick Links */}\n          <div className=\"col-span-1\">\n            <h3 className=\"text-white font-display font-semibold text-base sm:text-lg mb-3 sm:mb-4\">Quick Links</h3>\n            <ul className=\"space-y-1 sm:space-y-2\">\n              <li>\n                <Link href=\"/\" className=\"text-gray-400 hover:text-neon-orange transition-colors text-xs sm:text-sm\">\n                  Home\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/about\" className=\"text-gray-400 hover:text-neon-orange transition-colors text-xs sm:text-sm\">\n                  About\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/features\" className=\"text-gray-400 hover:text-neon-orange transition-colors text-xs sm:text-sm\">\n                  Features\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/rules\" className=\"text-gray-400 hover:text-neon-orange transition-colors text-xs sm:text-sm\">\n                  Rules\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/contact\" className=\"text-gray-400 hover:text-neon-orange transition-colors text-xs sm:text-sm\">\n                  Contact\n                </Link>\n              </li>\n            </ul>\n          </div>\n\n          {/* Server Info */}\n          <div className=\"col-span-1\">\n            <h3 className=\"text-white font-display font-semibold text-base sm:text-lg mb-3 sm:mb-4\">Server Info</h3>\n            <ul className=\"space-y-1 sm:space-y-2\">\n              <li className=\"text-gray-400 text-xs sm:text-sm\">\n                <span className=\"text-neon-orange mr-2\">•</span> 24/7 Uptime\n              </li>\n              <li className=\"text-gray-400 text-xs sm:text-sm\">\n                <span className=\"text-neon-orange mr-2\">•</span> Active Staff\n              </li>\n              <li className=\"text-gray-400 text-xs sm:text-sm\">\n                <span className=\"text-neon-orange mr-2\">•</span> Custom Scripts\n              </li>\n              <li className=\"text-gray-400 text-xs sm:text-sm\">\n                <span className=\"text-neon-orange mr-2\">•</span> Regular Updates\n              </li>\n              <li className=\"text-gray-400 text-xs sm:text-sm\">\n                <span className=\"text-neon-orange mr-2\">•</span> Community Events\n              </li>\n            </ul>\n          </div>\n\n          {/* Discord */}\n          <div className=\"col-span-1 sm:col-span-2 md:col-span-1\">\n            <h3 className=\"text-white font-display font-semibold text-base sm:text-lg mb-3 sm:mb-4\">Join Our Discord</h3>\n            <p className=\"text-gray-400 text-xs sm:text-sm mb-3 sm:mb-4\">\n              Join our Discord community for server updates, events, and to connect with other players.\n            </p>\n            <a\n              href=\"https://discord.gg/GAMravHDnB\"\n              target=\"_blank\"\n              rel=\"noopener noreferrer\"\n              className=\"px-3 py-2 sm:px-4 sm:py-2 rounded-md bg-[#5865F2] hover:bg-[#4752C4] text-white font-medium transition-colors flex items-center justify-center space-x-2 w-full sm:w-auto text-xs sm:text-sm\"\n            >\n              <svg className=\"w-4 h-4 sm:w-5 sm:h-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path d=\"M20.317 4.3698a19.7913 19.7913 0 00-4.8851-1.5152.0741.0741 0 00-.0785.0371c-.211.3753-.4447.8648-.6083 1.2495-1.8447-.2762-3.68-.2762-5.4868 0-.1636-.3933-.4058-.8742-.6177-1.2495a.077.077 0 00-.0785-.037 19.7363 19.7363 0 00-4.8852 1.515.0699.0699 0 00-.0321.0277C.5334 9.0458-.319 13.5799.0992 18.0578a.0824.0824 0 00.0312.0561c2.0528 1.5076 4.0413 2.4228 5.9929 3.0294a.0777.0777 0 00.0842-.0276c.4616-.6304.8731-1.2952 1.226-1.9942a.076.076 0 00-.0416-.1057c-.6528-.2476-1.2743-.5495-1.8722-.8923a.077.077 0 01-.0076-.1277c.1258-.0943.2517-.1923.3718-.2914a.0743.0743 0 01.0776-.0105c3.9278 1.7933 8.18 1.7933 12.0614 0a.0739.0739 0 01.0785.0095c.1202.099.246.1981.3728.2924a.077.077 0 01-.0066.1276 12.2986 12.2986 0 01-1.873.8914.0766.0766 0 00-.0407.1067c.3604.698.7719 1.3628 1.225 1.9932a.076.076 0 00.0842.0286c1.961-.6067 3.9495-1.5219 6.0023-3.0294a.077.077 0 00.0313-.0552c.5004-5.177-.8382-9.6739-3.5485-13.6604a.061.061 0 00-.0312-.0286zM8.02 15.3312c-1.1825 0-2.1569-1.0857-2.1569-2.419 0-1.3332.9555-2.4189 2.157-2.4189 1.2108 0 2.1757 1.0952 2.1568 2.419 0 1.3332-.9555 2.4189-2.1569 2.4189zm7.9748 0c-1.1825 0-2.1569-1.0857-2.1569-2.419 0-1.3332.9554-2.4189 2.1569-2.4189 1.2108 0 2.1757 1.0952 2.1568 2.419 0 1.3332-.946 2.4189-2.1568 2.4189Z\"></path>\n              </svg>\n              <span>Join Discord</span>\n            </a>\n          </div>\n        </div>\n\n        {/* Copyright */}\n        <div className=\"border-t border-gray-800 pt-4 sm:pt-6 mt-4 sm:mt-6 text-center\">\n          <p className=\"text-gray-500 text-xs sm:text-sm\">\n            &copy; {new Date().getFullYear()} TGRS - Telugu Gaming Roleplay Server. All rights reserved.\n          </p>\n        </div>\n\n\n      </div>\n    </footer>\n  );\n};\n\nexport default Footer;\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAEA,MAAM,SAAS;IACb,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAI,WAAU;;sDACvB,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;gDACJ,KAAI;gDACJ,KAAI;gDACJ,OAAO;gDACP,QAAQ;gDACR,WAAU;;;;;;;;;;;sDAGd,8OAAC;4CAAK,WAAU;sDAAsE;;;;;;;;;;;;8CAIxF,8OAAC;oCAAE,WAAU;8CAAwC;;;;;;8CAGrD,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC;4CAAE,MAAK;4CAA0C,QAAO;4CAAS,KAAI;4CAAsB,WAAU;4CAAkD,cAAW;sDACjK,cAAA,8OAAC;gDAAI,WAAU;gDAAU,MAAK;gDAAe,SAAQ;gDAAY,eAAY;0DAC3E,cAAA,8OAAC;oDAAK,GAAE;;;;;;;;;;;;;;;;sDAIZ,8OAAC;4CAAE,MAAK;4CAAgC,QAAO;4CAAS,KAAI;4CAAsB,WAAU;4CAAoD,cAAW;sDACzJ,cAAA,8OAAC;gDAAI,WAAU;gDAAU,MAAK;gDAAe,SAAQ;gDAAY,eAAY;0DAC3E,cAAA,8OAAC;oDAAK,GAAE;;;;;;;;;;;;;;;;sDAIZ,8OAAC;4CAAE,MAAK;4CAAI,WAAU;4CAAsD,cAAW;sDACrF,cAAA,8OAAC;gDAAI,WAAU;gDAAU,MAAK;gDAAe,SAAQ;gDAAY,eAAY;0DAC3E,cAAA,8OAAC;oDAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;8CAMd,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;kDAEb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAA+C;;;;;;8DAG/D,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAyG;;;;;;sEAIzH,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAK,WAAU;8EAA6D;;;;;;8EAC7E,8OAAC;oEAAK,WAAU;oEAA6D,OAAO;wEAAC,gBAAgB;oEAAM;8EAAG;;;;;;8EAC9G,8OAAC;oEAAK,WAAU;oEAA+D,OAAO;wEAAC,gBAAgB;oEAAM;8EAAG;;;;;;;;;;;;;;;;;;8DAIpH,8OAAC;oDAAK,WAAU;8DAA+C;;;;;;8DAG/D,8OAAC;oDACC,MAAK;oDACL,QAAO;oDACP,KAAI;oDACJ,WAAU;;sEAGV,8OAAC;4DAAI,WAAU;;;;;;sEAGf,8OAAC;4DAAK,WAAU;sEAAiP;;;;;;sEAKjQ,8OAAC;4DAAI,WAAU;;;;;;sEAGf,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAI,WAAU;4EAAU,MAAK;4EAAe,SAAQ;sFACnD,cAAA,8OAAC;gFAAK,GAAE;;;;;;;;;;;sFAEV,8OAAC;sFAAK;;;;;;;;;;;;8EAGR,8OAAC;oEAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAS3B,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA0E;;;;;;8CACxF,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAI,WAAU;0DAA4E;;;;;;;;;;;sDAIvG,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAS,WAAU;0DAA4E;;;;;;;;;;;sDAI5G,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAY,WAAU;0DAA4E;;;;;;;;;;;sDAI/G,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAS,WAAU;0DAA4E;;;;;;;;;;;sDAI5G,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;0DAA4E;;;;;;;;;;;;;;;;;;;;;;;sCAQlH,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA0E;;;;;;8CACxF,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAK,WAAU;8DAAwB;;;;;;gDAAQ;;;;;;;sDAElD,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAK,WAAU;8DAAwB;;;;;;gDAAQ;;;;;;;sDAElD,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAK,WAAU;8DAAwB;;;;;;gDAAQ;;;;;;;sDAElD,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAK,WAAU;8DAAwB;;;;;;gDAAQ;;;;;;;sDAElD,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAK,WAAU;8DAAwB;;;;;;gDAAQ;;;;;;;;;;;;;;;;;;;sCAMtD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA0E;;;;;;8CACxF,8OAAC;oCAAE,WAAU;8CAAgD;;;;;;8CAG7D,8OAAC;oCACC,MAAK;oCACL,QAAO;oCACP,KAAI;oCACJ,WAAU;;sDAEV,8OAAC;4CAAI,WAAU;4CAAwB,MAAK;4CAAe,SAAQ;sDACjE,cAAA,8OAAC;gDAAK,GAAE;;;;;;;;;;;sDAEV,8OAAC;sDAAK;;;;;;;;;;;;;;;;;;;;;;;;8BAMZ,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAE,WAAU;;4BAAmC;4BACtC,IAAI,OAAO,WAAW;4BAAG;;;;;;;;;;;;;;;;;;;;;;;AAQ7C;uCAEe"}}, {"offset": {"line": 699, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 705, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/TGRS%20Website/tgrs-website/src/app/about/page.tsx"], "sourcesContent": ["import React from \"react\";\nimport Navbar from \"@/components/Navbar\";\nimport Footer from \"@/components/Footer\";\nimport Image from \"next/image\";\n\nexport default function About() {\n  return (\n    <>\n      <Navbar />\n\n      {/* Header */}\n      <div className=\"pt-24 pb-12 md:pt-32 md:pb-20 relative bg-black\">\n        <div className=\"absolute inset-0 pattern-circuit opacity-5\"></div>\n        <div className=\"container mx-auto px-4 md:px-6 relative z-10\">\n          <div className=\"text-center\">\n            <h1 className=\"text-4xl md:text-5xl font-display font-bold text-white mb-6 wave-heading-bg\">\n              <span className=\"text-neon-orange\">ABOUT</span> TGRS\n            </h1>\n            <p className=\"text-gray-400 max-w-3xl mx-auto\">\n              Learn more about the Telugu Gaming Roleplay Server, our mission, and the team behind it.\n            </p>\n          </div>\n        </div>\n      </div>\n\n      {/* Section Divider */}\n      <div className=\"section-divider\"></div>\n\n      {/* About Section */}\n      <section className=\"py-20 relative bg-black/50\">\n        <div className=\"container mx-auto px-4 md:px-6\">\n          <div className=\"glass rounded-lg p-8 border border-neon-orange/20 mb-12\">\n            <div className=\"flex flex-col md:flex-row items-center gap-8\">\n              <div className=\"md:w-1/3 flex justify-center\">\n                <div className=\"relative w-32 h-32 md:w-40 md:h-40\">\n                  <Image\n                    src=\"/assets/tgrs-logo.png\"\n                    alt=\"TGRS Logo\"\n                    fill\n                    className=\"object-contain\"\n                  />\n                </div>\n              </div>\n              <div className=\"md:w-2/3\">\n                <h2 className=\"text-2xl md:text-3xl font-display font-bold text-white mb-4\">\n                  Our Story\n                </h2>\n                <p className=\"text-gray-400 mb-4\">\n                  TGRS was founded with a vision to create a unique and immersive roleplay experience specifically for the Telugu gaming community. What started as a small project has grown into a vibrant community of passionate roleplayers.\n                </p>\n                <p className=\"text-gray-400 mb-4\">\n                  Our server is built on the principles of creativity, respect, and fun. We believe in creating an environment where players can express themselves through roleplay while building lasting friendships.\n                </p>\n                <p className=\"text-gray-400\">\n                  With custom scripts, unique features, and a dedicated team, TGRS offers an unparalleled gaming experience that celebrates Telugu culture and community.\n                </p>\n              </div>\n            </div>\n          </div>\n\n          {/* Mission & Values */}\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-8 mb-16\">\n            <div className=\"glass-enhanced rounded-xl p-4 border border-neon-orange/30 hover:border-neon-orange/70 transition-all duration-500 hover:shadow-neon-strong group relative h-full flex flex-col pattern-circuit\">\n              <div className=\"absolute inset-0 bg-black/20 rounded-xl\"></div>\n\n              <div className=\"relative z-10 flex flex-col h-full\">\n                <div className=\"mb-3 text-neon-orange group-hover:text-white transition-all duration-300 relative icon-container\">\n                  <div className=\"w-6 h-6 md:w-8 md:h-8 relative transform group-hover:scale-105 transition-transform duration-300 origin-center\">\n                    <div className=\"absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-500 pointer-events-none\">\n                      {/* Top particles */}\n                      <div className=\"absolute w-1 h-1 bg-neon-orange rounded-full animate-ping\" style={{top: '-4px', left: '20%', animationDelay: '0s'}}></div>\n                      <div className=\"absolute w-0.5 h-0.5 bg-yellow-400 rounded-full animate-pulse\" style={{top: '-2px', right: '25%', animationDelay: '0.4s'}}></div>\n\n                      {/* Right particles */}\n                      <div className=\"absolute w-1 h-1 bg-orange-400 rounded-full animate-ping\" style={{right: '-4px', top: '30%', animationDelay: '0.2s'}}></div>\n                      <div className=\"absolute w-0.5 h-0.5 bg-yellow-300 rounded-full animate-pulse\" style={{right: '-2px', bottom: '35%', animationDelay: '0.7s'}}></div>\n\n                      {/* Bottom particles */}\n                      <div className=\"absolute w-1 h-1 bg-neon-orange rounded-full animate-ping\" style={{bottom: '-4px', right: '20%', animationDelay: '0.5s'}}></div>\n                      <div className=\"absolute w-0.5 h-0.5 bg-orange-500 rounded-full animate-pulse\" style={{bottom: '-2px', left: '30%', animationDelay: '0.9s'}}></div>\n\n                      {/* Left particles */}\n                      <div className=\"absolute w-1 h-1 bg-yellow-400 rounded-full animate-ping\" style={{left: '-4px', bottom: '25%', animationDelay: '0.3s'}}></div>\n                      <div className=\"absolute w-0.5 h-0.5 bg-neon-orange rounded-full animate-pulse\" style={{left: '-2px', top: '40%', animationDelay: '0.6s'}}></div>\n                    </div>\n                    <svg className=\"w-full h-full relative z-10\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                      <path d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"/>\n                    </svg>\n                  </div>\n                </div>\n                <h2 className=\"text-base md:text-lg font-display font-bold text-white mb-2 group-hover:text-neon-orange transition-colors duration-300\">\n                  Our Mission\n                </h2>\n                <p className=\"text-xs md:text-sm text-gray-400 group-hover:text-gray-200 transition-colors duration-300 leading-relaxed flex-grow\">\n                  To create the premier roleplay platform for the Telugu gaming community, fostering creativity, immersion, and positive social connections. We strive to continuously improve and innovate.\n                </p>\n              </div>\n\n              <div className=\"absolute top-2 right-2 w-3 h-3 border-t-2 border-r-2 border-neon-orange/30 group-hover:border-neon-orange transition-colors duration-300 z-20\"></div>\n              <div className=\"absolute bottom-2 left-2 w-3 h-3 border-b-2 border-l-2 border-neon-orange/30 group-hover:border-neon-orange transition-colors duration-300 z-20\"></div>\n            </div>\n\n            <div className=\"glass-enhanced rounded-xl p-4 border border-neon-orange/30 hover:border-neon-orange/70 transition-all duration-500 hover:shadow-neon-strong group relative h-full flex flex-col pattern-hexagon\">\n              <div className=\"absolute inset-0 bg-black/20 rounded-xl\"></div>\n\n              <div className=\"relative z-10 flex flex-col h-full\">\n                <div className=\"mb-3 text-neon-orange group-hover:text-white transition-all duration-300 relative icon-container\">\n                  <div className=\"w-6 h-6 md:w-8 md:h-8 relative transform group-hover:scale-105 transition-transform duration-300 origin-center\">\n                    <div className=\"absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-500 pointer-events-none\">\n                      {/* Top particles */}\n                      <div className=\"absolute w-1 h-1 bg-neon-orange rounded-full animate-ping\" style={{top: '-4px', left: '20%', animationDelay: '0s'}}></div>\n                      <div className=\"absolute w-0.5 h-0.5 bg-yellow-400 rounded-full animate-pulse\" style={{top: '-2px', right: '25%', animationDelay: '0.4s'}}></div>\n\n                      {/* Right particles */}\n                      <div className=\"absolute w-1 h-1 bg-orange-400 rounded-full animate-ping\" style={{right: '-4px', top: '30%', animationDelay: '0.2s'}}></div>\n                      <div className=\"absolute w-0.5 h-0.5 bg-yellow-300 rounded-full animate-pulse\" style={{right: '-2px', bottom: '35%', animationDelay: '0.7s'}}></div>\n\n                      {/* Bottom particles */}\n                      <div className=\"absolute w-1 h-1 bg-neon-orange rounded-full animate-ping\" style={{bottom: '-4px', right: '20%', animationDelay: '0.5s'}}></div>\n                      <div className=\"absolute w-0.5 h-0.5 bg-orange-500 rounded-full animate-pulse\" style={{bottom: '-2px', left: '30%', animationDelay: '0.9s'}}></div>\n\n                      {/* Left particles */}\n                      <div className=\"absolute w-1 h-1 bg-yellow-400 rounded-full animate-ping\" style={{left: '-4px', bottom: '25%', animationDelay: '0.3s'}}></div>\n                      <div className=\"absolute w-0.5 h-0.5 bg-neon-orange rounded-full animate-pulse\" style={{left: '-2px', top: '40%', animationDelay: '0.6s'}}></div>\n                    </div>\n                    <svg className=\"w-full h-full relative z-10\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                      <path d=\"M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z\"/>\n                    </svg>\n                  </div>\n                </div>\n                <h2 className=\"text-base md:text-lg font-display font-bold text-white mb-2 group-hover:text-neon-orange transition-colors duration-300\">\n                  Our Values\n                </h2>\n                <div className=\"text-xs md:text-sm text-gray-400 group-hover:text-gray-200 transition-colors duration-300 leading-relaxed flex-grow space-y-1\">\n                  <div>• Community & Creativity</div>\n                  <div>• Quality & Respect</div>\n                  <div>• Fun & Innovation</div>\n                  <div>• Telugu Culture</div>\n                </div>\n              </div>\n\n              <div className=\"absolute top-2 right-2 w-3 h-3 border-t-2 border-r-2 border-neon-orange/30 group-hover:border-neon-orange transition-colors duration-300 z-20\"></div>\n              <div className=\"absolute bottom-2 left-2 w-3 h-3 border-b-2 border-l-2 border-neon-orange/30 group-hover:border-neon-orange transition-colors duration-300 z-20\"></div>\n            </div>\n          </div>\n\n\n        </div>\n      </section>\n\n      <Footer />\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;;;;;AAEe,SAAS;IACtB,qBACE;;0BACE,8OAAC,4HAAA,CAAA,UAAM;;;;;0BAGP,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;4CAAK,WAAU;sDAAmB;;;;;;wCAAY;;;;;;;8CAEjD,8OAAC;oCAAE,WAAU;8CAAkC;;;;;;;;;;;;;;;;;;;;;;;0BAQrD,8OAAC;gBAAI,WAAU;;;;;;0BAGf,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;gDACJ,KAAI;gDACJ,KAAI;gDACJ,IAAI;gDACJ,WAAU;;;;;;;;;;;;;;;;kDAIhB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAA8D;;;;;;0DAG5E,8OAAC;gDAAE,WAAU;0DAAqB;;;;;;0DAGlC,8OAAC;gDAAE,WAAU;0DAAqB;;;;;;0DAGlC,8OAAC;gDAAE,WAAU;0DAAgB;;;;;;;;;;;;;;;;;;;;;;;sCAQnC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDAEf,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFAEb,8OAAC;wEAAI,WAAU;wEAA4D,OAAO;4EAAC,KAAK;4EAAQ,MAAM;4EAAO,gBAAgB;wEAAI;;;;;;kFACjI,8OAAC;wEAAI,WAAU;wEAAgE,OAAO;4EAAC,KAAK;4EAAQ,OAAO;4EAAO,gBAAgB;wEAAM;;;;;;kFAGxI,8OAAC;wEAAI,WAAU;wEAA2D,OAAO;4EAAC,OAAO;4EAAQ,KAAK;4EAAO,gBAAgB;wEAAM;;;;;;kFACnI,8OAAC;wEAAI,WAAU;wEAAgE,OAAO;4EAAC,OAAO;4EAAQ,QAAQ;4EAAO,gBAAgB;wEAAM;;;;;;kFAG3I,8OAAC;wEAAI,WAAU;wEAA4D,OAAO;4EAAC,QAAQ;4EAAQ,OAAO;4EAAO,gBAAgB;wEAAM;;;;;;kFACvI,8OAAC;wEAAI,WAAU;wEAAgE,OAAO;4EAAC,QAAQ;4EAAQ,MAAM;4EAAO,gBAAgB;wEAAM;;;;;;kFAG1I,8OAAC;wEAAI,WAAU;wEAA2D,OAAO;4EAAC,MAAM;4EAAQ,QAAQ;4EAAO,gBAAgB;wEAAM;;;;;;kFACrI,8OAAC;wEAAI,WAAU;wEAAiE,OAAO;4EAAC,MAAM;4EAAQ,KAAK;4EAAO,gBAAgB;wEAAM;;;;;;;;;;;;0EAE1I,8OAAC;gEAAI,WAAU;gEAA8B,MAAK;gEAAe,SAAQ;0EACvE,cAAA,8OAAC;oEAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;8DAId,8OAAC;oDAAG,WAAU;8DAA0H;;;;;;8DAGxI,8OAAC;oDAAE,WAAU;8DAAsH;;;;;;;;;;;;sDAKrI,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;;;;;;;;;;;;8CAGjB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDAEf,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFAEb,8OAAC;wEAAI,WAAU;wEAA4D,OAAO;4EAAC,KAAK;4EAAQ,MAAM;4EAAO,gBAAgB;wEAAI;;;;;;kFACjI,8OAAC;wEAAI,WAAU;wEAAgE,OAAO;4EAAC,KAAK;4EAAQ,OAAO;4EAAO,gBAAgB;wEAAM;;;;;;kFAGxI,8OAAC;wEAAI,WAAU;wEAA2D,OAAO;4EAAC,OAAO;4EAAQ,KAAK;4EAAO,gBAAgB;wEAAM;;;;;;kFACnI,8OAAC;wEAAI,WAAU;wEAAgE,OAAO;4EAAC,OAAO;4EAAQ,QAAQ;4EAAO,gBAAgB;wEAAM;;;;;;kFAG3I,8OAAC;wEAAI,WAAU;wEAA4D,OAAO;4EAAC,QAAQ;4EAAQ,OAAO;4EAAO,gBAAgB;wEAAM;;;;;;kFACvI,8OAAC;wEAAI,WAAU;wEAAgE,OAAO;4EAAC,QAAQ;4EAAQ,MAAM;4EAAO,gBAAgB;wEAAM;;;;;;kFAG1I,8OAAC;wEAAI,WAAU;wEAA2D,OAAO;4EAAC,MAAM;4EAAQ,QAAQ;4EAAO,gBAAgB;wEAAM;;;;;;kFACrI,8OAAC;wEAAI,WAAU;wEAAiE,OAAO;4EAAC,MAAM;4EAAQ,KAAK;4EAAO,gBAAgB;wEAAM;;;;;;;;;;;;0EAE1I,8OAAC;gEAAI,WAAU;gEAA8B,MAAK;gEAAe,SAAQ;0EACvE,cAAA,8OAAC;oEAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;8DAId,8OAAC;oDAAG,WAAU;8DAA0H;;;;;;8DAGxI,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;sEAAI;;;;;;sEACL,8OAAC;sEAAI;;;;;;sEACL,8OAAC;sEAAI;;;;;;sEACL,8OAAC;sEAAI;;;;;;;;;;;;;;;;;;sDAIT,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQvB,8OAAC,4HAAA,CAAA,UAAM;;;;;;;AAGb"}}, {"offset": {"line": 1316, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1327, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": ""}}, {"offset": {"line": 1327, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}