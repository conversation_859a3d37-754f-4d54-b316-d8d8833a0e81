(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{8491:(t,e,r)=>{Promise.resolve().then(r.t.bind(r,8173,23)),Promise.resolve().then(r.t.bind(r,7970,23)),Promise.resolve().then(r.bind(r,8188)),Promise.resolve().then(r.bind(r,7282))},8188:(t,e,r)=>{"use strict";r.d(e,{default:()=>i});var a=r(5155),o=r(2115);let i=()=>{let t=(0,o.useRef)(null),e=(0,o.useRef)(void 0),r=(0,o.useRef)([]);return(0,o.useEffect)(()=>{let a=t.current;if(!a)return;let o=a.getContext("2d");if(!o)return;let i=()=>{a.width=a.offsetWidth,a.height=a.offsetHeight};i(),window.addEventListener("resize",i),(()=>{let t=[],e=["rgba(212, 175, 55, 0.6)","rgba(205, 133, 63, 0.5)","rgba(255, 215, 0, 0.4)","rgba(184, 134, 11, 0.5)","rgba(251, 191, 36, 0.4)","rgba(255, 193, 7, 0.5)","rgba(218, 165, 32, 0.4)","rgba(255, 140, 0, 0.3)","rgba(255, 165, 0, 0.4)"],o=["rgba(255, 215, 0, 0.8)","rgba(255, 193, 7, 0.8)","rgba(255, 140, 0, 0.7)"];["T","G","R","S","T","G","R","S"].forEach((e,r)=>{t.push({id:r,x:Math.random()*(a.width-100)+50,y:Math.random()*(a.height-100)+50,vx:(Math.random()-.5)*1.2,vy:(Math.random()-.5)*1.2,radius:22,color:o[Math.floor(Math.random()*o.length)],type:"letter",letter:e,rotation:Math.random()*Math.PI*2,rotationSpeed:(Math.random()-.5)*.02})});for(let r=8;r<16;r++){let o=["circle","triangle"][Math.floor(2*Math.random())];t.push({id:r,x:Math.random()*(a.width-60)+30,y:Math.random()*(a.height-60)+30,vx:(Math.random()-.5)*2,vy:(Math.random()-.5)*2,radius:10*Math.random()+5,color:e[Math.floor(Math.random()*e.length)],type:o,rotation:Math.random()*Math.PI*2,rotationSpeed:(Math.random()-.5)*.03})}r.current=t})();let n=(t,e)=>{let r=t.x-e.x,a=t.y-e.y;return Math.sqrt(r*r+a*a)<t.radius+e.radius},l=(t,e)=>{let r=t.x-e.x,a=t.y-e.y,o=Math.sqrt(r*r+a*a);if(0===o)return;let i=r/o,n=a/o,l=(t.vx-e.vx)*i+(t.vy-e.vy)*n;if(l>0)return;let s=2*l/2;t.vx-=s*i,t.vy-=s*n,e.vx+=s*i,e.vy+=s*n,t.rotationSpeed+=(Math.random()-.5)*.02,e.rotationSpeed+=(Math.random()-.5)*.02;let d=t.radius+e.radius-o,h=d/2*i,u=d/2*n;t.x+=h,t.y+=u,e.x-=h,e.y-=u},s=(t,e)=>{switch(t.fillStyle=e.color,t.strokeStyle=e.color.replace(/0\.\d+/,"0.8"),t.lineWidth=1,e.type){case"circle":t.beginPath(),t.arc(e.x,e.y,e.radius,0,2*Math.PI),t.fill(),t.stroke();break;case"triangle":t.save(),t.translate(e.x,e.y),t.rotate(e.rotation),t.beginPath(),t.moveTo(0,-e.radius),t.lineTo(-(.866*e.radius),.5*e.radius),t.lineTo(.866*e.radius,.5*e.radius),t.closePath(),t.fill(),t.stroke(),t.restore();break;case"letter":t.save(),t.translate(e.x,e.y),t.rotate(e.rotation),t.shadowColor=e.color,t.shadowBlur=8,t.shadowOffsetX=0,t.shadowOffsetY=0,t.font="bold ".concat(1.3*e.radius,"px Arial"),t.textAlign="center",t.textBaseline="middle",t.fillStyle=e.color,t.strokeStyle="rgba(255, 255, 255, 0.3)",t.lineWidth=1,t.fillText(e.letter||"",0,0),t.strokeText(e.letter||"",0,0),t.shadowBlur=0,t.restore()}},d=()=>{o.clearRect(0,0,a.width,a.height);let t=r.current;t.forEach(t=>{t.x+=t.vx,t.y+=t.vy,t.rotation+=t.rotationSpeed,(t.x-t.radius<=0||t.x+t.radius>=a.width)&&(t.vx*=-.98,t.x=Math.max(t.radius,Math.min(a.width-t.radius,t.x)),t.rotationSpeed+=(Math.random()-.5)*.015),(t.y-t.radius<=0||t.y+t.radius>=a.height)&&(t.vy*=-.98,t.y=Math.max(t.radius,Math.min(a.height-t.radius,t.y)),t.rotationSpeed+=(Math.random()-.5)*.015),t.vx*=.9995,t.vy*=.9995;let e=Math.sqrt(t.vx*t.vx+t.vy*t.vy);if(e>3)t.vx=t.vx/e*3,t.vy=t.vy/e*3;else if(e<.5&&e>0){let r=.5/e;t.vx*=r,t.vy*=r}else 0===e&&(t.vx=(Math.random()-.5)*2,t.vy=(Math.random()-.5)*2)});for(let e=0;e<t.length;e++)for(let r=e+1;r<t.length;r++)n(t[e],t[r])&&l(t[e],t[r]);t.forEach(t=>s(o,t)),e.current=requestAnimationFrame(d)};return d(),()=>{window.removeEventListener("resize",i),e.current&&cancelAnimationFrame(e.current)}},[]),(0,a.jsx)("canvas",{ref:t,className:"absolute inset-0 w-full h-full pointer-events-none",style:{zIndex:1}})}}},t=>{var e=e=>t(t.s=e);t.O(0,[992,282,441,517,358],()=>e(8491)),_N_E=t.O()}]);