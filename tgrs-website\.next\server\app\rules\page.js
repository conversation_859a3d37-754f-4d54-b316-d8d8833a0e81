(()=>{var e={};e.id=670,e.ids=[670],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},9551:e=>{"use strict";e.exports=require("url")},7090:(e,t,n)=>{"use strict";n.r(t),n.d(t,{GlobalError:()=>r.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>h,tree:()=>d});var o=n(260),i=n(8203),a=n(5155),r=n.n(a),s=n(7292),l={};for(let e in s)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>s[e]);n.d(t,l);let d=["",{children:["rules",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(n.bind(n,2425)),"C:\\Users\\<USER>\\Desktop\\TGRS Website\\tgrs-website\\src\\app\\rules\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(n.bind(n,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(n.bind(n,1354)),"C:\\Users\\<USER>\\Desktop\\TGRS Website\\tgrs-website\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(n.t.bind(n,9937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(n.t.bind(n,9116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(n.t.bind(n,1485,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(n.bind(n,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["C:\\Users\\<USER>\\Desktop\\TGRS Website\\tgrs-website\\src\\app\\rules\\page.tsx"],u={require:n,loadChunk:()=>Promise.resolve()},h=new o.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/rules/page",pathname:"/rules",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},5068:(e,t,n)=>{Promise.resolve().then(n.bind(n,2425))},4810:(e,t,n)=>{Promise.resolve().then(n.bind(n,9573))},9573:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>l});var o=n(5512),i=n(8009),a=n(6519),r=n(8784);let s=[{id:"server-rules",name:"Server Rules",rules:[{id:"inappropriate-language",title:"Inappropriate Language",content:"Don't use 18+ words in city situations & Server meetings."},{id:"exploits",title:"Exploits and Glitches",content:"Do not use glitches of the game to your advantage or use exploits. If you happen to find so, make sure you report it on the discord."},{id:"respect",title:"Respect Others",content:"Respect others at all times. Racial slurs, hate comments, rape references and sexual harassment are not allowed."},{id:"cultural-respect",title:"Cultural Respect",content:"Under no circumstances, you are not allowed to speak about others Religion, Culture or Country, you must respect each other."},{id:"discriminatory-rp",title:"Discriminatory RP",content:"You are not allowed to have role-play which can be deemed to have racist, slavery or homophobic acts."},{id:"rule-encouragement",title:"Rule Encouragement",content:"Encouraging or forcing other members of the community to break rules is strictly prohibited and will be punished with a Temp/permanent ban."},{id:"ban-evasion",title:"Ban Evasion",content:"After being banned, you may not play on other accounts to get around the ban or attempt anything that might trick staff into thinking you're a different person."},{id:"griefing",title:"Griefing",content:"Do not grief others. Griefing is the act of intentionally angering or irritating another player with malicious intent. Example: Consistently stalking a player to force RP or ruining their RP experiences."},{id:"ooc-targeting",title:"OOC Targeting",content:"Players must not target, harass, or take any adverse actions against another player out-of-character (OOC) due to in-character (IC) actions or roleplay scenarios. All interactions that arise within roleplay must remain strictly within the context of the game."},{id:"safe-zone-crimes",title:"Safe Zone Crimes",content:"You are not allowed to do any criminal activity such as Hotwiring/Illegal Dealings and many more in Safe Zones until and unless any situation is going on."},{id:"toxicity",title:"Toxicity",content:"Without any proper reason, Toxicity will not be tolerated at all in the server."},{id:"emergency-abuse",title:"Emergency Service Abuse",content:"Additionally, 311/911 should not be used for cursing the government employees. (No Actions will be taken if both the party agrees)"},{id:"hacker-items",title:"Hacker Items",content:"Accepting any kind of items from hackers/doublers may it be in any means, should be reported to the support team as soon as possible. Taking any items from them will lead to ban."},{id:"playtime-requirement",title:"Playtime Requirement",content:"You must have a minimum of 12 hours playtime to initiate any kind of criminal activity."},{id:"pov-requirement",title:"POV Requirement",content:"You must have a POV (point of view) if you are initiating any type of situation & must have citizen ID's."},{id:"ems-hostage",title:"EMS Hostage",content:"You can't take EMS as a hostage at any cost when he/she is on-duty."},{id:"report-pov",title:"Report POV",content:"When submitting a player report it is mandatory to provide a POV that is raw and not edited/cut in middle. A valid judgement will be taken by the administration upon proper investigations of POV's from both parties."}]},{id:"general",name:"General Rules",rules:[{id:"rdm",title:"RDM",content:"1. Killing people without giving them a specific reason. Always take the initiative and explain why you're doing what you're doing. They wouldn't always need to initiate before shooting, depending on the story building up to a fight between different tribes. RDM is killing someone when they are AFK."},{id:"combat-logging",title:"Combat Logging",content:"2. Logging out or Quitting during an active situation to avoid role-play it leads to ban."},{id:"vdm",title:"VDM",content:"3. Never kill anyone on purpose using a vehicle as a weapon. Also you cannot deliberately use a vehicle to run over someone unless there is direct threat of your own life."},{id:"cop-baiting",title:"Cop Baiting",content:"Cop baiting is prohibited, this is when your sole purpose is to attract the attention of LEO with no gain to the development of your character."}]},{id:"meta-gaming",name:"Meta Gaming",rules:[{id:"ooc-information",title:"Using Out-of-Character Information",content:"1. Using out-of-character/external information for in-game situations. This includes stream sniping (OOC to IC)."},{id:"content-permission",title:"Verbal/Digital Content Permission",content:"2. Verbal/Digital Content Permission."},{id:"copyrighted-music",title:"Copyrighted Music",content:"3. You cannot play copyrighted music unless you have everyone's permission, and you cannot play it at government places such as Pillbox/PD, etc."},{id:"emergency-impersonation",title:"Impersonation of Emergency Services",content:"4. Impersonation of Emergency Services - Impersonation of Police/EMS/Mechanics is forbidden. You may not use their character models."},{id:"character-development",title:"Character Development",content:"5. Character development is the observable changes the character makes as the narrative progresses. To track character development, the player should focus on the character traits and circumstances of the character. Examples of the character's circumstances are where they live, what they eat, their family structure, social life, appearance, and backstory, etc."}]},{id:"police-hostage",name:"Police as Hostage",rules:[{id:"hostage-requirements",title:"Police as Hostage Requirements",content:"You can take on-duty police as a hostage only when there are more than 6 on-duty police officers which involves proper roleplay scenarios. You can't directly gunpoint any on-duty officer and take them hostage, and you must have a back-story to take police as a Hostage."}]},{id:"pd-looting",name:"PD Looting",rules:[{id:"police-items",title:"Police Items Restriction",content:"1. You can't loot police items or guns (those which are not available publicly)"},{id:"public-items",title:"Public Items",content:"2. You can loot ammo's and other items which are publicly available in city and the guns which are available in the city for civilians/gangs."},{id:"corrupt-cop",title:"Corrupt Cop",content:"3. Being a corrupt cop is not allowed, until staff/admin need to confirm."},{id:"corrupt-ems",title:"Corrupt EMS/Mechanic",content:"4. Being a corrupt EMS / Mechanic is not allowed."}]},{id:"robbery-rules",name:"Robbery Rules",rules:[{id:"house-robbery",title:"House Robbery",content:"• Maximum 2-3 people allowed in house robbery\n• You can take only 1 hostage in a house robbery. (Optional)\n• You will get 2 demands from the police. (Note : If you have hostage)\n• If you don't have hostage negotiate with police to chase or surrender and continue situation.\n• Suspects can use 1 escape vehicles\n• Code Red is strictly prohibited."},{id:"fleeca-bank-store",title:"Fleeca Bank & Store Robbery",content:"• Min 2-4people in one robbery situation are allowed.\n• Only 1 escape vehicle is allowed.\nDemands and Info:\n• You can take only one person as a hostage.\n• Friendly hostage is not allowed.\n• Robbers can keep only 2 demands."},{id:"jewellery-robbery",title:"Jewellery Robbery",content:"• Maximum 4 people in one robbery situation are allowed.\n• Only 1 escape vehicle is allowed.\nDemands and Info:\n• You can take only 1-2 person as a hostage.\n• Friendly hostage is not allowed.\n• Robbers can keep only 2 demands"},{id:"pacific-bank",title:"Pacific Bank Robbery",content:"• Maximum 4-6 people in one robbery situation are allowed.\n• Only 1 escape vehicle is allowed.\nDemands and Info:\n• You can take 1-2 persons as a hostage.\n• Friendly hostage is not allowed.\n• Robbers can keep only 2 demands. Negotiate with PD if you want extra 1 demand.(if you have 2 hostages you can have 3 demands)"}]},{id:"important-rules",name:"Important Rules",rules:[{id:"staff-respect",title:"Disrespecting Staff/Support",content:"Staff Members are only fulfilling their role here on TGRS when they make a decision. Do not make their life difficult by disrespecting them (Verbal Abuse / Threats), this is not permitted this will mute you for a Day on discord and 30 Community Service in game."},{id:"spamming",title:"Spamming",content:"Messaging Staff members through the use of our Discord Server multiple times will result in a delay in your request. Each member of Staff has their own reasons for not responding the first time so if you do not get a response, be patient and wait for your turn."},{id:"staff-abuse",title:"Staff Abuse",content:"If a Staff member is caught abusing his/her power with clear evidence and no clear reasoning, they will be dealt with depending on the severity of the issue."},{id:"staff-discretion",title:"Staff Discretion",content:"TGRS Staff reserve the right to restrict access to our services (game server and Discord) at their own discretion. This may include (but not limited to) players trying to cause issues, players not willing to resolve disputes, players who show a bad attitude towards rule violations etc."}]},{id:"robbery-demands-vehicle",name:"Robbery Demands and Vehicle Rules",rules:[{id:"money-car-demands",title:"Money and Car Demands",content:"♦ You cannot demand Money for Hostage.\n♦ You cannot demand Car for Hostage."},{id:"police-shooting",title:"Police Shooting Rules",content:"♦ If Police Fires First, then their Intention is to Burst the tires only, unless until Civilian Cod-Reds, Police Won't Shoot You."},{id:"vehicle-robbery",title:"Vehicle Robbery Restrictions",content:"♦ You may not rob the bank, store with a vehicle inside."},{id:"party-size",title:"Party Size Announcement",content:"♦ You must announce your party size at the beginning of a store/bank robbery to the police."},{id:"water-escape",title:"Water Escape",content:"♦ If you choose water to escape then there will be a code red situation. Police will do Code RED."},{id:"pd-guns-demand",title:"PD Guns Demand",content:"♦ You cannot demand PD Guns For Police hostage situations."},{id:"demands-acceptance",title:"Demands Acceptance",content:"♦ The number of demands and type of demands (of the robbers) will be accepted or declined according to the POLICE decision while a scripted robbery is in progress."}]},{id:"police-chase",name:"Police Chase Rules",rules:[{id:"body-cam",title:"Body Cam Requirement",content:"• Always Body Cam ON when you are in duty/situation."},{id:"quick-repair",title:"Quick Repair Restrictions",content:"• Quick repair at a mechanic shop is great but when in police chase you are not allowed to do so as it is very unrealistic if your cars dying get out, leave it and find a new one."},{id:"interference",title:"No Interference",content:"• If you see someone who has been pulled over do not interfere with their scenario unless you are involved."},{id:"robbery-interference",title:"Robbery Interference",content:"• If you see a robbery taking place don't be a hero drive to a safe location and call the police."},{id:"chase-involvement",title:"Chase Involvement",content:"• If you see a police chase do not get involved you can give directions if asked by a police officer but do not try to ram the suspect, shoot and or join the pursuit if you want to."},{id:"pit-timing",title:"PIT Timing",content:"• You need to give 10-15min time to robbers on chase after the pit will start on higher officer command/call."}]},{id:"emergency-services",name:"Emergency Services",rules:[{id:"city-restart",title:"City Restart Warnings",content:"• City restart warnings appear 30 minutes before they occur, This warning should signal a hold on Bank Robberies, Vangelico hits and convenience stores."},{id:"situation-activity",title:"Situation Activity",content:"• If you are in middle of the situation after situation everyone must be active in situation continue, if not it leads to ban."}]},{id:"basic-rules",name:"Basic Rules",rules:[{id:"death-communication",title:"Death Communication",content:"People should not be allowed to speak after death."},{id:"cuffed-radio",title:"Cuffed Radio",content:"People should not be allowed to use the radio while cuffed."},{id:"cuffed-doors",title:"Cuffed Door Access",content:"People should not be allowed to open and close doors while cuffed."},{id:"respawn-return",title:"Respawn Return",content:"People should not be allowed to respawn after death and come back to the same situation."},{id:"combat-log",title:"Combat Logging",content:"People should not be allowed to 'quit' (Combat Log) after dying."},{id:"store-robbery",title:"Store Robbery",content:"Multiple people should not be allowed to be involved in a Store Robbery."},{id:"ems-transport",title:"EMS Transport",content:"People shouldn't be allowed to carry their friends to the Pillbox in the presence of EMS."},{id:"family-abuse",title:"Family Abuse",content:"People shouldn't be allowed to abuse someone's father/mother."},{id:"ooc-usage",title:"OOC Usage",content:"You cannot go OOC in situations unless or until for Bugs."},{id:"fail-rp",title:"Fail RP",content:"Don't break any kind of RP situation in between because it is considered as Fail RP."},{id:"new-life-rule",title:"New Life Rule",content:"New life rule should be followed after Respawn or Hospital treatment."},{id:"hostage-restrictions",title:"Hostage Restrictions",content:"You are not allowed to take a hostage from the following locations or situations: Local Job Areas (Taxi, Lumber, Garbage, Electrician, etc.), Safe Zone / Green Zone, New Players from job centers, If they are Unconscious or AFK."},{id:"support-contact",title:"Support Contact",content:"If you have any doubt regarding rules, then join support."},{id:"greenzones",title:"Greenzones",content:"All job locations, selling locations, shops, businesses, government establishments and its surroundings are considered as greenzones, you cannot commit any kind of crimes here."},{id:"robbery-requirements",title:"Robbery Requirements",content:"To rob a citizen you need 2 vehicles and 4 people minimum, you cannot rob in the city, you can only rob outside of city or in ghettos. Without proper roleplay you cannot rob anyone."}]},{id:"prohibited-roleplay",name:"Prohibited Role-play",rules:[{id:"afk-farming",title:"AFK Farming",content:"1. AFK - AFK farming, abusing in-game bugs, etc. This will result in an immediate temp/permanent ban."},{id:"smuggling",title:"Smuggling",content:"2. Engaging in smuggling from PD, EMS, or other whitelist jobs and selling those items to the public can result in a bannable offense and character wipe."},{id:"new-life-rule-detail",title:"New Life Rule",content:"3. If you are downed and respawn at the hospital, your character forgets all events leading up to you being downed in the current scenario."},{id:"respawn-restrictions",title:"Respawn Restrictions",content:"4. You may not respawn if you have been advised that police or EMS is on the way to your scene."},{id:"active-situation",title:"Active Situation",content:"5. You may not respawn if you are in an active situation. (15-20mins)"},{id:"ems-call",title:"EMS Call",content:"6. The only time you should be calling EMS/police while downed is when you're 100% alone and your injuries were not inflicted by someone else or any situation. Example: /911 LOCAL CALL"},{id:"ban-appeal",title:"Ban Appeal",content:"7. BAN can be appealed and will be revived."},{id:"hunting-weapons",title:"Hunting Weapons",content:"8. Any weapons/tools that are bought from hunting job and found to be used anywhere else other than hunting will get you banned without hesitation."}]},{id:"marketplace-policy",name:"Marketplace Policy",rules:[{id:"marketplace-zones",title:"Marketplace Zones",content:"At Legion Square, the marketplace is divided into two distinct zones."},{id:"legal-market-title",title:"Legal Market",content:"Legal Market:"},{id:"legal-items",title:"Legal Items Only",content:"1. Only legal items are permitted for sale."},{id:"price-limit",title:"Price Limit",content:"2. The maximum allowed sale price is 30% above the item's base value."},{id:"price-penalties",title:"Price Penalties",content:"3. Any item exceeding this price limit will be subject to removal and penalties."},{id:"illegal-market-title",title:"Illegal Market",content:"Illegal Market:"},{id:"illegal-items",title:"Illegal Items Only",content:"1. Only illegal items may be listed in this section."},{id:"dynamic-pricing",title:"Dynamic Pricing",content:"2. Item pricing is dynamic and may fluctuate based on demand and rarity."},{id:"responsible-selling",title:"Responsible Selling",content:"3. Sellers are expected to act responsibly and within the roleplay framework."}]}];function l(){let[e,t]=(0,i.useState)("server-rules"),[n,l]=(0,i.useState)([]),[d,c]=(0,i.useState)(""),u=e=>{l(t=>t.includes(e)?t.filter(t=>t!==e):[...t,e])},h=s.find(t=>t.id===e),m=h?.rules.filter(e=>e.title.toLowerCase().includes(d.toLowerCase())||e.content.toLowerCase().includes(d.toLowerCase()))||[];return(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(a.default,{}),(0,o.jsxs)("div",{className:"pt-24 pb-8 md:pt-32 md:pb-12 relative bg-black",children:[(0,o.jsx)("div",{className:"absolute inset-0 pattern-circuit opacity-5"}),(0,o.jsx)("div",{className:"container mx-auto px-4 md:px-6 relative z-10",children:(0,o.jsxs)("div",{className:"mb-8 text-center",children:[(0,o.jsxs)("h1",{className:"text-4xl md:text-5xl font-display font-bold text-white mb-6 wave-heading-bg",children:[(0,o.jsx)("span",{className:"text-neon-orange",children:"SERVER"})," RULES"]}),(0,o.jsx)("p",{className:"text-gray-400 max-w-3xl mx-auto",children:"These are the TGRS Community rules, essential for maintaining a fair and immersive roleplay experience."})]})})]}),(0,o.jsx)("div",{className:"section-divider"}),(0,o.jsxs)("section",{className:"py-20 relative bg-black",children:[(0,o.jsx)("div",{className:"absolute inset-0 pattern-hexagon opacity-5"}),(0,o.jsxs)("div",{className:"container mx-auto px-4 md:px-6 relative z-10",children:[(0,o.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4 mb-12",children:[(0,o.jsxs)("div",{className:"glass-enhanced rounded-xl p-4 text-center border border-neon-orange/30 hover:border-neon-orange/70 transition-all duration-300 group",children:[(0,o.jsx)("div",{className:"text-2xl font-bold text-neon-orange mb-1 group-hover:text-white transition-colors",children:s.length}),(0,o.jsx)("div",{className:"text-xs text-gray-400 group-hover:text-gray-200 transition-colors",children:"Categories"})]}),(0,o.jsxs)("div",{className:"glass-enhanced rounded-xl p-4 text-center border border-neon-orange/30 hover:border-neon-orange/70 transition-all duration-300 group",children:[(0,o.jsx)("div",{className:"text-2xl font-bold text-neon-orange mb-1 group-hover:text-white transition-colors",children:s.reduce((e,t)=>e+t.rules.length,0)}),(0,o.jsx)("div",{className:"text-xs text-gray-400 group-hover:text-gray-200 transition-colors",children:"Total Rules"})]}),(0,o.jsxs)("div",{className:"glass-enhanced rounded-xl p-4 text-center border border-neon-orange/30 hover:border-neon-orange/70 transition-all duration-300 group",children:[(0,o.jsx)("div",{className:"text-2xl font-bold text-neon-orange mb-1 group-hover:text-white transition-colors",children:"24/7"}),(0,o.jsx)("div",{className:"text-xs text-gray-400 group-hover:text-gray-200 transition-colors",children:"Enforcement"})]}),(0,o.jsxs)("div",{className:"glass-enhanced rounded-xl p-4 text-center border border-neon-orange/30 hover:border-neon-orange/70 transition-all duration-300 group",children:[(0,o.jsx)("div",{className:"text-2xl font-bold text-neon-orange mb-1 group-hover:text-white transition-colors",children:"Fair"}),(0,o.jsx)("div",{className:"text-xs text-gray-400 group-hover:text-gray-200 transition-colors",children:"Gameplay"})]})]}),(0,o.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-8",children:[(0,o.jsxs)("div",{className:"lg:col-span-1",children:[(0,o.jsx)("div",{className:"mb-6",children:(0,o.jsxs)("div",{className:"relative glass-enhanced rounded-xl border border-neon-orange/30 hover:border-neon-orange/70 transition-all duration-300 group",children:[(0,o.jsx)("svg",{className:"absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400 group-hover:text-neon-orange transition-colors",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,o.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})}),(0,o.jsx)("input",{type:"text",placeholder:"Search rules...",value:d,onChange:e=>c(e.target.value),className:"w-full bg-transparent border-0 rounded-xl pl-12 pr-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-neon-orange/50"})]})}),(0,o.jsxs)("div",{className:"space-y-3",children:[(0,o.jsx)("h3",{className:"text-sm font-semibold text-neon-orange uppercase tracking-wider mb-6",children:"Rule Categories"}),s.map(n=>(0,o.jsxs)("button",{onClick:()=>{t(n.id),l([])},className:`w-full text-left px-5 py-4 rounded-xl transition-all duration-300 group relative overflow-hidden transform hover:scale-[1.02] ${e===n.id?"glass-enhanced border-2 border-neon-orange text-white font-bold bg-gradient-to-r from-neon-orange/15 to-neon-red/10 shadow-lg shadow-neon-orange/20":"glass-enhanced border border-neon-orange/30 text-gray-300 hover:border-neon-orange/70 hover:text-white hover:bg-neon-orange/5"}`,children:[(0,o.jsxs)("div",{className:"flex items-center justify-between relative z-10",children:[(0,o.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,o.jsx)("div",{className:`w-3 h-3 rounded-full transition-all duration-300 ${e===n.id?"bg-neon-orange shadow-lg shadow-neon-orange/50 animate-pulse":"bg-neon-orange/60 group-hover:bg-neon-orange group-hover:shadow-md group-hover:shadow-neon-orange/30"}`}),(0,o.jsx)("span",{className:`font-medium transition-all duration-300 ${e===n.id?"text-base":"text-sm group-hover:text-base"}`,children:n.name})]}),(0,o.jsx)("span",{className:`text-xs px-3 py-1.5 rounded-full font-semibold transition-all duration-300 ${e===n.id?"bg-neon-orange/30 text-neon-orange border-2 border-neon-orange/50 shadow-md":"bg-neon-orange/15 text-neon-orange/80 border border-neon-orange/30 group-hover:bg-neon-orange/25 group-hover:text-neon-orange group-hover:border-neon-orange/50"}`,children:n.rules.length})]}),e===n.id?(0,o.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-neon-orange/10 via-neon-orange/5 to-neon-red/5 opacity-100"}):(0,o.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-neon-orange/8 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"}),e===n.id&&(0,o.jsx)("div",{className:"absolute left-0 top-1/2 transform -translate-y-1/2 w-1 h-8 bg-gradient-to-b from-neon-orange to-neon-red rounded-r-full"}),(0,o.jsx)("div",{className:`absolute inset-0 rounded-xl transition-all duration-300 ${e===n.id?"shadow-inner shadow-neon-orange/20":"group-hover:shadow-lg group-hover:shadow-neon-orange/10"}`})]},n.id))]})]}),(0,o.jsxs)("div",{className:"lg:col-span-3",children:[(0,o.jsxs)("div",{className:"glass-enhanced rounded-xl border border-neon-orange/30 overflow-hidden",children:[(0,o.jsxs)("div",{className:"relative p-6 bg-gradient-to-r from-neon-orange/10 to-neon-red/10 border-b border-neon-orange/30",children:[(0,o.jsx)("div",{className:"absolute inset-0 pattern-circuit opacity-10"}),(0,o.jsxs)("div",{className:"relative z-10 flex items-center justify-between",children:[(0,o.jsxs)("div",{children:[(0,o.jsx)("h2",{className:"text-2xl md:text-3xl font-display font-bold text-white mb-2",children:h?.name}),(0,o.jsxs)("p",{className:"text-neon-orange text-sm",children:[m.length," rule",1!==m.length?"s":""," in this category"]})]}),(0,o.jsxs)("div",{className:"hidden md:flex items-center space-x-2",children:[(0,o.jsx)("div",{className:"w-3 h-3 bg-neon-orange rounded-full animate-pulse"}),(0,o.jsx)("span",{className:"text-xs text-gray-400 uppercase tracking-wider",children:"Active"})]})]})]}),(0,o.jsx)("div",{children:m.map((e,t)=>(0,o.jsxs)("div",{className:"border-b border-neon-orange/10 last:border-b-0",children:[(0,o.jsxs)("button",{onClick:()=>u(e.id),className:"w-full px-6 py-5 text-left hover:bg-neon-orange/5 transition-all duration-300 flex items-center justify-between group relative overflow-hidden",children:[(0,o.jsxs)("div",{className:"flex items-center space-x-4 flex-1",children:[(0,o.jsx)("div",{className:"flex-shrink-0",children:(0,o.jsx)("div",{className:"w-8 h-8 rounded-lg bg-gradient-to-br from-neon-orange/20 to-neon-red/20 border border-neon-orange/30 flex items-center justify-center group-hover:border-neon-orange/70 transition-colors",children:(0,o.jsx)("span",{className:"text-xs font-bold text-neon-orange group-hover:text-white transition-colors",children:String(t+1).padStart(2,"0")})})}),(0,o.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,o.jsx)("h3",{className:"text-white font-semibold group-hover:text-neon-orange transition-colors text-left",children:e.title}),(0,o.jsxs)("p",{className:"text-gray-400 text-sm mt-1 line-clamp-1",children:[e.content.substring(0,80),"..."]})]})]}),(0,o.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,o.jsx)("div",{className:`px-3 py-1 rounded-full text-xs font-medium transition-colors ${n.includes(e.id)?"bg-neon-orange/20 text-neon-orange border border-neon-orange/30":"bg-gray-700/50 text-gray-400 border border-gray-600/30"}`,children:n.includes(e.id)?"Expanded":"Collapsed"}),(0,o.jsx)("svg",{className:`w-5 h-5 text-gray-400 group-hover:text-neon-orange transition-all duration-300 ${n.includes(e.id)?"rotate-180":""}`,fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,o.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 9l-7 7-7-7"})})]}),(0,o.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-neon-orange/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"})]}),n.includes(e.id)&&(0,o.jsx)("div",{className:"px-6 pb-6 bg-black/20",children:(0,o.jsxs)("div",{className:"ml-12 bg-gradient-to-r from-neon-orange/10 to-neon-red/5 rounded-xl p-6 border border-neon-orange/30 relative overflow-hidden",children:[(0,o.jsx)("div",{className:"absolute top-0 left-0 w-1 h-full bg-gradient-to-b from-neon-orange to-neon-red"}),(0,o.jsxs)("div",{className:"relative z-10",children:[(0,o.jsxs)("div",{className:"flex items-center space-x-2 mb-3",children:[(0,o.jsx)("svg",{className:"w-4 h-4 text-neon-orange",fill:"currentColor",viewBox:"0 0 20 20",children:(0,o.jsx)("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z",clipRule:"evenodd"})}),(0,o.jsx)("span",{className:"text-neon-orange text-sm font-semibold uppercase tracking-wider",children:"Rule Details"})]}),(0,o.jsx)("p",{className:"text-gray-200 leading-relaxed whitespace-pre-line",children:e.content})]}),(0,o.jsx)("div",{className:"absolute bottom-2 right-2 w-3 h-3 border-t-2 border-r-2 border-neon-orange/30"})]})})]},e.id))}),0===m.length&&(0,o.jsxs)("div",{className:"p-12 text-center",children:[(0,o.jsx)("div",{className:"w-16 h-16 mx-auto mb-4 rounded-full bg-gradient-to-br from-neon-orange/20 to-neon-red/20 border border-neon-orange/30 flex items-center justify-center",children:(0,o.jsx)("svg",{className:"w-8 h-8 text-neon-orange",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,o.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})})}),(0,o.jsx)("h3",{className:"text-white font-semibold mb-2",children:"No Rules Found"}),(0,o.jsx)("p",{className:"text-gray-400",children:"Try adjusting your search terms or select a different category."})]})]}),(0,o.jsxs)("div",{className:"mt-8 glass-enhanced rounded-xl p-6 border border-neon-orange/30 relative overflow-hidden",children:[(0,o.jsx)("div",{className:"absolute inset-0 pattern-dots opacity-5"}),(0,o.jsx)("div",{className:"relative z-10",children:(0,o.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,o.jsx)("div",{className:"flex-shrink-0",children:(0,o.jsx)("div",{className:"w-12 h-12 rounded-xl bg-gradient-to-br from-neon-orange/20 to-neon-red/20 border border-neon-orange/30 flex items-center justify-center",children:(0,o.jsx)("svg",{className:"w-6 h-6 text-neon-orange",fill:"currentColor",viewBox:"0 0 20 20",children:(0,o.jsx)("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z",clipRule:"evenodd"})})})}),(0,o.jsxs)("div",{className:"flex-1",children:[(0,o.jsxs)("h3",{className:"text-white font-semibold mb-2 flex items-center",children:[(0,o.jsx)("span",{children:"Important Notice"}),(0,o.jsx)("div",{className:"ml-2 w-2 h-2 bg-neon-orange rounded-full animate-pulse"})]}),(0,o.jsx)("p",{className:"text-gray-300 text-sm leading-relaxed mb-4",children:"By joining our server, you agree to follow these rules. The staff team reserves the right to modify these rules at any time and enforce them at their discretion."}),(0,o.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3",children:[(0,o.jsxs)("a",{href:"https://discord.gg/GAMravHDnB",target:"_blank",rel:"noopener noreferrer",className:"inline-flex items-center px-4 py-2 bg-gradient-to-r from-neon-orange to-neon-red text-black font-semibold rounded-lg hover:shadow-neon-strong transition-all duration-300 group",children:[(0,o.jsx)("svg",{className:"w-4 h-4 mr-2 group-hover:scale-110 transition-transform",fill:"currentColor",viewBox:"0 0 24 24",children:(0,o.jsx)("path",{d:"M20.317 4.37a19.791 19.791 0 0 0-4.885-1.515.074.074 0 0 0-.079.037c-.21.375-.444.864-.608 1.25a18.27 18.27 0 0 0-5.487 0 12.64 12.64 0 0 0-.617-1.25.077.077 0 0 0-.079-.037A19.736 19.736 0 0 0 3.677 4.37a.07.07 0 0 0-.032.027C.533 9.046-.32 13.58.099 18.057a.082.082 0 0 0 .031.057 19.9 19.9 0 0 0 5.993 3.03.078.078 0 0 0 .084-.028c.462-.63.874-1.295 1.226-1.994a.076.076 0 0 0-.041-.106 13.107 13.107 0 0 1-1.872-.892.077.077 0 0 1-.008-.128 10.2 10.2 0 0 0 .372-.292.074.074 0 0 1 .077-.01c3.928 1.793 8.18 1.793 12.062 0a.074.074 0 0 1 .078.01c.12.098.246.198.373.292a.077.077 0 0 1-.006.127 12.299 12.299 0 0 1-1.873.892.077.077 0 0 0-.041.107c.36.698.772 1.362 1.225 1.993a.076.076 0 0 0 .084.028 19.839 19.839 0 0 0 6.002-3.03.077.077 0 0 0 .032-.054c.5-5.177-.838-9.674-3.549-13.66a.061.061 0 0 0-.031-.03z"})}),"Join Discord Support"]}),(0,o.jsxs)("div",{className:"inline-flex items-center px-4 py-2 bg-black/30 border border-neon-orange/30 text-gray-300 rounded-lg",children:[(0,o.jsx)("svg",{className:"w-4 h-4 mr-2 text-neon-orange",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,o.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})}),(0,o.jsx)("span",{className:"text-sm",children:"24/7 Enforcement"})]})]})]})]})}),(0,o.jsx)("div",{className:"absolute top-2 right-2 w-3 h-3 border-t-2 border-r-2 border-neon-orange/30"}),(0,o.jsx)("div",{className:"absolute bottom-2 left-2 w-3 h-3 border-b-2 border-l-2 border-neon-orange/30"})]})]})]})]})]}),(0,o.jsx)(r.A,{})]})}},2425:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>o});let o=(0,n(6760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\TGRS Website\\\\tgrs-website\\\\src\\\\app\\\\rules\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\TGRS Website\\tgrs-website\\src\\app\\rules\\page.tsx","default")}};var t=require("../../webpack-runtime.js");t.C(e);var n=e=>t(t.s=e),o=t.X(0,[638,413,666,540,662,403],()=>n(7090));module.exports=o})();