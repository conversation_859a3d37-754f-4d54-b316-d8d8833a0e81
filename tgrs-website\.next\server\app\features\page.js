(()=>{var e={};e.id=944,e.ids=[944],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},9551:e=>{"use strict";e.exports=require("url")},6879:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>o.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>x,tree:()=>d});var r=t(260),a=t(8203),n=t(5155),o=t.n(n),i=t(7292),l={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);t.d(s,l);let d=["",{children:["features",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,6735)),"C:\\Users\\<USER>\\Desktop\\TGRS Website\\tgrs-website\\src\\app\\features\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,1354)),"C:\\Users\\<USER>\\Desktop\\TGRS Website\\tgrs-website\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,9937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,9116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,1485,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["C:\\Users\\<USER>\\Desktop\\TGRS Website\\tgrs-website\\src\\app\\features\\page.tsx"],m={require:t,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/features/page",pathname:"/features",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},7218:(e,s,t)=>{Promise.resolve().then(t.bind(t,6735))},6602:(e,s,t)=>{Promise.resolve().then(t.bind(t,3323))},3323:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>l});var r=t(5512);t(8009);var a=t(6519),n=t(8784);let o=({title:e,description:s,icon:t,className:a="",tiltDirection:n="none",pattern:o="circuit"})=>(0,r.jsxs)("div",{className:`glass-enhanced rounded-lg p-3 sm:p-4 md:p-5 border border-neon-orange/30 hover:border-neon-orange/70 transition-all duration-500 hover:shadow-neon-strong group relative h-full flex flex-col ${(()=>{switch(n){case"left":return"card-tilt-left";case"right":return"card-tilt-right";default:return""}})()} ${(()=>{switch(o){case"dots":return"pattern-dots";case"grid":return"pattern-grid";case"hexagon":return"pattern-hexagon";case"circuit":return"pattern-circuit";default:return""}})()} ${a}`,children:[(0,r.jsx)("div",{className:"absolute inset-0 bg-black/20 rounded-xl"}),(0,r.jsxs)("div",{className:"relative z-10 flex flex-col h-full",children:[t&&(0,r.jsx)("div",{className:"mb-6 sm:mb-4 md:mb-3 text-neon-orange group-hover:text-white transition-all duration-300 relative icon-container",children:(0,r.jsxs)("div",{className:"w-6 h-6 sm:w-7 sm:h-7 md:w-8 md:h-8 relative transform group-hover:scale-105 transition-transform duration-300 origin-center",children:[(0,r.jsxs)("div",{className:"absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-500 pointer-events-none",children:[(0,r.jsx)("div",{className:"absolute w-1 h-1 bg-neon-orange rounded-full animate-ping",style:{top:"-4px",left:"20%",animationDelay:"0s"}}),(0,r.jsx)("div",{className:"absolute w-0.5 h-0.5 bg-yellow-400 rounded-full animate-pulse",style:{top:"-2px",right:"25%",animationDelay:"0.4s"}}),(0,r.jsx)("div",{className:"absolute w-1 h-1 bg-orange-400 rounded-full animate-ping",style:{right:"-4px",top:"30%",animationDelay:"0.2s"}}),(0,r.jsx)("div",{className:"absolute w-0.5 h-0.5 bg-yellow-300 rounded-full animate-pulse",style:{right:"-2px",bottom:"35%",animationDelay:"0.7s"}}),(0,r.jsx)("div",{className:"absolute w-1 h-1 bg-neon-orange rounded-full animate-ping",style:{bottom:"-4px",right:"20%",animationDelay:"0.5s"}}),(0,r.jsx)("div",{className:"absolute w-0.5 h-0.5 bg-orange-500 rounded-full animate-pulse",style:{bottom:"-2px",left:"30%",animationDelay:"0.9s"}}),(0,r.jsx)("div",{className:"absolute w-1 h-1 bg-yellow-400 rounded-full animate-ping",style:{left:"-4px",bottom:"25%",animationDelay:"0.3s"}}),(0,r.jsx)("div",{className:"absolute w-0.5 h-0.5 bg-neon-orange rounded-full animate-pulse",style:{left:"-2px",top:"40%",animationDelay:"0.6s"}})]}),(0,r.jsx)("div",{className:"w-full h-full relative z-10",children:t})]})}),(0,r.jsx)("h3",{className:"text-sm sm:text-base md:text-lg font-display font-bold text-white mb-1 sm:mb-2 group-hover:text-neon-orange transition-colors duration-300",children:e}),(0,r.jsx)("p",{className:"text-xs sm:text-sm md:text-base text-gray-400 group-hover:text-gray-200 transition-colors duration-300 leading-relaxed flex-grow",children:s})]}),(0,r.jsx)("div",{className:"absolute top-2 right-2 w-3 h-3 border-t-2 border-r-2 border-neon-orange/30 group-hover:border-neon-orange transition-colors duration-300 z-20"}),(0,r.jsx)("div",{className:"absolute bottom-2 left-2 w-3 h-3 border-b-2 border-l-2 border-neon-orange/30 group-hover:border-neon-orange transition-colors duration-300 z-20"})]});var i=t(2149);function l(){return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(a.default,{}),(0,r.jsxs)("div",{className:"pt-24 pb-12 md:pt-32 md:pb-20 relative bg-black",children:[(0,r.jsx)("div",{className:"absolute inset-0 pattern-circuit opacity-5"}),(0,r.jsx)("div",{className:"container mx-auto px-4 md:px-6 relative z-10",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsxs)("h1",{className:"text-4xl md:text-5xl font-display font-bold text-white mb-6 wave-heading-bg",children:[(0,r.jsx)("span",{className:"text-neon-orange",children:"SERVER"})," FEATURES"]}),(0,r.jsx)("p",{className:"text-gray-400 max-w-3xl mx-auto",children:"Discover what makes TGRS the premier Telugu community FiveM roleplay server."})]})})]}),(0,r.jsx)("div",{className:"section-divider"}),(0,r.jsx)("section",{className:"py-20 relative bg-black/50",children:(0,r.jsxs)("div",{className:"container mx-auto px-6 sm:px-12 md:px-16 lg:px-20 xl:px-24",children:[(0,r.jsx)("div",{className:"max-w-6xl mx-auto",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 md:gap-8 mb-12 md:mb-16",children:[(0,r.jsxs)("div",{className:"glass rounded-lg p-6 border border-neon-orange/20 text-center",children:[(0,r.jsx)("div",{className:"w-16 h-16 rounded-full bg-neon-orange/20 flex items-center justify-center mx-auto mb-4",children:(0,r.jsx)("svg",{className:"w-8 h-8 text-neon-orange",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"})})}),(0,r.jsx)("h3",{className:"text-xl font-display font-semibold text-white mb-2",children:"Optimized Performance"}),(0,r.jsx)("p",{className:"text-gray-400",children:"Our server is optimized for smooth gameplay with minimal lag, ensuring a seamless roleplay experience."})]}),(0,r.jsxs)("div",{className:"glass rounded-lg p-6 border border-neon-orange/20 text-center",children:[(0,r.jsx)("div",{className:"w-16 h-16 rounded-full bg-neon-orange/20 flex items-center justify-center mx-auto mb-4",children:(0,r.jsx)("svg",{className:"w-8 h-8 text-neon-orange",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"})})}),(0,r.jsx)("h3",{className:"text-xl font-display font-semibold text-white mb-2",children:"Telugu Community"}),(0,r.jsx)("p",{className:"text-gray-400",children:"A dedicated server for Telugu gamers, creating a unique cultural connection within the roleplay experience."})]}),(0,r.jsxs)("div",{className:"glass rounded-lg p-6 border border-neon-orange/20 text-center",children:[(0,r.jsx)("div",{className:"w-16 h-16 rounded-full bg-neon-orange/20 flex items-center justify-center mx-auto mb-4",children:(0,r.jsx)("svg",{className:"w-8 h-8 text-neon-orange",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"})})}),(0,r.jsx)("h3",{className:"text-xl font-display font-semibold text-white mb-2",children:"Custom Scripts"}),(0,r.jsx)("p",{className:"text-gray-400",children:"Unique and exclusive scripts developed specifically for our server to enhance the roleplay experience."})]})]})}),(0,r.jsx)("div",{className:"section-divider"}),(0,r.jsxs)("div",{className:"mb-12 md:mb-16 pt-16 md:pt-20",children:[(0,r.jsxs)("div",{className:"text-center mb-8 md:mb-12",children:[(0,r.jsxs)("h2",{className:"text-3xl md:text-4xl font-display font-bold text-white mb-4 wave-heading-bg",children:[(0,r.jsx)("span",{className:"text-neon-orange",children:"GAMEPLAY"})," FEATURES"]}),(0,r.jsx)("p",{className:"text-gray-400 max-w-3xl mx-auto px-2",children:"Immersive gameplay elements that make TGRS unique"})]}),(0,r.jsx)("div",{className:"max-w-6xl mx-auto",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-5 md:gap-6",children:[(0,r.jsx)(o,{title:"Custom Character Creation",description:"Create a unique character with extensive customization options tailored for diverse appearances.",icon:(0,r.jsx)("svg",{className:"w-10 h-10",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"})})}),(0,r.jsx)(o,{title:"Advanced Economy System",description:"A balanced economy with multiple jobs, businesses, and investment opportunities.",icon:(0,r.jsx)("svg",{className:"w-10 h-10",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})})}),(0,r.jsx)(o,{title:"Custom Properties",description:"Own and customize properties throughout the city, from apartments to businesses.",icon:(0,r.jsx)("svg",{className:"w-10 h-10",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"})})}),(0,r.jsx)(o,{title:"Realistic Vehicle System",description:"Detailed vehicle mechanics including fuel, damage, and maintenance requirements.",icon:(0,r.jsx)("svg",{className:"w-10 h-10",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 10V3L4 14h7v7l9-11h-7z"})})}),(0,r.jsx)(o,{title:"Advanced Job System",description:"Multiple legal and illegal career paths with progression and unique gameplay mechanics.",icon:(0,r.jsx)("svg",{className:"w-10 h-10",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2m4 6h.01M5 20h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"})})}),(0,r.jsx)(o,{title:"Dynamic Weather System",description:"Realistic and dynamic weather patterns that affect gameplay and visibility.",icon:(0,r.jsx)("svg",{className:"w-10 h-10",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 15a4 4 0 004 4h9a5 5 0 10-.1-9.999 5.002 5.002 0 10-9.78 2.096A4.001 4.001 0 003 15z"})})})]})})]}),(0,r.jsx)("div",{className:"section-divider"}),(0,r.jsxs)("div",{className:"mb-12 md:mb-16 pt-12 md:pt-16",children:[(0,r.jsxs)("div",{className:"text-center mb-8 md:mb-12",children:[(0,r.jsxs)("h2",{className:"text-2xl sm:text-3xl md:text-4xl font-display font-bold text-white mb-4 wave-heading-bg",children:[(0,r.jsx)("span",{className:"text-neon-orange",children:"COMMUNITY"})," FEATURES"]}),(0,r.jsx)("p",{className:"text-sm sm:text-base md:text-lg text-gray-400 max-w-3xl mx-auto px-4",children:"Elements that enhance our community experience"})]}),(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6 sm:gap-8 lg:gap-12 items-stretch",children:[(0,r.jsxs)("div",{className:"glass-enhanced rounded-xl p-6 sm:p-8 border border-neon-orange/30 hover:border-neon-orange/60 transition-all duration-500 hover:shadow-neon-strong group h-full",children:[(0,r.jsxs)("h3",{className:"text-xl sm:text-2xl font-display font-semibold text-white mb-4 sm:mb-6 flex items-center group-hover:text-neon-orange transition-colors duration-300",children:[(0,r.jsx)("div",{className:"w-8 h-8 sm:w-10 sm:h-10 mr-3 sm:mr-4 text-neon-orange group-hover:text-white transition-colors duration-300 relative",children:(0,r.jsx)("svg",{className:"w-full h-full",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"})})}),"Active Discord Community"]}),(0,r.jsx)("p",{className:"text-sm sm:text-base text-gray-400 mb-4 sm:mb-6 group-hover:text-gray-200 transition-colors duration-300 leading-relaxed",children:"Our Discord server is the hub for all community activities, featuring:"}),(0,r.jsxs)("ul",{className:"space-y-3 sm:space-y-4 text-sm sm:text-base text-gray-400 group-hover:text-gray-200 transition-colors duration-300",children:[(0,r.jsxs)("li",{className:"flex items-start",children:[(0,r.jsx)("span",{className:"text-neon-orange mr-3 mt-1 text-lg",children:"•"}),(0,r.jsx)("span",{className:"leading-relaxed",children:"Dedicated support channels"})]}),(0,r.jsxs)("li",{className:"flex items-start",children:[(0,r.jsx)("span",{className:"text-neon-orange mr-3 mt-1 text-lg",children:"•"}),(0,r.jsx)("span",{className:"leading-relaxed",children:"Community events and announcements"})]}),(0,r.jsxs)("li",{className:"flex items-start",children:[(0,r.jsx)("span",{className:"text-neon-orange mr-3 mt-1 text-lg",children:"•"}),(0,r.jsx)("span",{className:"leading-relaxed",children:"Character development resources"})]}),(0,r.jsxs)("li",{className:"flex items-start",children:[(0,r.jsx)("span",{className:"text-neon-orange mr-3 mt-1 text-lg",children:"•"}),(0,r.jsx)("span",{className:"leading-relaxed",children:"Voice channels for out-of-game socializing"})]})]})]}),(0,r.jsxs)("div",{className:"glass-enhanced rounded-xl p-6 sm:p-8 border border-neon-orange/30 hover:border-neon-orange/60 transition-all duration-500 hover:shadow-neon-strong group h-full",children:[(0,r.jsxs)("h3",{className:"text-xl sm:text-2xl font-display font-semibold text-white mb-4 sm:mb-6 flex items-center group-hover:text-neon-orange transition-colors duration-300",children:[(0,r.jsx)("div",{className:"w-8 h-8 sm:w-10 sm:h-10 mr-3 sm:mr-4 text-neon-orange group-hover:text-white transition-colors duration-300 relative",children:(0,r.jsx)("svg",{className:"w-full h-full",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"})})}),"Regular Events"]}),(0,r.jsx)("p",{className:"text-sm sm:text-base text-gray-400 mb-4 sm:mb-6 group-hover:text-gray-200 transition-colors duration-300 leading-relaxed",children:"We host a variety of events to keep the community engaged:"}),(0,r.jsxs)("ul",{className:"space-y-3 sm:space-y-4 text-sm sm:text-base text-gray-400 group-hover:text-gray-200 transition-colors duration-300",children:[(0,r.jsxs)("li",{className:"flex items-start",children:[(0,r.jsx)("span",{className:"text-neon-orange mr-3 mt-1 text-lg",children:"•"}),(0,r.jsx)("span",{className:"leading-relaxed",children:"Weekly car meets and races"})]}),(0,r.jsxs)("li",{className:"flex items-start",children:[(0,r.jsx)("span",{className:"text-neon-orange mr-3 mt-1 text-lg",children:"•"}),(0,r.jsx)("span",{className:"leading-relaxed",children:"Special holiday celebrations"})]}),(0,r.jsxs)("li",{className:"flex items-start",children:[(0,r.jsx)("span",{className:"text-neon-orange mr-3 mt-1 text-lg",children:"•"}),(0,r.jsx)("span",{className:"leading-relaxed",children:"Community challenges with prizes"})]}),(0,r.jsxs)("li",{className:"flex items-start",children:[(0,r.jsx)("span",{className:"text-neon-orange mr-3 mt-1 text-lg",children:"•"}),(0,r.jsx)("span",{className:"leading-relaxed",children:"Roleplay scenarios and storylines"})]})]})]})]})})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("p",{className:"text-gray-400 mb-6",children:"Ready to experience all these features and more? Join our server today!"}),(0,r.jsx)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:(0,r.jsx)(i.A,{href:"/rules",variant:"outline",size:"lg",children:"View Rules"})})]})]})}),(0,r.jsx)(n.A,{})]})}},2149:(e,s,t)=>{"use strict";t.d(s,{A:()=>o});var r=t(5512);t(8009);var a=t(8531),n=t.n(a);let o=({children:e,href:s,variant:t="primary",size:a="md",className:o="",onClick:i})=>{let l=`inline-flex items-center justify-center font-medium transition-all duration-300 rounded-full ${{primary:"bg-gradient-to-r from-neon-orange to-neon-red text-white hover:shadow-neon",secondary:"bg-neon-orange text-white hover:bg-neon-orange/90 hover:shadow-neon-orange",outline:"border border-neon-orange text-neon-orange hover:bg-neon-orange/10",ghost:"text-white hover:text-neon-orange hover:bg-white/5"}[t]} ${{sm:"text-xs px-3 py-1.5",md:"text-sm px-4 py-2",lg:"text-base px-6 py-3"}[a]} ${o}`;return s?s.startsWith("http")||s.startsWith("https")?(0,r.jsx)("a",{href:s,className:l,target:"_blank",rel:"noopener noreferrer",children:e}):(0,r.jsx)(n(),{href:s,className:l,children:e}):(0,r.jsx)("button",{className:l,onClick:i,children:e})}},6735:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(6760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\TGRS Website\\\\tgrs-website\\\\src\\\\app\\\\features\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\TGRS Website\\tgrs-website\\src\\app\\features\\page.tsx","default")}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[638,413,666,540,662,403],()=>t(6879));module.exports=r})();