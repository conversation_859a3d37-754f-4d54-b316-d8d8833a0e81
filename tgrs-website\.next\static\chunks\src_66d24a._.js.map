{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/TGRS%20Website/tgrs-website/src/components/Navbar.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport Link from \"next/link\";\nimport Image from \"next/image\";\nimport { usePathname } from \"next/navigation\";\n\nconst Navbar = () => {\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n  const [scrolled, setScrolled] = useState(false);\n  const pathname = usePathname();\n\n  // Helper function to check if a link is active\n  const isActive = (href: string) => {\n    if (href === \"/\") {\n      return pathname === \"/\";\n    }\n    return pathname.startsWith(href);\n  };\n\n  useEffect(() => {\n    const handleScroll = () => {\n      if (window.scrollY > 10) {\n        setScrolled(true);\n      } else {\n        setScrolled(false);\n      }\n    };\n\n    window.addEventListener(\"scroll\", handleScroll);\n    return () => window.removeEventListener(\"scroll\", handleScroll);\n  }, []);\n\n  const toggleMenu = () => {\n    setIsMenuOpen(!isMenuOpen);\n  };\n\n  return (\n    <nav\n      className={`fixed top-0 left-0 w-full z-50 transition-all duration-500 ${\n        scrolled\n          ? \"bg-black/90 backdrop-blur-xl py-2 shadow-lg shadow-neon-orange/10\"\n          : \"bg-transparent py-4\"\n      }`}\n    >\n      {/* Subtle pattern overlay */}\n      <div className=\"absolute inset-0 pattern-dots opacity-20 pointer-events-none\"></div>\n\n      <div className=\"container mx-auto px-4 md:px-6 relative\">\n        <div className=\"flex items-center justify-between\">\n          {/* Enhanced Logo */}\n          <Link href=\"/\" className=\"flex items-center group\">\n            <div className=\"relative h-12 w-12 mr-3 transition-all duration-300 group-hover:scale-110 group-hover:rotate-3\">\n              <div className=\"absolute inset-0 bg-gradient-to-br from-neon-orange/20 to-neon-red/20 rounded-full blur-sm group-hover:blur-md transition-all duration-300\"></div>\n              <Image\n                src=\"/assets/tgrs-logo.png\"\n                alt=\"TGRS Logo\"\n                width={48}\n                height={48}\n                className=\"w-full h-full object-contain relative z-10 drop-shadow-lg\"\n                priority\n              />\n            </div>\n            <span className=\"text-white font-display font-bold text-xl tracking-wider group-hover:text-neon-orange transition-all duration-300\">\n              TGRS\n            </span>\n          </Link>\n\n          {/* Enhanced Desktop Navigation */}\n          <div className=\"hidden md:flex items-center space-x-1\">\n            {/* Navigation Links */}\n            <div className=\"flex items-center space-x-1 mr-6\">\n              <Link\n                href=\"/\"\n                className=\"relative px-4 py-2 text-white font-medium transition-all duration-300 group\"\n              >\n                <span className={`relative z-10 transition-colors duration-300 ${\n                  isActive(\"/\") ? \"text-neon-orange\" : \"group-hover:text-neon-orange\"\n                }`}>\n                  Home\n                </span>\n\n                {/* Active state particle effects */}\n                {isActive(\"/\") && (\n                  <div className=\"absolute inset-0 opacity-100 transition-all duration-500\">\n                    {/* Floating particles */}\n                    <div className=\"absolute top-1 left-2 w-1 h-1 bg-neon-orange rounded-full animate-ping\"></div>\n                    <div className=\"absolute top-2 right-3 w-1.5 h-1.5 bg-yellow-400 rounded-full animate-pulse\"></div>\n                    <div className=\"absolute bottom-2 left-3 w-1 h-1 bg-neon-orange rounded-full animate-bounce\"></div>\n                    <div className=\"absolute bottom-1 right-2 w-1 h-1 bg-yellow-300 rounded-full animate-ping\" style={{animationDelay: '0.5s'}}></div>\n                    <div className=\"absolute top-3 left-1/2 w-0.5 h-0.5 bg-neon-orange rounded-full animate-pulse\" style={{animationDelay: '0.3s'}}></div>\n                  </div>\n                )}\n\n                <div className={`absolute inset-0 bg-gradient-to-r from-neon-orange/10 to-neon-red/10 rounded-lg transition-all duration-300 scale-95 ${\n                  isActive(\"/\") ? \"opacity-30 scale-100\" : \"opacity-0 group-hover:opacity-100 group-hover:scale-100\"\n                }`}></div>\n              </Link>\n              <Link\n                href=\"/about\"\n                className=\"relative px-4 py-2 text-white font-medium transition-all duration-300 group\"\n              >\n                <span className={`relative z-10 transition-colors duration-300 ${\n                  isActive(\"/about\") ? \"text-neon-orange\" : \"group-hover:text-neon-orange\"\n                }`}>\n                  About\n                </span>\n\n                {/* Active state particle effects */}\n                {isActive(\"/about\") && (\n                  <div className=\"absolute inset-0 opacity-100 transition-all duration-500\">\n                    {/* Floating particles */}\n                    <div className=\"absolute top-1 left-2 w-1 h-1 bg-neon-orange rounded-full animate-ping\"></div>\n                    <div className=\"absolute top-2 right-3 w-1.5 h-1.5 bg-yellow-400 rounded-full animate-pulse\"></div>\n                    <div className=\"absolute bottom-2 left-3 w-1 h-1 bg-neon-orange rounded-full animate-bounce\"></div>\n                    <div className=\"absolute bottom-1 right-2 w-1 h-1 bg-yellow-300 rounded-full animate-ping\" style={{animationDelay: '0.5s'}}></div>\n                    <div className=\"absolute top-3 left-1/2 w-0.5 h-0.5 bg-neon-orange rounded-full animate-pulse\" style={{animationDelay: '0.3s'}}></div>\n                  </div>\n                )}\n\n                <div className={`absolute inset-0 bg-gradient-to-r from-neon-orange/10 to-neon-red/10 rounded-lg transition-all duration-300 scale-95 ${\n                  isActive(\"/about\") ? \"opacity-30 scale-100\" : \"opacity-0 group-hover:opacity-100 group-hover:scale-100\"\n                }`}></div>\n              </Link>\n              <Link\n                href=\"/features\"\n                className=\"relative px-4 py-2 text-white font-medium transition-all duration-300 group\"\n              >\n                <span className={`relative z-10 transition-colors duration-300 ${\n                  isActive(\"/features\") ? \"text-neon-orange\" : \"group-hover:text-neon-orange\"\n                }`}>\n                  Features\n                </span>\n\n                {/* Active state particle effects */}\n                {isActive(\"/features\") && (\n                  <div className=\"absolute inset-0 opacity-100 transition-all duration-500\">\n                    {/* Floating particles */}\n                    <div className=\"absolute top-1 left-2 w-1 h-1 bg-neon-orange rounded-full animate-ping\"></div>\n                    <div className=\"absolute top-2 right-3 w-1.5 h-1.5 bg-yellow-400 rounded-full animate-pulse\"></div>\n                    <div className=\"absolute bottom-2 left-3 w-1 h-1 bg-neon-orange rounded-full animate-bounce\"></div>\n                    <div className=\"absolute bottom-1 right-2 w-1 h-1 bg-yellow-300 rounded-full animate-ping\" style={{animationDelay: '0.5s'}}></div>\n                    <div className=\"absolute top-3 left-1/2 w-0.5 h-0.5 bg-neon-orange rounded-full animate-pulse\" style={{animationDelay: '0.3s'}}></div>\n                  </div>\n                )}\n\n                <div className={`absolute inset-0 bg-gradient-to-r from-neon-orange/10 to-neon-red/10 rounded-lg transition-all duration-300 scale-95 ${\n                  isActive(\"/features\") ? \"opacity-30 scale-100\" : \"opacity-0 group-hover:opacity-100 group-hover:scale-100\"\n                }`}></div>\n              </Link>\n              <Link\n                href=\"/rules\"\n                className=\"relative px-4 py-2 text-white font-medium transition-all duration-300 group\"\n              >\n                <span className={`relative z-10 transition-colors duration-300 ${\n                  isActive(\"/rules\") ? \"text-neon-orange\" : \"group-hover:text-neon-orange\"\n                }`}>\n                  Rules\n                </span>\n\n                {/* Active state particle effects */}\n                {isActive(\"/rules\") && (\n                  <div className=\"absolute inset-0 opacity-100 transition-all duration-500\">\n                    {/* Floating particles */}\n                    <div className=\"absolute top-1 left-2 w-1 h-1 bg-neon-orange rounded-full animate-ping\"></div>\n                    <div className=\"absolute top-2 right-3 w-1.5 h-1.5 bg-yellow-400 rounded-full animate-pulse\"></div>\n                    <div className=\"absolute bottom-2 left-3 w-1 h-1 bg-neon-orange rounded-full animate-bounce\"></div>\n                    <div className=\"absolute bottom-1 right-2 w-1 h-1 bg-yellow-300 rounded-full animate-ping\" style={{animationDelay: '0.5s'}}></div>\n                    <div className=\"absolute top-3 left-1/2 w-0.5 h-0.5 bg-neon-orange rounded-full animate-pulse\" style={{animationDelay: '0.3s'}}></div>\n                  </div>\n                )}\n\n                <div className={`absolute inset-0 bg-gradient-to-r from-neon-orange/10 to-neon-red/10 rounded-lg transition-all duration-300 scale-95 ${\n                  isActive(\"/rules\") ? \"opacity-30 scale-100\" : \"opacity-0 group-hover:opacity-100 group-hover:scale-100\"\n                }`}></div>\n              </Link>\n              <Link\n                href=\"/vip\"\n                className=\"relative px-4 py-2 text-white font-medium transition-all duration-300 group overflow-hidden\"\n              >\n                {/* Purple heart with particles */}\n                <div className={`absolute inset-0 transition-all duration-500 ${\n                  isActive(\"/vip\") ? \"opacity-100\" : \"opacity-0 group-hover:opacity-100\"\n                }`}>\n                  {/* Floating particles */}\n                  <div className=\"absolute top-1 left-2 w-1 h-1 bg-purple-400 rounded-full animate-ping\"></div>\n                  <div className=\"absolute top-3 right-3 w-1.5 h-1.5 bg-pink-400 rounded-full animate-pulse\"></div>\n                  <div className=\"absolute bottom-2 left-1 w-1 h-1 bg-purple-300 rounded-full animate-bounce\"></div>\n                  <div className=\"absolute bottom-1 right-2 w-1 h-1 bg-pink-300 rounded-full animate-ping\" style={{animationDelay: '0.5s'}}></div>\n                  <div className=\"absolute top-2 left-1/2 w-0.5 h-0.5 bg-purple-500 rounded-full animate-pulse\" style={{animationDelay: '0.3s'}}></div>\n                  <div className=\"absolute bottom-3 right-1 w-0.5 h-0.5 bg-pink-500 rounded-full animate-bounce\" style={{animationDelay: '0.7s'}}></div>\n                </div>\n\n                {/* Purple gradient background */}\n                <div className={`absolute inset-0 bg-gradient-to-r from-purple-600/20 via-pink-500/20 to-purple-600/20 rounded-lg transition-all duration-300 scale-95 ${\n                  isActive(\"/vip\") ? \"opacity-100 scale-100\" : \"opacity-0 group-hover:opacity-100 group-hover:scale-100\"\n                }`}></div>\n\n                {/* Glow effect */}\n                <div className={`absolute inset-0 bg-gradient-to-r from-purple-500/30 to-pink-500/30 rounded-lg blur-sm transition-all duration-500 scale-110 ${\n                  isActive(\"/vip\") ? \"opacity-60\" : \"opacity-0 group-hover:opacity-60\"\n                }`}></div>\n\n                <span className={`relative z-10 transition-colors duration-300 flex items-center ${\n                  isActive(\"/vip\") ? \"text-purple-300\" : \"group-hover:text-purple-300\"\n                }`}>\n                  <i className={`fas fa-heart mr-2 transition-colors duration-300 ${\n                    isActive(\"/vip\")\n                      ? \"text-pink-400 animate-pulse\"\n                      : \"text-purple-400 group-hover:text-pink-400 group-hover:animate-pulse\"\n                  }`}></i>\n                  VIP\n                </span>\n\n              </Link>\n            </div>\n\n            {/* Enhanced Play Now Button */}\n            <Link\n              href=\"cfx.re/join/o57lj7\"\n              className=\"relative px-6 py-2.5 rounded-full font-medium transition-all duration-300 group overflow-hidden\"\n            >\n              {/* Animated background */}\n              <div className=\"absolute inset-0 bg-gradient-to-r from-neon-orange to-neon-red transition-all duration-300 group-hover:scale-105\"></div>\n              <div className=\"absolute inset-0 bg-gradient-to-r from-neon-orange-light to-neon-red opacity-0 group-hover:opacity-100 transition-all duration-300\"></div>\n\n              {/* Glow effect */}\n              <div className=\"absolute inset-0 rounded-full bg-gradient-to-r from-neon-orange to-neon-red blur-md opacity-0 group-hover:opacity-50 transition-all duration-300 scale-110\"></div>\n\n              {/* Button text */}\n              <span className=\"relative z-10 text-white font-semibold tracking-wide group-hover:text-black transition-colors duration-300\">\n                Play Now\n              </span>\n            </Link>\n          </div>\n\n          {/* Enhanced Mobile Menu Button */}\n          <button\n            className=\"md:hidden relative p-2 text-white focus:outline-none group transition-all duration-300\"\n            onClick={toggleMenu}\n          >\n            <div className=\"absolute inset-0 bg-gradient-to-r from-neon-orange/20 to-neon-red/20 rounded-lg opacity-0 group-hover:opacity-100 transition-all duration-300\"></div>\n            <svg\n              className={`w-6 h-6 relative z-10 transition-all duration-300 ${isMenuOpen ? 'rotate-90 text-neon-orange' : 'rotate-0'}`}\n              fill=\"none\"\n              stroke=\"currentColor\"\n              viewBox=\"0 0 24 24\"\n              xmlns=\"http://www.w3.org/2000/svg\"\n            >\n              {isMenuOpen ? (\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  strokeWidth={2}\n                  d=\"M6 18L18 6M6 6l12 12\"\n                />\n              ) : (\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  strokeWidth={2}\n                  d=\"M4 6h16M4 12h16M4 18h16\"\n                />\n              )}\n            </svg>\n          </button>\n        </div>\n\n        {/* Enhanced Mobile Menu */}\n        {isMenuOpen && (\n          <div className=\"md:hidden mt-6 relative animate-menu-slide-down\">\n            {/* Background with pattern */}\n            <div className=\"absolute inset-0 pattern-circuit opacity-20 rounded-xl\"></div>\n            <div className=\"relative bg-black/90 backdrop-blur-xl rounded-xl p-6 border border-neon-orange/40 shadow-lg shadow-neon-orange/20 overflow-hidden\">\n              {/* Particle effects background */}\n              <div className=\"absolute inset-0 opacity-30 pointer-events-none\">\n                <div className=\"absolute top-2 left-4 w-1 h-1 bg-neon-orange rounded-full animate-ping\"></div>\n                <div className=\"absolute top-4 right-6 w-1.5 h-1.5 bg-yellow-400 rounded-full animate-pulse\"></div>\n                <div className=\"absolute bottom-6 left-3 w-1 h-1 bg-neon-orange rounded-full animate-bounce\"></div>\n                <div className=\"absolute bottom-3 right-4 w-1 h-1 bg-yellow-300 rounded-full animate-ping\" style={{animationDelay: '0.5s'}}></div>\n                <div className=\"absolute top-1/2 left-1/2 w-0.5 h-0.5 bg-neon-orange rounded-full animate-pulse\" style={{animationDelay: '0.3s'}}></div>\n                <div className=\"absolute top-8 right-2 w-0.5 h-0.5 bg-yellow-500 rounded-full animate-bounce\" style={{animationDelay: '0.7s'}}></div>\n              </div>\n              <div className=\"flex flex-col space-y-4\">\n                <Link\n                  href=\"/\"\n                  className=\"relative px-4 py-3 text-white font-medium transition-all duration-300 group rounded-lg\"\n                  onClick={() => setIsMenuOpen(false)}\n                >\n                  <div className={`absolute inset-0 bg-gradient-to-r from-neon-orange/10 to-neon-red/10 rounded-lg transition-all duration-300 ${\n                    isActive(\"/\") ? \"opacity-30\" : \"opacity-0 group-hover:opacity-100\"\n                  }`}></div>\n                  <span className={`relative z-10 transition-colors duration-300 ${\n                    isActive(\"/\") ? \"text-neon-orange\" : \"group-hover:text-neon-orange\"\n                  }`}>\n                    Home\n                    {isActive(\"/\") && <span className=\"ml-2 text-neon-orange animate-pulse\">✦</span>}\n                  </span>\n                </Link>\n                <Link\n                  href=\"/about\"\n                  className=\"relative px-4 py-3 text-white font-medium transition-all duration-300 group rounded-lg\"\n                  onClick={() => setIsMenuOpen(false)}\n                >\n                  <div className={`absolute inset-0 bg-gradient-to-r from-neon-orange/10 to-neon-red/10 rounded-lg transition-all duration-300 ${\n                    isActive(\"/about\") ? \"opacity-30\" : \"opacity-0 group-hover:opacity-100\"\n                  }`}></div>\n                  <span className={`relative z-10 transition-colors duration-300 ${\n                    isActive(\"/about\") ? \"text-neon-orange\" : \"group-hover:text-neon-orange\"\n                  }`}>\n                    About\n                    {isActive(\"/about\") && <span className=\"ml-2 text-neon-orange animate-pulse\">✦</span>}\n                  </span>\n                </Link>\n                <Link\n                  href=\"/features\"\n                  className=\"relative px-4 py-3 text-white font-medium transition-all duration-300 group rounded-lg\"\n                  onClick={() => setIsMenuOpen(false)}\n                >\n                  <div className={`absolute inset-0 bg-gradient-to-r from-neon-orange/10 to-neon-red/10 rounded-lg transition-all duration-300 ${\n                    isActive(\"/features\") ? \"opacity-30\" : \"opacity-0 group-hover:opacity-100\"\n                  }`}></div>\n                  <span className={`relative z-10 transition-colors duration-300 ${\n                    isActive(\"/features\") ? \"text-neon-orange\" : \"group-hover:text-neon-orange\"\n                  }`}>\n                    Features\n                    {isActive(\"/features\") && <span className=\"ml-2 text-neon-orange animate-pulse\">✦</span>}\n                  </span>\n                </Link>\n                <Link\n                  href=\"/rules\"\n                  className=\"relative px-4 py-3 text-white font-medium transition-all duration-300 group rounded-lg\"\n                  onClick={() => setIsMenuOpen(false)}\n                >\n                  <div className={`absolute inset-0 bg-gradient-to-r from-neon-orange/10 to-neon-red/10 rounded-lg transition-all duration-300 ${\n                    isActive(\"/rules\") ? \"opacity-30\" : \"opacity-0 group-hover:opacity-100\"\n                  }`}></div>\n                  <span className={`relative z-10 transition-colors duration-300 ${\n                    isActive(\"/rules\") ? \"text-neon-orange\" : \"group-hover:text-neon-orange\"\n                  }`}>\n                    Rules\n                    {isActive(\"/rules\") && <span className=\"ml-2 text-neon-orange animate-pulse\">✦</span>}\n                  </span>\n                </Link>\n                <Link\n                  href=\"/vip\"\n                  className=\"relative px-4 py-3 text-white font-medium transition-all duration-300 group rounded-lg overflow-hidden\"\n                  onClick={() => setIsMenuOpen(false)}\n                >\n                  {/* Purple heart with particles */}\n                  <div className=\"absolute inset-0 opacity-0 group-hover:opacity-100 transition-all duration-500\">\n                    {/* Floating particles */}\n                    <div className=\"absolute top-2 left-3 w-1 h-1 bg-purple-400 rounded-full animate-ping\"></div>\n                    <div className=\"absolute top-4 right-4 w-1.5 h-1.5 bg-pink-400 rounded-full animate-pulse\"></div>\n                    <div className=\"absolute bottom-3 left-2 w-1 h-1 bg-purple-300 rounded-full animate-bounce\"></div>\n                    <div className=\"absolute bottom-2 right-3 w-1 h-1 bg-pink-300 rounded-full animate-ping\" style={{animationDelay: '0.5s'}}></div>\n                    <div className=\"absolute top-3 left-1/2 w-0.5 h-0.5 bg-purple-500 rounded-full animate-pulse\" style={{animationDelay: '0.3s'}}></div>\n                    <div className=\"absolute bottom-4 right-2 w-0.5 h-0.5 bg-pink-500 rounded-full animate-bounce\" style={{animationDelay: '0.7s'}}></div>\n                  </div>\n\n                  {/* Purple gradient background */}\n                  <div className=\"absolute inset-0 bg-gradient-to-r from-purple-600/20 via-pink-500/20 to-purple-600/20 rounded-lg opacity-0 group-hover:opacity-100 transition-all duration-300\"></div>\n\n                  {/* Glow effect */}\n                  <div className=\"absolute inset-0 bg-gradient-to-r from-purple-500/30 to-pink-500/30 rounded-lg blur-sm opacity-0 group-hover:opacity-60 transition-all duration-500 scale-110\"></div>\n\n                  <span className={`relative z-10 transition-colors duration-300 flex items-center ${\n                    isActive(\"/vip\") ? \"text-purple-300\" : \"group-hover:text-purple-300\"\n                  }`}>\n                    <i className={`fas fa-heart mr-2 transition-colors duration-300 ${\n                      isActive(\"/vip\")\n                        ? \"text-pink-400 animate-pulse\"\n                        : \"text-purple-400 group-hover:text-pink-400 group-hover:animate-pulse\"\n                    }`}></i>\n                    VIP\n                    {isActive(\"/vip\") && <span className=\"ml-2 text-purple-400 animate-pulse\">♥</span>}\n                  </span>\n                </Link>\n\n                {/* Divider */}\n                <div className=\"h-px bg-gradient-to-r from-transparent via-neon-orange/50 to-transparent my-2\"></div>\n\n                {/* Enhanced Mobile Play Now Button */}\n                <Link\n                  href=\"cfx.re/join/o57lj7\"\n                  className=\"relative px-6 py-3 rounded-full font-medium transition-all duration-300 group overflow-hidden text-center\"\n                  onClick={() => setIsMenuOpen(false)}\n                >\n                  {/* Animated background */}\n                  <div className=\"absolute inset-0 bg-gradient-to-r from-neon-orange to-neon-red transition-all duration-300\"></div>\n                  <div className=\"absolute inset-0 bg-gradient-to-r from-neon-orange-light to-neon-red opacity-0 group-hover:opacity-100 transition-all duration-300\"></div>\n\n                  {/* Glow effect */}\n                  <div className=\"absolute inset-0 rounded-full bg-gradient-to-r from-neon-orange to-neon-red blur-sm opacity-0 group-hover:opacity-60 transition-all duration-300\"></div>\n\n                  {/* Button text */}\n                  <span className=\"relative z-10 text-white font-semibold tracking-wide group-hover:text-black transition-colors duration-300\">\n                    Play Now\n                  </span>\n                </Link>\n              </div>\n            </div>\n          </div>\n        )}\n      </div>\n    </nav>\n  );\n};\n\nexport default Navbar;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAOA,MAAM,SAAS;;IACb,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,+CAA+C;IAC/C,MAAM,WAAW,CAAC;QAChB,IAAI,SAAS,KAAK;YAChB,OAAO,aAAa;QACtB;QACA,OAAO,SAAS,UAAU,CAAC;IAC7B;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACR,MAAM;iDAAe;oBACnB,IAAI,OAAO,OAAO,GAAG,IAAI;wBACvB,YAAY;oBACd,OAAO;wBACL,YAAY;oBACd;gBACF;;YAEA,OAAO,gBAAgB,CAAC,UAAU;YAClC;oCAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;;QACpD;2BAAG,EAAE;IAEL,MAAM,aAAa;QACjB,cAAc,CAAC;IACjB;IAEA,qBACE,6LAAC;QACC,WAAW,CAAC,2DAA2D,EACrE,WACI,sEACA,uBACJ;;0BAGF,6LAAC;gBAAI,WAAU;;;;;;0BAEf,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;;kDACvB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC,gIAAA,CAAA,UAAK;gDACJ,KAAI;gDACJ,KAAI;gDACJ,OAAO;gDACP,QAAQ;gDACR,WAAU;gDACV,QAAQ;;;;;;;;;;;;kDAGZ,6LAAC;wCAAK,WAAU;kDAAoH;;;;;;;;;;;;0CAMtI,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;;kEAEV,6LAAC;wDAAK,WAAW,CAAC,6CAA6C,EAC7D,SAAS,OAAO,qBAAqB,gCACrC;kEAAE;;;;;;oDAKH,SAAS,sBACR,6LAAC;wDAAI,WAAU;;0EAEb,6LAAC;gEAAI,WAAU;;;;;;0EACf,6LAAC;gEAAI,WAAU;;;;;;0EACf,6LAAC;gEAAI,WAAU;;;;;;0EACf,6LAAC;gEAAI,WAAU;gEAA4E,OAAO;oEAAC,gBAAgB;gEAAM;;;;;;0EACzH,6LAAC;gEAAI,WAAU;gEAAgF,OAAO;oEAAC,gBAAgB;gEAAM;;;;;;;;;;;;kEAIjI,6LAAC;wDAAI,WAAW,CAAC,qHAAqH,EACpI,SAAS,OAAO,yBAAyB,2DACzC;;;;;;;;;;;;0DAEJ,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;;kEAEV,6LAAC;wDAAK,WAAW,CAAC,6CAA6C,EAC7D,SAAS,YAAY,qBAAqB,gCAC1C;kEAAE;;;;;;oDAKH,SAAS,2BACR,6LAAC;wDAAI,WAAU;;0EAEb,6LAAC;gEAAI,WAAU;;;;;;0EACf,6LAAC;gEAAI,WAAU;;;;;;0EACf,6LAAC;gEAAI,WAAU;;;;;;0EACf,6LAAC;gEAAI,WAAU;gEAA4E,OAAO;oEAAC,gBAAgB;gEAAM;;;;;;0EACzH,6LAAC;gEAAI,WAAU;gEAAgF,OAAO;oEAAC,gBAAgB;gEAAM;;;;;;;;;;;;kEAIjI,6LAAC;wDAAI,WAAW,CAAC,qHAAqH,EACpI,SAAS,YAAY,yBAAyB,2DAC9C;;;;;;;;;;;;0DAEJ,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;;kEAEV,6LAAC;wDAAK,WAAW,CAAC,6CAA6C,EAC7D,SAAS,eAAe,qBAAqB,gCAC7C;kEAAE;;;;;;oDAKH,SAAS,8BACR,6LAAC;wDAAI,WAAU;;0EAEb,6LAAC;gEAAI,WAAU;;;;;;0EACf,6LAAC;gEAAI,WAAU;;;;;;0EACf,6LAAC;gEAAI,WAAU;;;;;;0EACf,6LAAC;gEAAI,WAAU;gEAA4E,OAAO;oEAAC,gBAAgB;gEAAM;;;;;;0EACzH,6LAAC;gEAAI,WAAU;gEAAgF,OAAO;oEAAC,gBAAgB;gEAAM;;;;;;;;;;;;kEAIjI,6LAAC;wDAAI,WAAW,CAAC,qHAAqH,EACpI,SAAS,eAAe,yBAAyB,2DACjD;;;;;;;;;;;;0DAEJ,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;;kEAEV,6LAAC;wDAAK,WAAW,CAAC,6CAA6C,EAC7D,SAAS,YAAY,qBAAqB,gCAC1C;kEAAE;;;;;;oDAKH,SAAS,2BACR,6LAAC;wDAAI,WAAU;;0EAEb,6LAAC;gEAAI,WAAU;;;;;;0EACf,6LAAC;gEAAI,WAAU;;;;;;0EACf,6LAAC;gEAAI,WAAU;;;;;;0EACf,6LAAC;gEAAI,WAAU;gEAA4E,OAAO;oEAAC,gBAAgB;gEAAM;;;;;;0EACzH,6LAAC;gEAAI,WAAU;gEAAgF,OAAO;oEAAC,gBAAgB;gEAAM;;;;;;;;;;;;kEAIjI,6LAAC;wDAAI,WAAW,CAAC,qHAAqH,EACpI,SAAS,YAAY,yBAAyB,2DAC9C;;;;;;;;;;;;0DAEJ,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;;kEAGV,6LAAC;wDAAI,WAAW,CAAC,6CAA6C,EAC5D,SAAS,UAAU,gBAAgB,qCACnC;;0EAEA,6LAAC;gEAAI,WAAU;;;;;;0EACf,6LAAC;gEAAI,WAAU;;;;;;0EACf,6LAAC;gEAAI,WAAU;;;;;;0EACf,6LAAC;gEAAI,WAAU;gEAA0E,OAAO;oEAAC,gBAAgB;gEAAM;;;;;;0EACvH,6LAAC;gEAAI,WAAU;gEAA+E,OAAO;oEAAC,gBAAgB;gEAAM;;;;;;0EAC5H,6LAAC;gEAAI,WAAU;gEAAgF,OAAO;oEAAC,gBAAgB;gEAAM;;;;;;;;;;;;kEAI/H,6LAAC;wDAAI,WAAW,CAAC,sIAAsI,EACrJ,SAAS,UAAU,0BAA0B,2DAC7C;;;;;;kEAGF,6LAAC;wDAAI,WAAW,CAAC,6HAA6H,EAC5I,SAAS,UAAU,eAAe,oCAClC;;;;;;kEAEF,6LAAC;wDAAK,WAAW,CAAC,+DAA+D,EAC/E,SAAS,UAAU,oBAAoB,+BACvC;;0EACA,6LAAC;gEAAE,WAAW,CAAC,iDAAiD,EAC9D,SAAS,UACL,gCACA,uEACJ;;;;;;4DAAM;;;;;;;;;;;;;;;;;;;kDAQd,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;;0DAGV,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAI,WAAU;;;;;;0DAGf,6LAAC;gDAAI,WAAU;;;;;;0DAGf,6LAAC;gDAAK,WAAU;0DAA6G;;;;;;;;;;;;;;;;;;0CAOjI,6LAAC;gCACC,WAAU;gCACV,SAAS;;kDAET,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCACC,WAAW,CAAC,kDAAkD,EAAE,aAAa,+BAA+B,YAAY;wCACxH,MAAK;wCACL,QAAO;wCACP,SAAQ;wCACR,OAAM;kDAEL,2BACC,6LAAC;4CACC,eAAc;4CACd,gBAAe;4CACf,aAAa;4CACb,GAAE;;;;;iEAGJ,6LAAC;4CACC,eAAc;4CACd,gBAAe;4CACf,aAAa;4CACb,GAAE;;;;;;;;;;;;;;;;;;;;;;;oBAQX,4BACC,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAI,WAAU;gDAA4E,OAAO;oDAAC,gBAAgB;gDAAM;;;;;;0DACzH,6LAAC;gDAAI,WAAU;gDAAkF,OAAO;oDAAC,gBAAgB;gDAAM;;;;;;0DAC/H,6LAAC;gDAAI,WAAU;gDAA+E,OAAO;oDAAC,gBAAgB;gDAAM;;;;;;;;;;;;kDAE9H,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;gDACV,SAAS,IAAM,cAAc;;kEAE7B,6LAAC;wDAAI,WAAW,CAAC,4GAA4G,EAC3H,SAAS,OAAO,eAAe,qCAC/B;;;;;;kEACF,6LAAC;wDAAK,WAAW,CAAC,6CAA6C,EAC7D,SAAS,OAAO,qBAAqB,gCACrC;;4DAAE;4DAED,SAAS,sBAAQ,6LAAC;gEAAK,WAAU;0EAAsC;;;;;;;;;;;;;;;;;;0DAG5E,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;gDACV,SAAS,IAAM,cAAc;;kEAE7B,6LAAC;wDAAI,WAAW,CAAC,4GAA4G,EAC3H,SAAS,YAAY,eAAe,qCACpC;;;;;;kEACF,6LAAC;wDAAK,WAAW,CAAC,6CAA6C,EAC7D,SAAS,YAAY,qBAAqB,gCAC1C;;4DAAE;4DAED,SAAS,2BAAa,6LAAC;gEAAK,WAAU;0EAAsC;;;;;;;;;;;;;;;;;;0DAGjF,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;gDACV,SAAS,IAAM,cAAc;;kEAE7B,6LAAC;wDAAI,WAAW,CAAC,4GAA4G,EAC3H,SAAS,eAAe,eAAe,qCACvC;;;;;;kEACF,6LAAC;wDAAK,WAAW,CAAC,6CAA6C,EAC7D,SAAS,eAAe,qBAAqB,gCAC7C;;4DAAE;4DAED,SAAS,8BAAgB,6LAAC;gEAAK,WAAU;0EAAsC;;;;;;;;;;;;;;;;;;0DAGpF,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;gDACV,SAAS,IAAM,cAAc;;kEAE7B,6LAAC;wDAAI,WAAW,CAAC,4GAA4G,EAC3H,SAAS,YAAY,eAAe,qCACpC;;;;;;kEACF,6LAAC;wDAAK,WAAW,CAAC,6CAA6C,EAC7D,SAAS,YAAY,qBAAqB,gCAC1C;;4DAAE;4DAED,SAAS,2BAAa,6LAAC;gEAAK,WAAU;0EAAsC;;;;;;;;;;;;;;;;;;0DAGjF,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;gDACV,SAAS,IAAM,cAAc;;kEAG7B,6LAAC;wDAAI,WAAU;;0EAEb,6LAAC;gEAAI,WAAU;;;;;;0EACf,6LAAC;gEAAI,WAAU;;;;;;0EACf,6LAAC;gEAAI,WAAU;;;;;;0EACf,6LAAC;gEAAI,WAAU;gEAA0E,OAAO;oEAAC,gBAAgB;gEAAM;;;;;;0EACvH,6LAAC;gEAAI,WAAU;gEAA+E,OAAO;oEAAC,gBAAgB;gEAAM;;;;;;0EAC5H,6LAAC;gEAAI,WAAU;gEAAgF,OAAO;oEAAC,gBAAgB;gEAAM;;;;;;;;;;;;kEAI/H,6LAAC;wDAAI,WAAU;;;;;;kEAGf,6LAAC;wDAAI,WAAU;;;;;;kEAEf,6LAAC;wDAAK,WAAW,CAAC,+DAA+D,EAC/E,SAAS,UAAU,oBAAoB,+BACvC;;0EACA,6LAAC;gEAAE,WAAW,CAAC,iDAAiD,EAC9D,SAAS,UACL,gCACA,uEACJ;;;;;;4DAAM;4DAEP,SAAS,yBAAW,6LAAC;gEAAK,WAAU;0EAAqC;;;;;;;;;;;;;;;;;;0DAK9E,6LAAC;gDAAI,WAAU;;;;;;0DAGf,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;gDACV,SAAS,IAAM,cAAc;;kEAG7B,6LAAC;wDAAI,WAAU;;;;;;kEACf,6LAAC;wDAAI,WAAU;;;;;;kEAGf,6LAAC;wDAAI,WAAU;;;;;;kEAGf,6LAAC;wDAAK,WAAU;kEAA6G;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAW/I;GAhZM;;QAGa,qIAAA,CAAA,cAAW;;;KAHxB;uCAkZS"}}, {"offset": {"line": 1047, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1053, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/TGRS%20Website/tgrs-website/src/components/Footer.tsx"], "sourcesContent": ["import Link from \"next/link\";\nimport Image from \"next/image\";\n\nconst Footer = () => {\n  return (\n    <footer className=\"bg-black/50 pattern-dots-footer border-t border-neon-orange/30 pt-12 pb-6 relative\">\n      <div className=\"container mx-auto px-4 md:px-6\">\n        <div className=\"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-6 sm:gap-8 mb-6 sm:mb-8\">\n          {/* Logo and About */}\n          <div className=\"col-span-1 sm:col-span-2 md:col-span-1\">\n            <Link href=\"/\" className=\"flex items-center mb-4\">\n              <div className=\"relative h-8 w-8 sm:h-10 sm:w-10 mr-3\">\n                <Image\n                  src=\"/assets/tgrs-logo.png\"\n                  alt=\"TGRS Logo\"\n                  width={40}\n                  height={40}\n                  className=\"w-full h-full object-contain\"\n                />\n              </div>\n              <span className=\"text-white font-display font-bold text-lg sm:text-xl tracking-wider\">\n                TGRS\n              </span>\n            </Link>\n            <p className=\"text-gray-400 text-xs sm:text-sm mb-4\">\n              The premier Telugu community FiveM roleplay server with immersive experiences and unique features.\n            </p>\n            <div className=\"flex space-x-3 sm:space-x-4\">\n              {/* YouTube */}\n              <a href=\"https://www.youtube.com/@GTA5RPTGRSCITY\" target=\"_blank\" rel=\"noopener noreferrer\" className=\"text-white hover:text-red-500 transition-colors\" aria-label=\"YouTube\">\n                <svg className=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\" aria-hidden=\"true\">\n                  <path d=\"M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z\"/>\n                </svg>\n              </a>\n              {/* Discord */}\n              <a href=\"https://discord.gg/GAMravHDnB\" target=\"_blank\" rel=\"noopener noreferrer\" className=\"text-white hover:text-[#5865F2] transition-colors\" aria-label=\"Discord\">\n                <svg className=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\" aria-hidden=\"true\">\n                  <path d=\"M20.317 4.3698a19.7913 19.7913 0 00-4.8851-1.5152.0741.0741 0 00-.0785.0371c-.211.3753-.4447.8648-.6083 1.2495-1.8447-.2762-3.68-.2762-5.4868 0-.1636-.3933-.4058-.8742-.6177-1.2495a.077.077 0 00-.0785-.037 19.7363 19.7363 0 00-4.8852 1.515.0699.0699 0 00-.0321.0277C.5334 9.0458-.319 13.5799.0992 18.0578a.0824.0824 0 00.0312.0561c2.0528 1.5076 4.0413 2.4228 5.9929 3.0294a.0777.0777 0 00.0842-.0276c.4616-.6304.8731-1.2952 1.226-1.9942a.076.076 0 00-.0416-.1057c-.6528-.2476-1.2743-.5495-1.8722-.8923a.077.077 0 01-.0076-.1277c.1258-.0943.2517-.1923.3718-.2914a.0743.0743 0 01.0776-.0105c3.9278 1.7933 8.18 1.7933 12.0614 0a.0739.0739 0 01.0785.0095c.1202.099.246.1981.3728.2924a.077.077 0 01-.0066.1276 12.2986 12.2986 0 01-1.873.8914.0766.0766 0 00-.0407.1067c.3604.698.7719 1.3628 1.225 1.9932a.076.076 0 00.0842.0286c1.961-.6067 3.9495-1.5219 6.0023-3.0294a.077.077 0 00.0313-.0552c.5004-5.177-.8382-9.6739-3.5485-13.6604a.061.061 0 00-.0312-.0286zM8.02 15.3312c-1.1825 0-2.1569-1.0857-2.1569-2.419 0-1.3332.9555-2.4189 2.157-2.4189 1.2108 0 2.1757 1.0952 2.1568 2.419-.0002 1.3332-.9555 2.4189-2.1569 2.4189zm7.9748 0c-1.1825 0-2.1569-1.0857-2.1569-2.419 0-1.3332.9554-2.4189 2.1569-2.4189 1.2108 0 2.1757 1.0952 2.1568 2.419 0 1.3332-.9554 2.4189-2.1568 2.4189Z\"/>\n                </svg>\n              </a>\n              {/* Website */}\n              <a href=\"/\" className=\"text-white hover:text-neon-orange transition-colors\" aria-label=\"Website\">\n                <svg className=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\" aria-hidden=\"true\">\n                  <path d=\"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-1 17.93c-3.94-.49-7-3.85-7-7.93 0-.62.08-1.21.21-1.79L9 15v1c0 1.1.9 2 2 2v1.93zm6.9-2.54c-.26-.81-1-1.39-1.9-1.39h-1v-3c0-.55-.45-1-1-1H8v-2h2c.55 0 1-.45 1-1V7h2c1.1 0 2-.9 2-2v-.41c2.93 1.19 5 4.06 5 7.41 0 2.08-.8 3.97-2.1 5.39z\"/>\n                </svg>\n              </a>\n\n            </div>\n          </div>\n\n          {/* Quick Links */}\n          <div className=\"col-span-1\">\n            <h3 className=\"text-white font-display font-semibold text-base sm:text-lg mb-3 sm:mb-4\">Quick Links</h3>\n            <ul className=\"space-y-1 sm:space-y-2\">\n              <li>\n                <Link href=\"/\" className=\"text-gray-400 hover:text-neon-orange transition-colors text-xs sm:text-sm\">\n                  Home\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/about\" className=\"text-gray-400 hover:text-neon-orange transition-colors text-xs sm:text-sm\">\n                  About\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/features\" className=\"text-gray-400 hover:text-neon-orange transition-colors text-xs sm:text-sm\">\n                  Features\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/rules\" className=\"text-gray-400 hover:text-neon-orange transition-colors text-xs sm:text-sm\">\n                  Rules\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/contact\" className=\"text-gray-400 hover:text-neon-orange transition-colors text-xs sm:text-sm\">\n                  Contact\n                </Link>\n              </li>\n            </ul>\n          </div>\n\n          {/* Server Info */}\n          <div className=\"col-span-1\">\n            <h3 className=\"text-white font-display font-semibold text-base sm:text-lg mb-3 sm:mb-4\">Server Info</h3>\n            <ul className=\"space-y-1 sm:space-y-2\">\n              <li className=\"text-gray-400 text-xs sm:text-sm\">\n                <span className=\"text-neon-orange mr-2\">•</span> 24/7 Uptime\n              </li>\n              <li className=\"text-gray-400 text-xs sm:text-sm\">\n                <span className=\"text-neon-orange mr-2\">•</span> Active Staff\n              </li>\n              <li className=\"text-gray-400 text-xs sm:text-sm\">\n                <span className=\"text-neon-orange mr-2\">•</span> Custom Scripts\n              </li>\n              <li className=\"text-gray-400 text-xs sm:text-sm\">\n                <span className=\"text-neon-orange mr-2\">•</span> Regular Updates\n              </li>\n              <li className=\"text-gray-400 text-xs sm:text-sm\">\n                <span className=\"text-neon-orange mr-2\">•</span> Community Events\n              </li>\n            </ul>\n          </div>\n\n          {/* Discord */}\n          <div className=\"col-span-1 sm:col-span-2 md:col-span-1\">\n            <h3 className=\"text-white font-display font-semibold text-base sm:text-lg mb-3 sm:mb-4\">Join Our Discord</h3>\n            <p className=\"text-gray-400 text-xs sm:text-sm mb-3 sm:mb-4\">\n              Join our Discord community for server updates, events, and to connect with other players.\n            </p>\n            <a\n              href=\"https://discord.gg/GAMravHDnB\"\n              target=\"_blank\"\n              rel=\"noopener noreferrer\"\n              className=\"px-3 py-2 sm:px-4 sm:py-2 rounded-md bg-[#5865F2] hover:bg-[#4752C4] text-white font-medium transition-colors flex items-center justify-center space-x-2 w-full sm:w-auto text-xs sm:text-sm\"\n            >\n              <svg className=\"w-4 h-4 sm:w-5 sm:h-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path d=\"M20.317 4.3698a19.7913 19.7913 0 00-4.8851-1.5152.0741.0741 0 00-.0785.0371c-.211.3753-.4447.8648-.6083 1.2495-1.8447-.2762-3.68-.2762-5.4868 0-.1636-.3933-.4058-.8742-.6177-1.2495a.077.077 0 00-.0785-.037 19.7363 19.7363 0 00-4.8852 1.515.0699.0699 0 00-.0321.0277C.5334 9.0458-.319 13.5799.0992 18.0578a.0824.0824 0 00.0312.0561c2.0528 1.5076 4.0413 2.4228 5.9929 3.0294a.0777.0777 0 00.0842-.0276c.4616-.6304.8731-1.2952 1.226-1.9942a.076.076 0 00-.0416-.1057c-.6528-.2476-1.2743-.5495-1.8722-.8923a.077.077 0 01-.0076-.1277c.1258-.0943.2517-.1923.3718-.2914a.0743.0743 0 01.0776-.0105c3.9278 1.7933 8.18 1.7933 12.0614 0a.0739.0739 0 01.0785.0095c.1202.099.246.1981.3728.2924a.077.077 0 01-.0066.1276 12.2986 12.2986 0 01-1.873.8914.0766.0766 0 00-.0407.1067c.3604.698.7719 1.3628 1.225 1.9932a.076.076 0 00.0842.0286c1.961-.6067 3.9495-1.5219 6.0023-3.0294a.077.077 0 00.0313-.0552c.5004-5.177-.8382-9.6739-3.5485-13.6604a.061.061 0 00-.0312-.0286zM8.02 15.3312c-1.1825 0-2.1569-1.0857-2.1569-2.419 0-1.3332.9555-2.4189 2.157-2.4189 1.2108 0 2.1757 1.0952 2.1568 2.419 0 1.3332-.9555 2.4189-2.1569 2.4189zm7.9748 0c-1.1825 0-2.1569-1.0857-2.1569-2.419 0-1.3332.9554-2.4189 2.1569-2.4189 1.2108 0 2.1757 1.0952 2.1568 2.419 0 1.3332-.946 2.4189-2.1568 2.4189Z\"></path>\n              </svg>\n              <span>Join Discord</span>\n            </a>\n          </div>\n        </div>\n\n        {/* Copyright */}\n        <div className=\"border-t border-gray-800 pt-4 sm:pt-6 mt-4 sm:mt-6 text-center\">\n          <p className=\"text-gray-500 text-xs sm:text-sm\">\n            &copy; {new Date().getFullYear()} TGRS - Telugu Gaming Roleplay Server. All rights reserved.\n          </p>\n        </div>\n      </div>\n    </footer>\n  );\n};\n\nexport default Footer;\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAEA,MAAM,SAAS;IACb,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAI,WAAU;;sDACvB,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;gDACJ,KAAI;gDACJ,KAAI;gDACJ,OAAO;gDACP,QAAQ;gDACR,WAAU;;;;;;;;;;;sDAGd,6LAAC;4CAAK,WAAU;sDAAsE;;;;;;;;;;;;8CAIxF,6LAAC;oCAAE,WAAU;8CAAwC;;;;;;8CAGrD,6LAAC;oCAAI,WAAU;;sDAEb,6LAAC;4CAAE,MAAK;4CAA0C,QAAO;4CAAS,KAAI;4CAAsB,WAAU;4CAAkD,cAAW;sDACjK,cAAA,6LAAC;gDAAI,WAAU;gDAAU,MAAK;gDAAe,SAAQ;gDAAY,eAAY;0DAC3E,cAAA,6LAAC;oDAAK,GAAE;;;;;;;;;;;;;;;;sDAIZ,6LAAC;4CAAE,MAAK;4CAAgC,QAAO;4CAAS,KAAI;4CAAsB,WAAU;4CAAoD,cAAW;sDACzJ,cAAA,6LAAC;gDAAI,WAAU;gDAAU,MAAK;gDAAe,SAAQ;gDAAY,eAAY;0DAC3E,cAAA,6LAAC;oDAAK,GAAE;;;;;;;;;;;;;;;;sDAIZ,6LAAC;4CAAE,MAAK;4CAAI,WAAU;4CAAsD,cAAW;sDACrF,cAAA,6LAAC;gDAAI,WAAU;gDAAU,MAAK;gDAAe,SAAQ;gDAAY,eAAY;0DAC3E,cAAA,6LAAC;oDAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQhB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAA0E;;;;;;8CACxF,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAI,WAAU;0DAA4E;;;;;;;;;;;sDAIvG,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAS,WAAU;0DAA4E;;;;;;;;;;;sDAI5G,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAY,WAAU;0DAA4E;;;;;;;;;;;sDAI/G,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAS,WAAU;0DAA4E;;;;;;;;;;;sDAI5G,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;0DAA4E;;;;;;;;;;;;;;;;;;;;;;;sCAQlH,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAA0E;;;;;;8CACxF,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;oDAAK,WAAU;8DAAwB;;;;;;gDAAQ;;;;;;;sDAElD,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;oDAAK,WAAU;8DAAwB;;;;;;gDAAQ;;;;;;;sDAElD,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;oDAAK,WAAU;8DAAwB;;;;;;gDAAQ;;;;;;;sDAElD,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;oDAAK,WAAU;8DAAwB;;;;;;gDAAQ;;;;;;;sDAElD,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;oDAAK,WAAU;8DAAwB;;;;;;gDAAQ;;;;;;;;;;;;;;;;;;;sCAMtD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAA0E;;;;;;8CACxF,6LAAC;oCAAE,WAAU;8CAAgD;;;;;;8CAG7D,6LAAC;oCACC,MAAK;oCACL,QAAO;oCACP,KAAI;oCACJ,WAAU;;sDAEV,6LAAC;4CAAI,WAAU;4CAAwB,MAAK;4CAAe,SAAQ;sDACjE,cAAA,6LAAC;gDAAK,GAAE;;;;;;;;;;;sDAEV,6LAAC;sDAAK;;;;;;;;;;;;;;;;;;;;;;;;8BAMZ,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAE,WAAU;;4BAAmC;4BACtC,IAAI,OAAO,WAAW;4BAAG;;;;;;;;;;;;;;;;;;;;;;;AAM7C;KAlIM;uCAoIS"}}, {"offset": {"line": 1536, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1542, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/TGRS%20Website/tgrs-website/src/components/FeatureCard.tsx"], "sourcesContent": ["import React from \"react\";\n\ninterface FeatureCardProps {\n  title: string;\n  description: string;\n  icon?: React.ReactNode;\n  className?: string;\n  tiltDirection?: \"left\" | \"right\" | \"none\";\n  pattern?: \"dots\" | \"grid\" | \"hexagon\" | \"circuit\" | \"none\";\n}\n\nconst FeatureCard = ({\n  title,\n  description,\n  icon,\n  className = \"\",\n  tiltDirection = \"none\",\n  pattern = \"circuit\",\n}: FeatureCardProps) => {\n  const getTiltClass = () => {\n    switch (tiltDirection) {\n      case \"left\":\n        return \"card-tilt-left\";\n      case \"right\":\n        return \"card-tilt-right\";\n      default:\n        return \"\";\n    }\n  };\n\n  const getPatternClass = () => {\n    switch (pattern) {\n      case \"dots\":\n        return \"pattern-dots\";\n      case \"grid\":\n        return \"pattern-grid\";\n      case \"hexagon\":\n        return \"pattern-hexagon\";\n      case \"circuit\":\n        return \"pattern-circuit\";\n      default:\n        return \"\";\n    }\n  };\n\n  return (\n    <div className={`glass-enhanced rounded-lg p-3 sm:p-4 md:p-5 border border-neon-orange/30 hover:border-neon-orange/70 transition-all duration-500 hover:shadow-neon-strong group relative h-full flex flex-col ${getTiltClass()} ${getPatternClass()} ${className}`}>\n      {/* Background overlay for pattern */}\n      <div className=\"absolute inset-0 bg-black/20 rounded-xl\"></div>\n\n      {/* Content */}\n      <div className=\"relative z-10 flex flex-col h-full\">\n        {icon && (\n          <div className=\"mb-6 sm:mb-4 md:mb-3 text-neon-orange group-hover:text-white transition-all duration-300 relative icon-container\">\n            <div className=\"w-6 h-6 sm:w-7 sm:h-7 md:w-8 md:h-8 relative transform group-hover:scale-105 transition-transform duration-300 origin-center\">\n              {/* Animated particles around the icon */}\n              <div className=\"absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-500 pointer-events-none\">\n                {/* Top particles */}\n                <div className=\"absolute w-1 h-1 bg-neon-orange rounded-full animate-ping\" style={{top: '-4px', left: '20%', animationDelay: '0s'}}></div>\n                <div className=\"absolute w-0.5 h-0.5 bg-yellow-400 rounded-full animate-pulse\" style={{top: '-2px', right: '25%', animationDelay: '0.4s'}}></div>\n\n                {/* Right particles */}\n                <div className=\"absolute w-1 h-1 bg-orange-400 rounded-full animate-ping\" style={{right: '-4px', top: '30%', animationDelay: '0.2s'}}></div>\n                <div className=\"absolute w-0.5 h-0.5 bg-yellow-300 rounded-full animate-pulse\" style={{right: '-2px', bottom: '35%', animationDelay: '0.7s'}}></div>\n\n                {/* Bottom particles */}\n                <div className=\"absolute w-1 h-1 bg-neon-orange rounded-full animate-ping\" style={{bottom: '-4px', right: '20%', animationDelay: '0.5s'}}></div>\n                <div className=\"absolute w-0.5 h-0.5 bg-orange-500 rounded-full animate-pulse\" style={{bottom: '-2px', left: '30%', animationDelay: '0.9s'}}></div>\n\n                {/* Left particles */}\n                <div className=\"absolute w-1 h-1 bg-yellow-400 rounded-full animate-ping\" style={{left: '-4px', bottom: '25%', animationDelay: '0.3s'}}></div>\n                <div className=\"absolute w-0.5 h-0.5 bg-neon-orange rounded-full animate-pulse\" style={{left: '-2px', top: '40%', animationDelay: '0.6s'}}></div>\n              </div>\n              <div className=\"w-full h-full relative z-10\">\n                {icon}\n              </div>\n            </div>\n          </div>\n        )}\n        <h3 className=\"text-sm sm:text-base md:text-lg font-display font-bold text-white mb-1 sm:mb-2 group-hover:text-neon-orange transition-colors duration-300\">\n          {title}\n        </h3>\n        <p className=\"text-xs sm:text-sm md:text-base text-gray-400 group-hover:text-gray-200 transition-colors duration-300 leading-relaxed flex-grow\">\n          {description}\n        </p>\n      </div>\n\n      {/* Decorative corner elements */}\n      <div className=\"absolute top-2 right-2 w-3 h-3 border-t-2 border-r-2 border-neon-orange/30 group-hover:border-neon-orange transition-colors duration-300 z-20\"></div>\n      <div className=\"absolute bottom-2 left-2 w-3 h-3 border-b-2 border-l-2 border-neon-orange/30 group-hover:border-neon-orange transition-colors duration-300 z-20\"></div>\n    </div>\n  );\n};\n\nexport default FeatureCard;\n"], "names": [], "mappings": ";;;;;AAWA,MAAM,cAAc,CAAC,EACnB,KAAK,EACL,WAAW,EACX,IAAI,EACJ,YAAY,EAAE,EACd,gBAAgB,MAAM,EACtB,UAAU,SAAS,EACF;IACjB,MAAM,eAAe;QACnB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,kBAAkB;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAC,8LAA8L,EAAE,eAAe,CAAC,EAAE,kBAAkB,CAAC,EAAE,WAAW;;0BAEjQ,6LAAC;gBAAI,WAAU;;;;;;0BAGf,6LAAC;gBAAI,WAAU;;oBACZ,sBACC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;;sDAEb,6LAAC;4CAAI,WAAU;4CAA4D,OAAO;gDAAC,KAAK;gDAAQ,MAAM;gDAAO,gBAAgB;4CAAI;;;;;;sDACjI,6LAAC;4CAAI,WAAU;4CAAgE,OAAO;gDAAC,KAAK;gDAAQ,OAAO;gDAAO,gBAAgB;4CAAM;;;;;;sDAGxI,6LAAC;4CAAI,WAAU;4CAA2D,OAAO;gDAAC,OAAO;gDAAQ,KAAK;gDAAO,gBAAgB;4CAAM;;;;;;sDACnI,6LAAC;4CAAI,WAAU;4CAAgE,OAAO;gDAAC,OAAO;gDAAQ,QAAQ;gDAAO,gBAAgB;4CAAM;;;;;;sDAG3I,6LAAC;4CAAI,WAAU;4CAA4D,OAAO;gDAAC,QAAQ;gDAAQ,OAAO;gDAAO,gBAAgB;4CAAM;;;;;;sDACvI,6LAAC;4CAAI,WAAU;4CAAgE,OAAO;gDAAC,QAAQ;gDAAQ,MAAM;gDAAO,gBAAgB;4CAAM;;;;;;sDAG1I,6LAAC;4CAAI,WAAU;4CAA2D,OAAO;gDAAC,MAAM;gDAAQ,QAAQ;gDAAO,gBAAgB;4CAAM;;;;;;sDACrI,6LAAC;4CAAI,WAAU;4CAAiE,OAAO;gDAAC,MAAM;gDAAQ,KAAK;gDAAO,gBAAgB;4CAAM;;;;;;;;;;;;8CAE1I,6LAAC;oCAAI,WAAU;8CACZ;;;;;;;;;;;;;;;;;kCAKT,6LAAC;wBAAG,WAAU;kCACX;;;;;;kCAEH,6LAAC;wBAAE,WAAU;kCACV;;;;;;;;;;;;0BAKL,6LAAC;gBAAI,WAAU;;;;;;0BACf,6LAAC;gBAAI,WAAU;;;;;;;;;;;;AAGrB;KAjFM;uCAmFS"}}, {"offset": {"line": 1764, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1770, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/TGRS%20Website/tgrs-website/src/components/Button.tsx"], "sourcesContent": ["import React from \"react\";\nimport Link from \"next/link\";\n\ninterface ButtonProps {\n  children: React.ReactNode;\n  href?: string;\n  variant?: \"primary\" | \"secondary\" | \"outline\" | \"ghost\";\n  size?: \"sm\" | \"md\" | \"lg\";\n  className?: string;\n  onClick?: () => void;\n}\n\nconst Button = ({\n  children,\n  href,\n  variant = \"primary\",\n  size = \"md\",\n  className = \"\",\n  onClick,\n}: ButtonProps) => {\n  const baseStyles = \"inline-flex items-center justify-center font-medium transition-all duration-300 rounded-full\";\n\n  const variantStyles = {\n    primary: \"bg-gradient-to-r from-neon-orange to-neon-red text-white hover:shadow-neon\",\n    secondary: \"bg-neon-orange text-white hover:bg-neon-orange/90 hover:shadow-neon-orange\",\n    outline: \"border border-neon-orange text-neon-orange hover:bg-neon-orange/10\",\n    ghost: \"text-white hover:text-neon-orange hover:bg-white/5\",\n  };\n\n  const sizeStyles = {\n    sm: \"text-xs px-3 py-1.5\",\n    md: \"text-sm px-4 py-2\",\n    lg: \"text-base px-6 py-3\",\n  };\n\n  const styles = `${baseStyles} ${variantStyles[variant]} ${sizeStyles[size]} ${className}`;\n\n  if (href) {\n    // Check if it's an external link\n    const isExternal = href.startsWith('http') || href.startsWith('https');\n\n    if (isExternal) {\n      return (\n        <a href={href} className={styles} target=\"_blank\" rel=\"noopener noreferrer\">\n          {children}\n        </a>\n      );\n    }\n\n    return (\n      <Link href={href} className={styles}>\n        {children}\n      </Link>\n    );\n  }\n\n  return (\n    <button className={styles} onClick={onClick}>\n      {children}\n    </button>\n  );\n};\n\nexport default Button;\n"], "names": [], "mappings": ";;;;AACA;;;AAWA,MAAM,SAAS,CAAC,EACd,QAAQ,EACR,IAAI,EACJ,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,YAAY,EAAE,EACd,OAAO,EACK;IACZ,MAAM,aAAa;IAEnB,MAAM,gBAAgB;QACpB,SAAS;QACT,WAAW;QACX,SAAS;QACT,OAAO;IACT;IAEA,MAAM,aAAa;QACjB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,SAAS,GAAG,WAAW,CAAC,EAAE,aAAa,CAAC,QAAQ,CAAC,CAAC,EAAE,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE,WAAW;IAEzF,IAAI,MAAM;QACR,iCAAiC;QACjC,MAAM,aAAa,KAAK,UAAU,CAAC,WAAW,KAAK,UAAU,CAAC;QAE9D,IAAI,YAAY;YACd,qBACE,6LAAC;gBAAE,MAAM;gBAAM,WAAW;gBAAQ,QAAO;gBAAS,KAAI;0BACnD;;;;;;QAGP;QAEA,qBACE,6LAAC,+JAAA,CAAA,UAAI;YAAC,MAAM;YAAM,WAAW;sBAC1B;;;;;;IAGP;IAEA,qBACE,6LAAC;QAAO,WAAW;QAAQ,SAAS;kBACjC;;;;;;AAGP;KAjDM;uCAmDS"}}, {"offset": {"line": 1834, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1840, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/TGRS%20Website/tgrs-website/src/app/features/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport React from \"react\";\nimport Navbar from \"@/components/Navbar\";\nimport Footer from \"@/components/Footer\";\nimport FeatureCard from \"@/components/FeatureCard\";\nimport Button from \"@/components/Button\";\n\nexport default function Features() {\n  return (\n    <>\n      <Navbar />\n\n      <div className=\"pt-24 pb-12 md:pt-32 md:pb-20 relative bg-black\">\n        <div className=\"absolute inset-0 pattern-circuit opacity-5\"></div>\n        <div className=\"container mx-auto px-4 md:px-6 relative z-10\">\n          <div className=\"text-center\">\n            <h1 className=\"text-4xl md:text-5xl font-display font-bold text-white mb-6 wave-heading-bg\">\n              <span className=\"text-neon-orange\">SERVER</span> FEATURES\n            </h1>\n            <p className=\"text-gray-400 max-w-3xl mx-auto\">\n              Discover what makes TGRS the premier Telugu community FiveM roleplay server.\n            </p>\n          </div>\n        </div>\n      </div>\n\n      {/* Section Divider */}\n      <div className=\"section-divider\"></div>\n\n      {/* Features Overview */}\n      <section className=\"py-20 relative bg-black/50\">\n        <div className=\"container mx-auto px-6 sm:px-12 md:px-16 lg:px-20 xl:px-24\">\n          <div className=\"max-w-6xl mx-auto\">\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6 md:gap-8 mb-12 md:mb-16\">\n              <div className=\"glass rounded-lg p-6 border border-neon-orange/20 text-center\">\n                <div className=\"w-16 h-16 rounded-full bg-neon-orange/20 flex items-center justify-center mx-auto mb-4\">\n                  <svg className=\"w-8 h-8 text-neon-orange\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z\" />\n                  </svg>\n                </div>\n                <h3 className=\"text-xl font-display font-semibold text-white mb-2\">Optimized Performance</h3>\n                <p className=\"text-gray-400\">Our server is optimized for smooth gameplay with minimal lag, ensuring a seamless roleplay experience.</p>\n              </div>\n\n              <div className=\"glass rounded-lg p-6 border border-neon-orange/20 text-center\">\n                <div className=\"w-16 h-16 rounded-full bg-neon-orange/20 flex items-center justify-center mx-auto mb-4\">\n                  <svg className=\"w-8 h-8 text-neon-orange\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\" />\n                  </svg>\n                </div>\n                <h3 className=\"text-xl font-display font-semibold text-white mb-2\">Telugu Community</h3>\n                <p className=\"text-gray-400\">A dedicated server for Telugu gamers, creating a unique cultural connection within the roleplay experience.</p>\n              </div>\n\n              <div className=\"glass rounded-lg p-6 border border-neon-orange/20 text-center\">\n                <div className=\"w-16 h-16 rounded-full bg-neon-orange/20 flex items-center justify-center mx-auto mb-4\">\n                  <svg className=\"w-8 h-8 text-neon-orange\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4\" />\n                  </svg>\n                </div>\n                <h3 className=\"text-xl font-display font-semibold text-white mb-2\">Custom Scripts</h3>\n                <p className=\"text-gray-400\">Unique and exclusive scripts developed specifically for our server to enhance the roleplay experience.</p>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"section-divider\"></div>\n\n          <div className=\"mb-12 md:mb-16 pt-16 md:pt-20\">\n            <div className=\"text-center mb-8 md:mb-12\">\n              <h2 className=\"text-3xl md:text-4xl font-display font-bold text-white mb-4 wave-heading-bg\">\n                <span className=\"text-neon-orange\">GAMEPLAY</span> FEATURES\n              </h2>\n              <p className=\"text-gray-400 max-w-3xl mx-auto px-2\">Immersive gameplay elements that make TGRS unique</p>\n            </div>\n\n            <div className=\"max-w-6xl mx-auto\">\n              <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-5 md:gap-6\">\n                {/* FeatureCard components */}\n                <FeatureCard title=\"Custom Character Creation\" description=\"Create a unique character with extensive customization options tailored for diverse appearances.\" icon={\n                  <svg className=\"w-10 h-10\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\" />\n                  </svg>} />\n                <FeatureCard title=\"Advanced Economy System\" description=\"A balanced economy with multiple jobs, businesses, and investment opportunities.\" icon={\n                  <svg className=\"w-10 h-10\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                  </svg>} />\n                <FeatureCard title=\"Custom Properties\" description=\"Own and customize properties throughout the city, from apartments to businesses.\" icon={\n                  <svg className=\"w-10 h-10\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6\" />\n                  </svg>} />\n                <FeatureCard title=\"Realistic Vehicle System\" description=\"Detailed vehicle mechanics including fuel, damage, and maintenance requirements.\" icon={\n                  <svg className=\"w-10 h-10\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 10V3L4 14h7v7l9-11h-7z\" />\n                  </svg>} />\n                <FeatureCard title=\"Advanced Job System\" description=\"Multiple legal and illegal career paths with progression and unique gameplay mechanics.\" icon={\n                  <svg className=\"w-10 h-10\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2m4 6h.01M5 20h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\" />\n                  </svg>} />\n                <FeatureCard title=\"Dynamic Weather System\" description=\"Realistic and dynamic weather patterns that affect gameplay and visibility.\" icon={\n                  <svg className=\"w-10 h-10\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 15a4 4 0 004 4h9a5 5 0 10-.1-9.999 5.002 5.002 0 10-9.78 2.096A4.001 4.001 0 003 15z\" />\n                  </svg>} />\n              </div>\n            </div>\n          </div>\n\n          <div className=\"section-divider\"></div>\n\n          {/* Community Features */}\n          <div className=\"mb-12 md:mb-16 pt-12 md:pt-16\">\n            <div className=\"text-center mb-8 md:mb-12\">\n              <h2 className=\"text-2xl sm:text-3xl md:text-4xl font-display font-bold text-white mb-4 wave-heading-bg\">\n                <span className=\"text-neon-orange\">COMMUNITY</span> FEATURES\n              </h2>\n              <p className=\"text-sm sm:text-base md:text-lg text-gray-400 max-w-3xl mx-auto px-4\">\n                Elements that enhance our community experience\n              </p>\n            </div>\n\n            <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n              <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6 sm:gap-8 lg:gap-12 items-stretch\">\n                <div className=\"glass-enhanced rounded-xl p-6 sm:p-8 border border-neon-orange/30 hover:border-neon-orange/60 transition-all duration-500 hover:shadow-neon-strong group h-full\">\n                  <h3 className=\"text-xl sm:text-2xl font-display font-semibold text-white mb-4 sm:mb-6 flex items-center group-hover:text-neon-orange transition-colors duration-300\">\n                    <div className=\"w-8 h-8 sm:w-10 sm:h-10 mr-3 sm:mr-4 text-neon-orange group-hover:text-white transition-colors duration-300 relative\">\n                      <svg className=\"w-full h-full\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\" />\n                      </svg>\n                    </div>\n                    Active Discord Community\n                  </h3>\n                  <p className=\"text-sm sm:text-base text-gray-400 mb-4 sm:mb-6 group-hover:text-gray-200 transition-colors duration-300 leading-relaxed\">\n                    Our Discord server is the hub for all community activities, featuring:\n                  </p>\n                  <ul className=\"space-y-3 sm:space-y-4 text-sm sm:text-base text-gray-400 group-hover:text-gray-200 transition-colors duration-300\">\n                    <li className=\"flex items-start\">\n                      <span className=\"text-neon-orange mr-3 mt-1 text-lg\">•</span>\n                      <span className=\"leading-relaxed\">Dedicated support channels</span>\n                    </li>\n                    <li className=\"flex items-start\">\n                      <span className=\"text-neon-orange mr-3 mt-1 text-lg\">•</span>\n                      <span className=\"leading-relaxed\">Community events and announcements</span>\n                    </li>\n                    <li className=\"flex items-start\">\n                      <span className=\"text-neon-orange mr-3 mt-1 text-lg\">•</span>\n                      <span className=\"leading-relaxed\">Character development resources</span>\n                    </li>\n                    <li className=\"flex items-start\">\n                      <span className=\"text-neon-orange mr-3 mt-1 text-lg\">•</span>\n                      <span className=\"leading-relaxed\">Voice channels for out-of-game socializing</span>\n                    </li>\n                  </ul>\n                </div>\n\n                <div className=\"glass-enhanced rounded-xl p-6 sm:p-8 border border-neon-orange/30 hover:border-neon-orange/60 transition-all duration-500 hover:shadow-neon-strong group h-full\">\n                  <h3 className=\"text-xl sm:text-2xl font-display font-semibold text-white mb-4 sm:mb-6 flex items-center group-hover:text-neon-orange transition-colors duration-300\">\n                    <div className=\"w-8 h-8 sm:w-10 sm:h-10 mr-3 sm:mr-4 text-neon-orange group-hover:text-white transition-colors duration-300 relative\">\n                      <svg className=\"w-full h-full\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\" />\n                      </svg>\n                    </div>\n                    Regular Events\n                  </h3>\n                  <p className=\"text-sm sm:text-base text-gray-400 mb-4 sm:mb-6 group-hover:text-gray-200 transition-colors duration-300 leading-relaxed\">\n                    We host a variety of events to keep the community engaged:\n                  </p>\n                  <ul className=\"space-y-3 sm:space-y-4 text-sm sm:text-base text-gray-400 group-hover:text-gray-200 transition-colors duration-300\">\n                    <li className=\"flex items-start\">\n                      <span className=\"text-neon-orange mr-3 mt-1 text-lg\">•</span>\n                      <span className=\"leading-relaxed\">Weekly car meets and races</span>\n                    </li>\n                    <li className=\"flex items-start\">\n                      <span className=\"text-neon-orange mr-3 mt-1 text-lg\">•</span>\n                      <span className=\"leading-relaxed\">Special holiday celebrations</span>\n                    </li>\n                    <li className=\"flex items-start\">\n                      <span className=\"text-neon-orange mr-3 mt-1 text-lg\">•</span>\n                      <span className=\"leading-relaxed\">Community challenges with prizes</span>\n                    </li>\n                    <li className=\"flex items-start\">\n                      <span className=\"text-neon-orange mr-3 mt-1 text-lg\">•</span>\n                      <span className=\"leading-relaxed\">Roleplay scenarios and storylines</span>\n                    </li>\n                  </ul>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Call to Action */}\n          <div className=\"text-center\">\n            <p className=\"text-gray-400 mb-6\">Ready to experience all these features and more? Join our server today!</p>\n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n              {/* <Button href=\"#\" variant=\"primary\" size=\"lg\">Join Discord</Button> */}\n              <Button href=\"/rules\" variant=\"outline\" size=\"lg\">View Rules</Button>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      <Footer />\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AANA;;;;;;AAQe,SAAS;IACtB,qBACE;;0BACE,6LAAC,+HAAA,CAAA,UAAM;;;;;0BAEP,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC;4CAAK,WAAU;sDAAmB;;;;;;wCAAa;;;;;;;8CAElD,6LAAC;oCAAE,WAAU;8CAAkC;;;;;;;;;;;;;;;;;;;;;;;0BAQrD,6LAAC;gBAAI,WAAU;;;;;;0BAGf,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;oDAA2B,MAAK;oDAAO,QAAO;oDAAe,SAAQ;oDAAY,OAAM;8DACpG,cAAA,6LAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;;;;;;0DAGzE,6LAAC;gDAAG,WAAU;0DAAqD;;;;;;0DACnE,6LAAC;gDAAE,WAAU;0DAAgB;;;;;;;;;;;;kDAG/B,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;oDAA2B,MAAK;oDAAO,QAAO;oDAAe,SAAQ;oDAAY,OAAM;8DACpG,cAAA,6LAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;;;;;;0DAGzE,6LAAC;gDAAG,WAAU;0DAAqD;;;;;;0DACnE,6LAAC;gDAAE,WAAU;0DAAgB;;;;;;;;;;;;kDAG/B,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;oDAA2B,MAAK;oDAAO,QAAO;oDAAe,SAAQ;oDAAY,OAAM;8DACpG,cAAA,6LAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;;;;;;0DAGzE,6LAAC;gDAAG,WAAU;0DAAqD;;;;;;0DACnE,6LAAC;gDAAE,WAAU;0DAAgB;;;;;;;;;;;;;;;;;;;;;;;sCAKnC,6LAAC;4BAAI,WAAU;;;;;;sCAEf,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;oDAAK,WAAU;8DAAmB;;;;;;gDAAe;;;;;;;sDAEpD,6LAAC;4CAAE,WAAU;sDAAuC;;;;;;;;;;;;8CAGtD,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DAEb,6LAAC,oIAAA,CAAA,UAAW;gDAAC,OAAM;gDAA4B,aAAY;gDAAmG,oBAC5J,6LAAC;oDAAI,WAAU;oDAAY,MAAK;oDAAO,QAAO;oDAAe,SAAQ;oDAAY,OAAM;8DACrF,cAAA,6LAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;;;;;;0DAEzE,6LAAC,oIAAA,CAAA,UAAW;gDAAC,OAAM;gDAA0B,aAAY;gDAAmF,oBAC1I,6LAAC;oDAAI,WAAU;oDAAY,MAAK;oDAAO,QAAO;oDAAe,SAAQ;oDAAY,OAAM;8DACrF,cAAA,6LAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;;;;;;0DAEzE,6LAAC,oIAAA,CAAA,UAAW;gDAAC,OAAM;gDAAoB,aAAY;gDAAmF,oBACpI,6LAAC;oDAAI,WAAU;oDAAY,MAAK;oDAAO,QAAO;oDAAe,SAAQ;oDAAY,OAAM;8DACrF,cAAA,6LAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;;;;;;0DAEzE,6LAAC,oIAAA,CAAA,UAAW;gDAAC,OAAM;gDAA2B,aAAY;gDAAmF,oBAC3I,6LAAC;oDAAI,WAAU;oDAAY,MAAK;oDAAO,QAAO;oDAAe,SAAQ;oDAAY,OAAM;8DACrF,cAAA,6LAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;;;;;;0DAEzE,6LAAC,oIAAA,CAAA,UAAW;gDAAC,OAAM;gDAAsB,aAAY;gDAA0F,oBAC7I,6LAAC;oDAAI,WAAU;oDAAY,MAAK;oDAAO,QAAO;oDAAe,SAAQ;oDAAY,OAAM;8DACrF,cAAA,6LAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;;;;;;0DAEzE,6LAAC,oIAAA,CAAA,UAAW;gDAAC,OAAM;gDAAyB,aAAY;gDAA8E,oBACpI,6LAAC;oDAAI,WAAU;oDAAY,MAAK;oDAAO,QAAO;oDAAe,SAAQ;oDAAY,OAAM;8DACrF,cAAA,6LAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAM/E,6LAAC;4BAAI,WAAU;;;;;;sCAGf,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;oDAAK,WAAU;8DAAmB;;;;;;gDAAgB;;;;;;;sDAErD,6LAAC;4CAAE,WAAU;sDAAuE;;;;;;;;;;;;8CAKtF,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;;0EACZ,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC;oEAAI,WAAU;oEAAgB,MAAK;oEAAO,QAAO;oEAAe,SAAQ;oEAAY,OAAM;8EACzF,cAAA,6LAAC;wEAAK,eAAc;wEAAQ,gBAAe;wEAAQ,aAAa;wEAAG,GAAE;;;;;;;;;;;;;;;;4DAEnE;;;;;;;kEAGR,6LAAC;wDAAE,WAAU;kEAA2H;;;;;;kEAGxI,6LAAC;wDAAG,WAAU;;0EACZ,6LAAC;gEAAG,WAAU;;kFACZ,6LAAC;wEAAK,WAAU;kFAAqC;;;;;;kFACrD,6LAAC;wEAAK,WAAU;kFAAkB;;;;;;;;;;;;0EAEpC,6LAAC;gEAAG,WAAU;;kFACZ,6LAAC;wEAAK,WAAU;kFAAqC;;;;;;kFACrD,6LAAC;wEAAK,WAAU;kFAAkB;;;;;;;;;;;;0EAEpC,6LAAC;gEAAG,WAAU;;kFACZ,6LAAC;wEAAK,WAAU;kFAAqC;;;;;;kFACrD,6LAAC;wEAAK,WAAU;kFAAkB;;;;;;;;;;;;0EAEpC,6LAAC;gEAAG,WAAU;;kFACZ,6LAAC;wEAAK,WAAU;kFAAqC;;;;;;kFACrD,6LAAC;wEAAK,WAAU;kFAAkB;;;;;;;;;;;;;;;;;;;;;;;;0DAKxC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;;0EACZ,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC;oEAAI,WAAU;oEAAgB,MAAK;oEAAO,QAAO;oEAAe,SAAQ;oEAAY,OAAM;8EACzF,cAAA,6LAAC;wEAAK,eAAc;wEAAQ,gBAAe;wEAAQ,aAAa;wEAAG,GAAE;;;;;;;;;;;;;;;;4DAEnE;;;;;;;kEAGR,6LAAC;wDAAE,WAAU;kEAA2H;;;;;;kEAGxI,6LAAC;wDAAG,WAAU;;0EACZ,6LAAC;gEAAG,WAAU;;kFACZ,6LAAC;wEAAK,WAAU;kFAAqC;;;;;;kFACrD,6LAAC;wEAAK,WAAU;kFAAkB;;;;;;;;;;;;0EAEpC,6LAAC;gEAAG,WAAU;;kFACZ,6LAAC;wEAAK,WAAU;kFAAqC;;;;;;kFACrD,6LAAC;wEAAK,WAAU;kFAAkB;;;;;;;;;;;;0EAEpC,6LAAC;gEAAG,WAAU;;kFACZ,6LAAC;wEAAK,WAAU;kFAAqC;;;;;;kFACrD,6LAAC;wEAAK,WAAU;kFAAkB;;;;;;;;;;;;0EAEpC,6LAAC;gEAAG,WAAU;;kFACZ,6LAAC;wEAAK,WAAU;kFAAqC;;;;;;kFACrD,6LAAC;wEAAK,WAAU;kFAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAS9C,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAClC,6LAAC;oCAAI,WAAU;8CAEb,cAAA,6LAAC,+HAAA,CAAA,UAAM;wCAAC,MAAK;wCAAS,SAAQ;wCAAU,MAAK;kDAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAM1D,6LAAC,+HAAA,CAAA,UAAM;;;;;;;AAGb;KApMwB"}}, {"offset": {"line": 2802, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}