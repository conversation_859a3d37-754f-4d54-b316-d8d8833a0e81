# 🚀 TGRS Website - GoDaddy Deployment Checklist

## ✅ PRE-DEPLOYMENT COMPLETED
- [x] Website built successfully
- [x] Static files generated in `out` folder
- [x] .htaccess file created for URL handling
- [x] All pages and features working
- [x] Mobile responsive design
- [x] SEO optimization (sitemap, robots.txt)

## 📋 DEPLOYMENT STEPS

### Step 1: Get GoDaddy FTP Credentials
1. Log into your GoDaddy account
2. Go to "My Products" → "Web Hosting"
3. Click "Manage" next to your hosting plan
4. Find FTP/SFTP credentials in hosting dashboard

### Step 2: Download FTP Client
- **Recommended**: FileZilla (free)
- **Alternative**: WinSCP, Cyberduck, or built-in Windows FTP

### Step 3: Upload Files
1. **Connect to FTP**:
   - Host: Your domain or FTP server
   - Username: Your FTP username
   - Password: Your FTP password
   - Port: 21 (or as specified)

2. **Navigate to public_html folder** on server

3. **Upload ALL contents from `out` folder**:
   - ✅ index.html
   - ✅ _next/ folder (contains CSS, JS)
   - ✅ assets/ folder (contains images)
   - ✅ All page folders (about/, contact/, vip/, etc.)
   - ✅ .htaccess file
   - ✅ favicon.ico
   - ✅ sitemap.xml
   - ✅ robots.txt

### Step 4: Test Website
- [ ] Visit your domain
- [ ] Check homepage loads correctly
- [ ] Test navigation menu
- [ ] Verify all pages work
- [ ] Test mobile responsiveness
- [ ] Check live player count widget
- [ ] Verify Discord/YouTube links work
- [ ] Test VIP section
- [ ] Check contact form

### Step 5: SEO Verification
- [ ] Submit sitemap to Google Search Console
- [ ] Verify robots.txt is accessible
- [ ] Check meta tags and descriptions
- [ ] Test page loading speed

## 🔧 TROUBLESHOOTING

### Issue: 404 errors on page refresh
**Solution**: Ensure .htaccess file is uploaded correctly

### Issue: CSS/JS not loading
**Solution**: Check that _next/ folder uploaded completely

### Issue: Images not showing
**Solution**: Verify assets/ folder uploaded with all images

### Issue: Live player count not working
**Solution**: Check if API calls work from your domain

## 📞 SUPPORT CONTACTS
- **GoDaddy Support**: 24/7 available
- **Developer**: Macpie (Discord: macpie)

## 🎯 POST-DEPLOYMENT TASKS
- [ ] Set up Google Analytics
- [ ] Configure Google Search Console
- [ ] Set up domain SSL certificate (if not auto-enabled)
- [ ] Test website speed with PageSpeed Insights
- [ ] Share website with TGRS community

## 🔄 FUTURE UPDATES
To update website:
1. Make changes to code
2. Run `npm run build`
3. Upload new files from `out` folder
4. Clear browser cache

---

## 📁 WHAT TO UPLOAD

Upload **EVERYTHING** from the `out` folder to your `public_html` directory:

```
public_html/
├── index.html                 ← Homepage
├── .htaccess                  ← URL handling
├── favicon.ico                ← Site icon
├── sitemap.xml               ← SEO sitemap
├── robots.txt                ← SEO robots
├── _next/                    ← CSS, JS, assets
│   ├── static/
│   └── ...
├── assets/                   ← Images
│   ├── tgrs-logo.png
│   ├── peds/
│   └── ...
├── about/
│   └── index.html
├── contact/
│   └── index.html
├── vip/
│   └── index.html
└── ... (all other page folders)
```

## 🎉 CONGRATULATIONS!
Your TGRS website is ready to go live! 🚀
