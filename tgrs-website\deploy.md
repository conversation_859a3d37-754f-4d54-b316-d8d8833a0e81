# 🚀 TGRS Website Deployment Guide for GoDaddy

## ✅ BUILD COMPLETED SUCCESSFULLY!

Your website has been built and is ready for deployment. The `out` folder contains all the files you need to upload.

## 📋 Prerequisites
- GoDaddy hosting account
- FTP/SFTP access credentials
- <PERSON><PERSON><PERSON> or similar FTP client

## 🔧 Step 1: Build the Website ✅ DONE

The website has been successfully built! The `out` folder contains:
- `index.html` (your homepage)
- `_next/` folder (CSS, JS, and other assets)
- All page HTML files
- Assets folder with images
- SEO files (sitemap.xml, robots.txt)

## 📁 Step 2: Prepare Files for Upload

After building, you'll have an `out` folder containing:
- `index.html` (your homepage)
- `_next/` folder (CSS, JS, and other assets)
- Other HTML files for different pages

## 🌐 Step 3: Upload to GoDaddy

### 3.1 Get Your FTP Credentials
1. Log into your GoDaddy account
2. Go to "My Products" → "Web Hosting"
3. Click "Manage" next to your hosting plan
4. Find FTP credentials in the hosting dashboard

### 3.2 Upload Files
1. **Download FileZilla** (free FTP client)
2. **Connect to your server**:
   - Host: Your domain or FTP server address
   - Username: Your FTP username
   - Password: Your FTP password
   - Port: 21 (or as specified by GoDaddy)

3. **Upload files**:
   - Navigate to `public_html` folder on the server
   - Upload ALL contents from the `out` folder
   - **Important**: Upload the CONTENTS of `out`, not the `out` folder itself

### 3.3 File Structure on Server
Your `public_html` should look like:
```
public_html/
├── index.html
├── _next/
│   ├── static/
│   └── ...
├── favicon.ico
└── other files...
```

## 🔧 Step 4: Configure Domain (if needed)

### 4.1 If using a subdomain:
- Create subdomain in GoDaddy control panel
- Point it to the correct folder

### 4.2 If using main domain:
- Ensure files are in `public_html` root
- Update DNS if needed

## ✅ Step 5: Test Your Website

1. Visit your domain in a browser
2. Check all pages work correctly
3. Test mobile responsiveness
4. Verify all links and features work

## 🔄 Step 6: Future Updates

To update your website:
1. Make changes to your code
2. Run `npm run build`
3. Upload new files from `out` folder to replace old ones
4. Clear browser cache to see changes

## 🚨 Common Issues & Solutions

### Issue: 404 errors on page refresh
**Solution**: Add `.htaccess` file to `public_html`:
```apache
RewriteEngine On
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)$ /index.html [L]
```

### Issue: CSS/JS not loading
**Solution**: Check file paths and ensure `_next` folder uploaded correctly

### Issue: Images not showing
**Solution**: Verify image files uploaded and paths are correct

## 📞 Need Help?
- GoDaddy Support: Available 24/7
- Check GoDaddy hosting documentation
- Verify FTP credentials are correct

## 🎯 Quick Checklist
- [ ] Built website with `npm run build`
- [ ] Connected to FTP server
- [ ] Uploaded all files from `out` folder
- [ ] Tested website in browser
- [ ] All pages and features working
- [ ] Mobile version working correctly
