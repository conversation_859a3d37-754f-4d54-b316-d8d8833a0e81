[{"C:\\Users\\<USER>\\Desktop\\TGRS Website\\tgrs-website\\src\\app\\about\\page.tsx": "1", "C:\\Users\\<USER>\\Desktop\\TGRS Website\\tgrs-website\\src\\app\\contact\\page.tsx": "2", "C:\\Users\\<USER>\\Desktop\\TGRS Website\\tgrs-website\\src\\app\\features\\page.tsx": "3", "C:\\Users\\<USER>\\Desktop\\TGRS Website\\tgrs-website\\src\\app\\layout.tsx": "4", "C:\\Users\\<USER>\\Desktop\\TGRS Website\\tgrs-website\\src\\app\\page.tsx": "5", "C:\\Users\\<USER>\\Desktop\\TGRS Website\\tgrs-website\\src\\app\\rules\\page.tsx": "6", "C:\\Users\\<USER>\\Desktop\\TGRS Website\\tgrs-website\\src\\app\\vip\\page.tsx": "7", "C:\\Users\\<USER>\\Desktop\\TGRS Website\\tgrs-website\\src\\components\\BouncingShapes.tsx": "8", "C:\\Users\\<USER>\\Desktop\\TGRS Website\\tgrs-website\\src\\components\\Button.tsx": "9", "C:\\Users\\<USER>\\Desktop\\TGRS Website\\tgrs-website\\src\\components\\FeatureCard.tsx": "10", "C:\\Users\\<USER>\\Desktop\\TGRS Website\\tgrs-website\\src\\components\\Footer.tsx": "11", "C:\\Users\\<USER>\\Desktop\\TGRS Website\\tgrs-website\\src\\components\\Hero.tsx": "12", "C:\\Users\\<USER>\\Desktop\\TGRS Website\\tgrs-website\\src\\components\\LogoPlaceholder.tsx": "13", "C:\\Users\\<USER>\\Desktop\\TGRS Website\\tgrs-website\\src\\components\\Navbar.tsx": "14", "C:\\Users\\<USER>\\Desktop\\TGRS Website\\tgrs-website\\src\\components\\VIPCard.tsx": "15", "C:\\Users\\<USER>\\Desktop\\TGRS Website\\tgrs-website\\src\\app\\fivem-roleplay-server\\page.tsx": "16", "C:\\Users\\<USER>\\Desktop\\TGRS Website\\tgrs-website\\src\\app\\robots.ts": "17", "C:\\Users\\<USER>\\Desktop\\TGRS Website\\tgrs-website\\src\\app\\sitemap.ts": "18", "C:\\Users\\<USER>\\Desktop\\TGRS Website\\tgrs-website\\src\\app\\telugu-fivem-server\\page.tsx": "19", "C:\\Users\\<USER>\\Desktop\\TGRS Website\\tgrs-website\\src\\app\\telugu-gaming-guide\\page.tsx": "20", "C:\\Users\\<USER>\\Desktop\\TGRS Website\\tgrs-website\\src\\app\\telugu-samp-server\\page.tsx": "21", "C:\\Users\\<USER>\\Desktop\\TGRS Website\\tgrs-website\\src\\components\\Analytics.tsx": "22", "C:\\Users\\<USER>\\Desktop\\TGRS Website\\tgrs-website\\src\\components\\CashFlyingAnimation.tsx": "23", "C:\\Users\\<USER>\\Desktop\\TGRS Website\\tgrs-website\\src\\components\\FlowingParticles.tsx": "24", "C:\\Users\\<USER>\\Desktop\\TGRS Website\\tgrs-website\\src\\components\\LivePlayerCount.tsx": "25", "C:\\Users\\<USER>\\Desktop\\TGRS Website\\tgrs-website\\src\\components\\VIPBouncingShapes.tsx": "26", "C:\\Users\\<USER>\\Desktop\\TGRS Website\\tgrs-website\\src\\components\\VIPCardNew.tsx": "27"}, {"size": 10162, "mtime": 1748049146184, "results": "28", "hashOfConfig": "29"}, {"size": 13495, "mtime": 1748050332459, "results": "30", "hashOfConfig": "29"}, {"size": 14766, "mtime": 1748077422129, "results": "31", "hashOfConfig": "29"}, {"size": 10718, "mtime": 1748080062769, "results": "32", "hashOfConfig": "29"}, {"size": 32047, "mtime": 1748080810229, "results": "33", "hashOfConfig": "29"}, {"size": 40901, "mtime": 1748050453863, "results": "34", "hashOfConfig": "29"}, {"size": 33779, "mtime": 1748078590114, "results": "35", "hashOfConfig": "29"}, {"size": 9295, "mtime": 1748053344189, "results": "36", "hashOfConfig": "29"}, {"size": 1614, "mtime": 1748030554051, "results": "37", "hashOfConfig": "29"}, {"size": 4364, "mtime": 1748074880015, "results": "38", "hashOfConfig": "29"}, {"size": 19095, "mtime": 1748081341425, "results": "39", "hashOfConfig": "29"}, {"size": 4202, "mtime": 1748075210241, "results": "40", "hashOfConfig": "29"}, {"size": 871, "mtime": 1748026440116, "results": "41", "hashOfConfig": "29"}, {"size": 23581, "mtime": 1748080167021, "results": "42", "hashOfConfig": "29"}, {"size": 17211, "mtime": 1748067625921, "results": "43", "hashOfConfig": "29"}, {"size": 8243, "mtime": 1748078157295, "results": "44", "hashOfConfig": "29"}, {"size": 535, "mtime": 1748078027007, "results": "45", "hashOfConfig": "29"}, {"size": 1557, "mtime": 1748078656050, "results": "46", "hashOfConfig": "29"}, {"size": 8701, "mtime": 1748078609309, "results": "47", "hashOfConfig": "29"}, {"size": 8565, "mtime": 1748078215187, "results": "48", "hashOfConfig": "29"}, {"size": 10376, "mtime": 1748078648474, "results": "49", "hashOfConfig": "29"}, {"size": 5710, "mtime": 1748078248736, "results": "50", "hashOfConfig": "29"}, {"size": 8251, "mtime": 1748062262575, "results": "51", "hashOfConfig": "29"}, {"size": 8211, "mtime": 1748060365120, "results": "52", "hashOfConfig": "29"}, {"size": 3244, "mtime": 1748079120977, "results": "53", "hashOfConfig": "29"}, {"size": 11992, "mtime": 1748058964054, "results": "54", "hashOfConfig": "29"}, {"size": 14497, "mtime": 1748067913893, "results": "55", "hashOfConfig": "29"}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1cosqkr", {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 6, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\Desktop\\TGRS Website\\tgrs-website\\src\\app\\about\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\TGRS Website\\tgrs-website\\src\\app\\contact\\page.tsx", ["137", "138", "139"], [], "C:\\Users\\<USER>\\Desktop\\TGRS Website\\tgrs-website\\src\\app\\features\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\TGRS Website\\tgrs-website\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\TGRS Website\\tgrs-website\\src\\app\\page.tsx", ["140", "141"], [], "C:\\Users\\<USER>\\Desktop\\TGRS Website\\tgrs-website\\src\\app\\rules\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\TGRS Website\\tgrs-website\\src\\app\\vip\\page.tsx", ["142"], [], "C:\\Users\\<USER>\\Desktop\\TGRS Website\\tgrs-website\\src\\components\\BouncingShapes.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\TGRS Website\\tgrs-website\\src\\components\\Button.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\TGRS Website\\tgrs-website\\src\\components\\FeatureCard.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\TGRS Website\\tgrs-website\\src\\components\\Footer.tsx", ["143"], [], "C:\\Users\\<USER>\\Desktop\\TGRS Website\\tgrs-website\\src\\components\\Hero.tsx", ["144", "145", "146", "147", "148"], [], "C:\\Users\\<USER>\\Desktop\\TGRS Website\\tgrs-website\\src\\components\\LogoPlaceholder.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\TGRS Website\\tgrs-website\\src\\components\\Navbar.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\TGRS Website\\tgrs-website\\src\\components\\VIPCard.tsx", ["149", "150", "151", "152", "153", "154"], [], "C:\\Users\\<USER>\\Desktop\\TGRS Website\\tgrs-website\\src\\app\\fivem-roleplay-server\\page.tsx", ["155"], [], "C:\\Users\\<USER>\\Desktop\\TGRS Website\\tgrs-website\\src\\app\\robots.ts", [], [], "C:\\Users\\<USER>\\Desktop\\TGRS Website\\tgrs-website\\src\\app\\sitemap.ts", [], [], "C:\\Users\\<USER>\\Desktop\\TGRS Website\\tgrs-website\\src\\app\\telugu-fivem-server\\page.tsx", ["156", "157"], [], "C:\\Users\\<USER>\\Desktop\\TGRS Website\\tgrs-website\\src\\app\\telugu-gaming-guide\\page.tsx", ["158"], [], "C:\\Users\\<USER>\\Desktop\\TGRS Website\\tgrs-website\\src\\app\\telugu-samp-server\\page.tsx", ["159", "160", "161"], [], "C:\\Users\\<USER>\\Desktop\\TGRS Website\\tgrs-website\\src\\components\\Analytics.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\TGRS Website\\tgrs-website\\src\\components\\CashFlyingAnimation.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\TGRS Website\\tgrs-website\\src\\components\\FlowingParticles.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\TGRS Website\\tgrs-website\\src\\components\\LivePlayerCount.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\TGRS Website\\tgrs-website\\src\\components\\VIPBouncingShapes.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\TGRS Website\\tgrs-website\\src\\components\\VIPCardNew.tsx", ["162", "163"], [], {"ruleId": "164", "severity": 2, "message": "165", "line": 66, "column": 45, "nodeType": "166", "messageId": "167", "suggestions": "168"}, {"ruleId": "164", "severity": 2, "message": "165", "line": 89, "column": 64, "nodeType": "166", "messageId": "167", "suggestions": "169"}, {"ruleId": "164", "severity": 2, "message": "165", "line": 262, "column": 87, "nodeType": "166", "messageId": "167", "suggestions": "170"}, {"ruleId": "164", "severity": 2, "message": "165", "line": 462, "column": 149, "nodeType": "166", "messageId": "167", "suggestions": "171"}, {"ruleId": "164", "severity": 2, "message": "165", "line": 501, "column": 151, "nodeType": "166", "messageId": "167", "suggestions": "172"}, {"ruleId": "173", "severity": 2, "message": "174", "line": 6, "column": 8, "nodeType": null, "messageId": "175", "endLine": 6, "endColumn": 14}, {"ruleId": "176", "severity": 2, "message": "177", "line": 42, "column": 15, "nodeType": "178", "endLine": 42, "endColumn": 112}, {"ruleId": "173", "severity": 2, "message": "174", "line": 2, "column": 8, "nodeType": null, "messageId": "175", "endLine": 2, "endColumn": 14}, {"ruleId": "173", "severity": 2, "message": "179", "line": 17, "column": 3, "nodeType": null, "messageId": "175", "endLine": 17, "endColumn": 20}, {"ruleId": "173", "severity": 2, "message": "180", "line": 18, "column": 3, "nodeType": null, "messageId": "175", "endLine": 18, "endColumn": 20}, {"ruleId": "173", "severity": 2, "message": "181", "line": 19, "column": 3, "nodeType": null, "messageId": "175", "endLine": 19, "endColumn": 22}, {"ruleId": "173", "severity": 2, "message": "182", "line": 20, "column": 3, "nodeType": null, "messageId": "175", "endLine": 20, "endColumn": 22}, {"ruleId": "173", "severity": 2, "message": "183", "line": 59, "column": 3, "nodeType": null, "messageId": "175", "endLine": 59, "endColumn": 14}, {"ruleId": "173", "severity": 2, "message": "184", "line": 60, "column": 3, "nodeType": null, "messageId": "175", "endLine": 60, "endColumn": 7}, {"ruleId": "173", "severity": 2, "message": "185", "line": 65, "column": 3, "nodeType": null, "messageId": "175", "endLine": 65, "endColumn": 7}, {"ruleId": "173", "severity": 2, "message": "186", "line": 68, "column": 3, "nodeType": null, "messageId": "175", "endLine": 68, "endColumn": 16}, {"ruleId": "173", "severity": 2, "message": "187", "line": 69, "column": 3, "nodeType": null, "messageId": "175", "endLine": 69, "endColumn": 10}, {"ruleId": "173", "severity": 2, "message": "188", "line": 70, "column": 3, "nodeType": null, "messageId": "175", "endLine": 70, "endColumn": 14}, {"ruleId": "164", "severity": 2, "message": "165", "line": 119, "column": 43, "nodeType": "166", "messageId": "167", "suggestions": "189"}, {"ruleId": "164", "severity": 2, "message": "165", "line": 126, "column": 33, "nodeType": "166", "messageId": "167", "suggestions": "190"}, {"ruleId": "164", "severity": 2, "message": "165", "line": 143, "column": 28, "nodeType": "166", "messageId": "167", "suggestions": "191"}, {"ruleId": "164", "severity": 2, "message": "165", "line": 140, "column": 37, "nodeType": "166", "messageId": "167", "suggestions": "192"}, {"ruleId": "164", "severity": 2, "message": "165", "line": 136, "column": 52, "nodeType": "166", "messageId": "167", "suggestions": "193"}, {"ruleId": "164", "severity": 2, "message": "165", "line": 159, "column": 28, "nodeType": "166", "messageId": "167", "suggestions": "194"}, {"ruleId": "176", "severity": 2, "message": "177", "line": 178, "column": 19, "nodeType": "178", "endLine": 181, "endColumn": 20}, {"ruleId": "173", "severity": 2, "message": "174", "line": 5, "column": 8, "nodeType": null, "messageId": "175", "endLine": 5, "endColumn": 14}, {"ruleId": "173", "severity": 2, "message": "184", "line": 52, "column": 3, "nodeType": null, "messageId": "175", "endLine": 52, "endColumn": 7}, "react/no-unescaped-entities", "`'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.", "JSXText", "unescapedEntityAlts", ["195", "196", "197", "198"], ["199", "200", "201", "202"], ["203", "204", "205", "206"], ["207", "208", "209", "210"], ["211", "212", "213", "214"], "@typescript-eslint/no-unused-vars", "'Button' is defined but never used.", "unusedVar", "@next/next/no-html-link-for-pages", "Do not use an `<a>` element to navigate to `/`. Use `<Link />` from `next/link` instead. See: https://nextjs.org/docs/messages/no-html-link-for-pages", "JSXOpeningElement", "'primaryButtonText' is defined but never used.", "'primaryButtonHref' is defined but never used.", "'secondaryButtonText' is defined but never used.", "'secondaryButtonHref' is defined but never used.", "'description' is defined but never used.", "'icon' is defined but never used.", "'pets' is assigned a value but never used.", "'tiltDirection' is assigned a value but never used.", "'pattern' is assigned a value but never used.", "'contactInfo' is assigned a value but never used.", ["215", "216", "217", "218"], ["219", "220", "221", "222"], ["223", "224", "225", "226"], ["227", "228", "229", "230"], ["231", "232", "233", "234"], ["235", "236", "237", "238"], {"messageId": "239", "data": "240", "fix": "241", "desc": "242"}, {"messageId": "239", "data": "243", "fix": "244", "desc": "245"}, {"messageId": "239", "data": "246", "fix": "247", "desc": "248"}, {"messageId": "239", "data": "249", "fix": "250", "desc": "251"}, {"messageId": "239", "data": "252", "fix": "253", "desc": "242"}, {"messageId": "239", "data": "254", "fix": "255", "desc": "245"}, {"messageId": "239", "data": "256", "fix": "257", "desc": "248"}, {"messageId": "239", "data": "258", "fix": "259", "desc": "251"}, {"messageId": "239", "data": "260", "fix": "261", "desc": "242"}, {"messageId": "239", "data": "262", "fix": "263", "desc": "245"}, {"messageId": "239", "data": "264", "fix": "265", "desc": "248"}, {"messageId": "239", "data": "266", "fix": "267", "desc": "251"}, {"messageId": "239", "data": "268", "fix": "269", "desc": "242"}, {"messageId": "239", "data": "270", "fix": "271", "desc": "245"}, {"messageId": "239", "data": "272", "fix": "273", "desc": "248"}, {"messageId": "239", "data": "274", "fix": "275", "desc": "251"}, {"messageId": "239", "data": "276", "fix": "277", "desc": "242"}, {"messageId": "239", "data": "278", "fix": "279", "desc": "245"}, {"messageId": "239", "data": "280", "fix": "281", "desc": "248"}, {"messageId": "239", "data": "282", "fix": "283", "desc": "251"}, {"messageId": "239", "data": "284", "fix": "285", "desc": "242"}, {"messageId": "239", "data": "286", "fix": "287", "desc": "245"}, {"messageId": "239", "data": "288", "fix": "289", "desc": "248"}, {"messageId": "239", "data": "290", "fix": "291", "desc": "251"}, {"messageId": "239", "data": "292", "fix": "293", "desc": "242"}, {"messageId": "239", "data": "294", "fix": "295", "desc": "245"}, {"messageId": "239", "data": "296", "fix": "297", "desc": "248"}, {"messageId": "239", "data": "298", "fix": "299", "desc": "251"}, {"messageId": "239", "data": "300", "fix": "301", "desc": "242"}, {"messageId": "239", "data": "302", "fix": "303", "desc": "245"}, {"messageId": "239", "data": "304", "fix": "305", "desc": "248"}, {"messageId": "239", "data": "306", "fix": "307", "desc": "251"}, {"messageId": "239", "data": "308", "fix": "309", "desc": "242"}, {"messageId": "239", "data": "310", "fix": "311", "desc": "245"}, {"messageId": "239", "data": "312", "fix": "313", "desc": "248"}, {"messageId": "239", "data": "314", "fix": "315", "desc": "251"}, {"messageId": "239", "data": "316", "fix": "317", "desc": "242"}, {"messageId": "239", "data": "318", "fix": "319", "desc": "245"}, {"messageId": "239", "data": "320", "fix": "321", "desc": "248"}, {"messageId": "239", "data": "322", "fix": "323", "desc": "251"}, {"messageId": "239", "data": "324", "fix": "325", "desc": "242"}, {"messageId": "239", "data": "326", "fix": "327", "desc": "245"}, {"messageId": "239", "data": "328", "fix": "329", "desc": "248"}, {"messageId": "239", "data": "330", "fix": "331", "desc": "251"}, "replaceWithAlt", {"alt": "332"}, {"range": "333", "text": "334"}, "Replace with `&apos;`.", {"alt": "335"}, {"range": "336", "text": "337"}, "Replace with `&lsquo;`.", {"alt": "338"}, {"range": "339", "text": "340"}, "Replace with `&#39;`.", {"alt": "341"}, {"range": "342", "text": "343"}, "Replace with `&rsquo;`.", {"alt": "332"}, {"range": "344", "text": "345"}, {"alt": "335"}, {"range": "346", "text": "347"}, {"alt": "338"}, {"range": "348", "text": "349"}, {"alt": "341"}, {"range": "350", "text": "351"}, {"alt": "332"}, {"range": "352", "text": "353"}, {"alt": "335"}, {"range": "354", "text": "355"}, {"alt": "338"}, {"range": "356", "text": "357"}, {"alt": "341"}, {"range": "358", "text": "359"}, {"alt": "332"}, {"range": "360", "text": "361"}, {"alt": "335"}, {"range": "362", "text": "363"}, {"alt": "338"}, {"range": "364", "text": "365"}, {"alt": "341"}, {"range": "366", "text": "367"}, {"alt": "332"}, {"range": "368", "text": "369"}, {"alt": "335"}, {"range": "370", "text": "371"}, {"alt": "338"}, {"range": "372", "text": "373"}, {"alt": "341"}, {"range": "374", "text": "375"}, {"alt": "332"}, {"range": "376", "text": "377"}, {"alt": "335"}, {"range": "378", "text": "379"}, {"alt": "338"}, {"range": "380", "text": "381"}, {"alt": "341"}, {"range": "382", "text": "383"}, {"alt": "332"}, {"range": "384", "text": "385"}, {"alt": "335"}, {"range": "386", "text": "387"}, {"alt": "338"}, {"range": "388", "text": "389"}, {"alt": "341"}, {"range": "390", "text": "391"}, {"alt": "332"}, {"range": "392", "text": "393"}, {"alt": "335"}, {"range": "394", "text": "395"}, {"alt": "338"}, {"range": "396", "text": "397"}, {"alt": "341"}, {"range": "398", "text": "399"}, {"alt": "332"}, {"range": "400", "text": "401"}, {"alt": "335"}, {"range": "402", "text": "403"}, {"alt": "338"}, {"range": "404", "text": "405"}, {"alt": "341"}, {"range": "406", "text": "407"}, {"alt": "332"}, {"range": "408", "text": "409"}, {"alt": "335"}, {"range": "410", "text": "411"}, {"alt": "338"}, {"range": "412", "text": "413"}, {"alt": "341"}, {"range": "414", "text": "415"}, {"alt": "332"}, {"range": "416", "text": "417"}, {"alt": "335"}, {"range": "418", "text": "419"}, {"alt": "338"}, {"range": "420", "text": "421"}, {"alt": "341"}, {"range": "422", "text": "423"}, "&apos;", [1820, 1903], "\n              Have questions or feedback? We&apos;d love to hear from you!\n            ", "&lsquo;", [1820, 1903], "\n              Have questions or feedback? We&lsquo;d love to hear from you!\n            ", "&#39;", [1820, 1903], "\n              Have questions or feedback? We&#39;d love to hear from you!\n            ", "&rsquo;", [1820, 1903], "\n              Have questions or feedback? We&rsquo;d love to hear from you!\n            ", [2900, 3008], "\n                    Your message has been sent successfully! We&apos;ll get back to you soon.\n                  ", [2900, 3008], "\n                    Your message has been sent successfully! We&lsquo;ll get back to you soon.\n                  ", [2900, 3008], "\n                    Your message has been sent successfully! We&#39;ll get back to you soon.\n                  ", [2900, 3008], "\n                    Your message has been sent successfully! We&rsquo;ll get back to you soon.\n                  ", [12381, 12546], "\n                      Yes, we have a whitelist process to ensure quality roleplay. You&apos;ll need to complete a simple application in our Discord.\n                    ", [12381, 12546], "\n                      Yes, we have a whitelist process to ensure quality roleplay. You&lsquo;ll need to complete a simple application in our Discord.\n                    ", [12381, 12546], "\n                      Yes, we have a whitelist process to ensure quality roleplay. You&#39;ll need to complete a simple application in our Discord.\n                    ", [12381, 12546], "\n                      Yes, we have a whitelist process to ensure quality roleplay. You&rsquo;ll need to complete a simple application in our Discord.\n                    ", [29072, 29331], "Immerse yourself in authentic Telugu roleplay scenarios with custom scripts that resonate with Telugu culture and language. Whether you&apos;re into competitive gaming, casual roleplay, or esports, TGRS offers the perfect environment for Telugu gaming enthusiasts.", [29072, 29331], "Immerse yourself in authentic Telugu roleplay scenarios with custom scripts that resonate with Telugu culture and language. Whether you&lsquo;re into competitive gaming, casual roleplay, or esports, TGRS offers the perfect environment for Telugu gaming enthusiasts.", [29072, 29331], "Immerse yourself in authentic Telugu roleplay scenarios with custom scripts that resonate with Telugu culture and language. Whether you&#39;re into competitive gaming, casual roleplay, or esports, TGRS offers the perfect environment for Telugu gaming enthusiasts.", [29072, 29331], "Immerse yourself in authentic Telugu roleplay scenarios with custom scripts that resonate with Telugu culture and language. Whether you&rsquo;re into competitive gaming, casual roleplay, or esports, TGRS offers the perfect environment for Telugu gaming enthusiasts.", [31715, 31880], "Participate in exciting events and competitions on TGRS Telugu roleplay server. From racing tournaments to roleplay competitions, there&apos;s always something happening.", [31715, 31880], "Participate in exciting events and competitions on TGRS Telugu roleplay server. From racing tournaments to roleplay competitions, there&lsquo;s always something happening.", [31715, 31880], "Participate in exciting events and competitions on TGRS Telugu roleplay server. From racing tournaments to roleplay competitions, there&#39;s always something happening.", [31715, 31880], "Participate in exciting events and competitions on TGRS Telugu roleplay server. From racing tournaments to roleplay competitions, there&rsquo;s always something happening.", [6601, 6919], "\n                New to FiveM roleplay? Don&apos;t worry! Our community is welcoming to players of all experience levels. \n                We provide comprehensive guides, helpful mentors, and a supportive environment to help you develop \n                your roleplay skills and create memorable characters.\n              ", [6601, 6919], "\n                New to FiveM roleplay? Don&lsquo;t worry! Our community is welcoming to players of all experience levels. \n                We provide comprehensive guides, helpful mentors, and a supportive environment to help you develop \n                your roleplay skills and create memorable characters.\n              ", [6601, 6919], "\n                New to FiveM roleplay? Don&#39;t worry! Our community is welcoming to players of all experience levels. \n                We provide comprehensive guides, helpful mentors, and a supportive environment to help you develop \n                your roleplay skills and create memorable characters.\n              ", [6601, 6919], "\n                New to FiveM roleplay? Don&rsquo;t worry! Our community is welcoming to players of all experience levels. \n                We provide comprehensive guides, helpful mentors, and a supportive environment to help you develop \n                your roleplay skills and create memorable characters.\n              ", [6290, 6609], "\n                The Telugu gaming scene has exploded in recent years, with more and more Telugu players looking for\n                quality gaming experiences. TGRS recognized this need early and built a server that caters specifically\n                to Telugu gamers&apos; preferences and cultural nuances.\n              ", [6290, 6609], "\n                The Telugu gaming scene has exploded in recent years, with more and more Telugu players looking for\n                quality gaming experiences. TGRS recognized this need early and built a server that caters specifically\n                to Telugu gamers&lsquo; preferences and cultural nuances.\n              ", [6290, 6609], "\n                The Telugu gaming scene has exploded in recent years, with more and more Telugu players looking for\n                quality gaming experiences. TGRS recognized this need early and built a server that caters specifically\n                to Telugu gamers&#39; preferences and cultural nuances.\n              ", [6290, 6609], "\n                The Telugu gaming scene has exploded in recent years, with more and more Telugu players looking for\n                quality gaming experiences. TGRS recognized this need early and built a server that caters specifically\n                to Telugu gamers&rsquo; preferences and cultural nuances.\n              ", [7436, 7744], "\n                Whether you&apos;re a seasoned FiveM player or new to roleplay gaming, TGRS welcomes all Telugu gamers.\n                Our community is known for being friendly, helpful, and inclusive. Join thousands of Telugu players\n                who have already made TGRS their gaming home.\n              ", [7436, 7744], "\n                Whether you&lsquo;re a seasoned FiveM player or new to roleplay gaming, TGRS welcomes all Telugu gamers.\n                Our community is known for being friendly, helpful, and inclusive. Join thousands of Telugu players\n                who have already made TGRS their gaming home.\n              ", [7436, 7744], "\n                Whether you&#39;re a seasoned FiveM player or new to roleplay gaming, TGRS welcomes all Telugu gamers.\n                Our community is known for being friendly, helpful, and inclusive. Join thousands of Telugu players\n                who have already made TGRS their gaming home.\n              ", [7436, 7744], "\n                Whether you&rsquo;re a seasoned FiveM player or new to roleplay gaming, TGRS welcomes all Telugu gamers.\n                Our community is known for being friendly, helpful, and inclusive. Join thousands of Telugu players\n                who have already made TGRS their gaming home.\n              ", [7917, 8131], "\n                  Ready to be part of the growing Telugu gaming community? Start with TGRS - the premier Telugu \n                  gaming server that&apos;s leading the way in Telugu gaming excellence.\n                ", [7917, 8131], "\n                  Ready to be part of the growing Telugu gaming community? Start with TGRS - the premier Telugu \n                  gaming server that&lsquo;s leading the way in Telugu gaming excellence.\n                ", [7917, 8131], "\n                  Ready to be part of the growing Telugu gaming community? Start with TGRS - the premier Telugu \n                  gaming server that&#39;s leading the way in Telugu gaming excellence.\n                ", [7917, 8131], "\n                  Ready to be part of the growing Telugu gaming community? Start with TGRS - the premier Telugu \n                  gaming server that&rsquo;s leading the way in Telugu gaming excellence.\n                ", [7174, 7397], "\n                  Many Telugu SAMP players have successfully transitioned to FiveM and found the experience \n                  to be superior in every way. Here&apos;s what Telugu gamers love about the upgrade:\n                ", [7174, 7397], "\n                  Many Telugu SAMP players have successfully transitioned to FiveM and found the experience \n                  to be superior in every way. Here&lsquo;s what Telugu gamers love about the upgrade:\n                ", [7174, 7397], "\n                  Many Telugu SAMP players have successfully transitioned to FiveM and found the experience \n                  to be superior in every way. Here&#39;s what Telugu gamers love about the upgrade:\n                ", [7174, 7397], "\n                  Many Telugu SAMP players have successfully transitioned to FiveM and found the experience \n                  to be superior in every way. Here&rsquo;s what Telugu gamers love about the upgrade:\n                ", [8564, 8945], "\n                Whether you&apos;re a veteran Telugu SAMP player looking for a new challenge or someone interested \n                in experiencing the best of Telugu gaming culture, TGRS welcomes you. Our community bridges \n                the gap between different gaming platforms while maintaining the Telugu spirit that makes \n                our community special.\n              ", [8564, 8945], "\n                Whether you&lsquo;re a veteran Telugu SAMP player looking for a new challenge or someone interested \n                in experiencing the best of Telugu gaming culture, TGRS welcomes you. Our community bridges \n                the gap between different gaming platforms while maintaining the Telugu spirit that makes \n                our community special.\n              ", [8564, 8945], "\n                Whether you&#39;re a veteran Telugu SAMP player looking for a new challenge or someone interested \n                in experiencing the best of Telugu gaming culture, TGRS welcomes you. Our community bridges \n                the gap between different gaming platforms while maintaining the Telugu spirit that makes \n                our community special.\n              ", [8564, 8945], "\n                Whether you&rsquo;re a veteran Telugu SAMP player looking for a new challenge or someone interested \n                in experiencing the best of Telugu gaming culture, TGRS welcomes you. Our community bridges \n                the gap between different gaming platforms while maintaining the Telugu spirit that makes \n                our community special.\n              "]