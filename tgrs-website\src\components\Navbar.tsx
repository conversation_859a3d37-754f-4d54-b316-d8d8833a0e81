"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import Image from "next/image";
import { usePathname } from "next/navigation";

const Navbar = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [scrolled, setScrolled] = useState(false);
  const pathname = usePathname();

  // Helper function to check if a link is active
  const isActive = (href: string) => {
    if (href === "/") {
      return pathname === "/";
    }
    return pathname.startsWith(href);
  };

  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 10) {
        setScrolled(true);
      } else {
        setScrolled(false);
      }
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  return (
    <nav
      className={`fixed top-0 left-0 w-full z-50 transition-all duration-500 ${
        scrolled
          ? "bg-black/90 backdrop-blur-xl py-2 shadow-lg shadow-neon-orange/10"
          : "bg-transparent py-4"
      }`}
    >
      {/* Subtle pattern overlay */}
      <div className="absolute inset-0 pattern-dots opacity-20 pointer-events-none"></div>

      <div className="container mx-auto px-4 md:px-6 relative">
        <div className="flex items-center justify-between">
          {/* Enhanced Logo */}
          <Link href="/" className="flex items-center group">
            <div className="relative h-12 w-12 mr-3 transition-all duration-300 group-hover:scale-110 group-hover:rotate-3">
              <div className="absolute inset-0 bg-gradient-to-br from-neon-orange/20 to-neon-red/20 rounded-full blur-sm group-hover:blur-md transition-all duration-300"></div>
              <Image
                src="/assets/tgrs-logo.png"
                alt="TGRS Logo"
                width={48}
                height={48}
                className="w-full h-full object-contain relative z-10 drop-shadow-lg"
                priority
              />
            </div>
            <span className="text-white font-display font-bold text-xl tracking-wider group-hover:text-neon-orange transition-all duration-300">
              TGRS
            </span>
          </Link>

          {/* Enhanced Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-1">
            {/* Navigation Links */}
            <div className="flex items-center space-x-1 mr-6">
              <Link
                href="/"
                className="relative px-4 py-2 text-white font-medium transition-all duration-300 group"
              >
                <span className={`relative z-10 transition-colors duration-300 ${
                  isActive("/") ? "text-neon-orange" : "group-hover:text-neon-orange"
                }`}>
                  Home
                </span>

                {/* Active state particle effects */}
                {isActive("/") && (
                  <div className="absolute inset-0 opacity-100 transition-all duration-500">
                    {/* Floating particles */}
                    <div className="absolute top-1 left-2 w-1 h-1 bg-neon-orange rounded-full animate-ping"></div>
                    <div className="absolute top-2 right-3 w-1.5 h-1.5 bg-yellow-400 rounded-full animate-pulse"></div>
                    <div className="absolute bottom-2 left-3 w-1 h-1 bg-neon-orange rounded-full animate-bounce"></div>
                    <div className="absolute bottom-1 right-2 w-1 h-1 bg-yellow-300 rounded-full animate-ping" style={{animationDelay: '0.5s'}}></div>
                    <div className="absolute top-3 left-1/2 w-0.5 h-0.5 bg-neon-orange rounded-full animate-pulse" style={{animationDelay: '0.3s'}}></div>
                  </div>
                )}

                <div className={`absolute inset-0 bg-gradient-to-r from-neon-orange/10 to-neon-red/10 rounded-lg transition-all duration-300 scale-95 ${
                  isActive("/") ? "opacity-30 scale-100" : "opacity-0 group-hover:opacity-100 group-hover:scale-100"
                }`}></div>
              </Link>
              <Link
                href="/about"
                className="relative px-4 py-2 text-white font-medium transition-all duration-300 group"
              >
                <span className={`relative z-10 transition-colors duration-300 ${
                  isActive("/about") ? "text-neon-orange" : "group-hover:text-neon-orange"
                }`}>
                  About
                </span>

                {/* Active state particle effects */}
                {isActive("/about") && (
                  <div className="absolute inset-0 opacity-100 transition-all duration-500">
                    {/* Floating particles */}
                    <div className="absolute top-1 left-2 w-1 h-1 bg-neon-orange rounded-full animate-ping"></div>
                    <div className="absolute top-2 right-3 w-1.5 h-1.5 bg-yellow-400 rounded-full animate-pulse"></div>
                    <div className="absolute bottom-2 left-3 w-1 h-1 bg-neon-orange rounded-full animate-bounce"></div>
                    <div className="absolute bottom-1 right-2 w-1 h-1 bg-yellow-300 rounded-full animate-ping" style={{animationDelay: '0.5s'}}></div>
                    <div className="absolute top-3 left-1/2 w-0.5 h-0.5 bg-neon-orange rounded-full animate-pulse" style={{animationDelay: '0.3s'}}></div>
                  </div>
                )}

                <div className={`absolute inset-0 bg-gradient-to-r from-neon-orange/10 to-neon-red/10 rounded-lg transition-all duration-300 scale-95 ${
                  isActive("/about") ? "opacity-30 scale-100" : "opacity-0 group-hover:opacity-100 group-hover:scale-100"
                }`}></div>
              </Link>
              <Link
                href="/features"
                className="relative px-4 py-2 text-white font-medium transition-all duration-300 group"
              >
                <span className={`relative z-10 transition-colors duration-300 ${
                  isActive("/features") ? "text-neon-orange" : "group-hover:text-neon-orange"
                }`}>
                  Features
                </span>

                {/* Active state particle effects */}
                {isActive("/features") && (
                  <div className="absolute inset-0 opacity-100 transition-all duration-500">
                    {/* Floating particles */}
                    <div className="absolute top-1 left-2 w-1 h-1 bg-neon-orange rounded-full animate-ping"></div>
                    <div className="absolute top-2 right-3 w-1.5 h-1.5 bg-yellow-400 rounded-full animate-pulse"></div>
                    <div className="absolute bottom-2 left-3 w-1 h-1 bg-neon-orange rounded-full animate-bounce"></div>
                    <div className="absolute bottom-1 right-2 w-1 h-1 bg-yellow-300 rounded-full animate-ping" style={{animationDelay: '0.5s'}}></div>
                    <div className="absolute top-3 left-1/2 w-0.5 h-0.5 bg-neon-orange rounded-full animate-pulse" style={{animationDelay: '0.3s'}}></div>
                  </div>
                )}

                <div className={`absolute inset-0 bg-gradient-to-r from-neon-orange/10 to-neon-red/10 rounded-lg transition-all duration-300 scale-95 ${
                  isActive("/features") ? "opacity-30 scale-100" : "opacity-0 group-hover:opacity-100 group-hover:scale-100"
                }`}></div>
              </Link>
              <Link
                href="/rules"
                className="relative px-4 py-2 text-white font-medium transition-all duration-300 group"
              >
                <span className={`relative z-10 transition-colors duration-300 ${
                  isActive("/rules") ? "text-neon-orange" : "group-hover:text-neon-orange"
                }`}>
                  Rules
                </span>

                {/* Active state particle effects */}
                {isActive("/rules") && (
                  <div className="absolute inset-0 opacity-100 transition-all duration-500">
                    {/* Floating particles */}
                    <div className="absolute top-1 left-2 w-1 h-1 bg-neon-orange rounded-full animate-ping"></div>
                    <div className="absolute top-2 right-3 w-1.5 h-1.5 bg-yellow-400 rounded-full animate-pulse"></div>
                    <div className="absolute bottom-2 left-3 w-1 h-1 bg-neon-orange rounded-full animate-bounce"></div>
                    <div className="absolute bottom-1 right-2 w-1 h-1 bg-yellow-300 rounded-full animate-ping" style={{animationDelay: '0.5s'}}></div>
                    <div className="absolute top-3 left-1/2 w-0.5 h-0.5 bg-neon-orange rounded-full animate-pulse" style={{animationDelay: '0.3s'}}></div>
                  </div>
                )}

                <div className={`absolute inset-0 bg-gradient-to-r from-neon-orange/10 to-neon-red/10 rounded-lg transition-all duration-300 scale-95 ${
                  isActive("/rules") ? "opacity-30 scale-100" : "opacity-0 group-hover:opacity-100 group-hover:scale-100"
                }`}></div>
              </Link>
              <Link
                href="/vip"
                className="relative px-4 py-2 text-white font-medium transition-all duration-300 group overflow-hidden"
              >
                {/* Purple heart with particles */}
                <div className={`absolute inset-0 transition-all duration-500 ${
                  isActive("/vip") ? "opacity-100" : "opacity-0 group-hover:opacity-100"
                }`}>
                  {/* Floating particles */}
                  <div className="absolute top-1 left-2 w-1 h-1 bg-purple-400 rounded-full animate-ping"></div>
                  <div className="absolute top-3 right-3 w-1.5 h-1.5 bg-pink-400 rounded-full animate-pulse"></div>
                  <div className="absolute bottom-2 left-1 w-1 h-1 bg-purple-300 rounded-full animate-bounce"></div>
                  <div className="absolute bottom-1 right-2 w-1 h-1 bg-pink-300 rounded-full animate-ping" style={{animationDelay: '0.5s'}}></div>
                  <div className="absolute top-2 left-1/2 w-0.5 h-0.5 bg-purple-500 rounded-full animate-pulse" style={{animationDelay: '0.3s'}}></div>
                  <div className="absolute bottom-3 right-1 w-0.5 h-0.5 bg-pink-500 rounded-full animate-bounce" style={{animationDelay: '0.7s'}}></div>
                </div>

                {/* Purple gradient background */}
                <div className={`absolute inset-0 bg-gradient-to-r from-purple-600/20 via-pink-500/20 to-purple-600/20 rounded-lg transition-all duration-300 scale-95 ${
                  isActive("/vip") ? "opacity-100 scale-100" : "opacity-0 group-hover:opacity-100 group-hover:scale-100"
                }`}></div>

                {/* Glow effect */}
                <div className={`absolute inset-0 bg-gradient-to-r from-purple-500/30 to-pink-500/30 rounded-lg blur-sm transition-all duration-500 scale-110 ${
                  isActive("/vip") ? "opacity-60" : "opacity-0 group-hover:opacity-60"
                }`}></div>

                <span className={`relative z-10 transition-colors duration-300 flex items-center ${
                  isActive("/vip") ? "text-purple-300" : "group-hover:text-purple-300"
                }`}>
                  <i className={`fas fa-heart mr-2 transition-colors duration-300 ${
                    isActive("/vip")
                      ? "text-pink-400 animate-pulse"
                      : "text-purple-400 group-hover:text-pink-400 group-hover:animate-pulse"
                  }`}></i>
                  VIP
                </span>

              </Link>
            </div>

            {/* Enhanced Play Now Button */}
            <Link
              href="cfx.re/join/o57lj7"
              className="relative px-6 py-2.5 rounded-full font-medium transition-all duration-300 group overflow-hidden"
            >
              {/* Animated background */}
              <div className="absolute inset-0 bg-gradient-to-r from-neon-orange to-neon-red transition-all duration-300 group-hover:scale-105"></div>
              <div className="absolute inset-0 bg-gradient-to-r from-neon-orange-light to-neon-red opacity-0 group-hover:opacity-100 transition-all duration-300"></div>

              {/* Glow effect */}
              <div className="absolute inset-0 rounded-full bg-gradient-to-r from-neon-orange to-neon-red blur-md opacity-0 group-hover:opacity-50 transition-all duration-300 scale-110"></div>

              {/* Button text */}
              <span className="relative z-10 text-white font-semibold tracking-wide group-hover:text-black transition-colors duration-300">
                Play Now
              </span>
            </Link>
          </div>

          {/* Enhanced Mobile Menu Button */}
          <button
            className="md:hidden relative p-2 text-white focus:outline-none group transition-all duration-300"
            onClick={toggleMenu}
          >
            <div className="absolute inset-0 bg-gradient-to-r from-neon-orange/20 to-neon-red/20 rounded-lg opacity-0 group-hover:opacity-100 transition-all duration-300"></div>
            <svg
              className={`w-6 h-6 relative z-10 transition-all duration-300 ${isMenuOpen ? 'rotate-90 text-neon-orange' : 'rotate-0'}`}
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              {isMenuOpen ? (
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                />
              ) : (
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M4 6h16M4 12h16M4 18h16"
                />
              )}
            </svg>
          </button>
        </div>

        {/* Enhanced Mobile Menu */}
        {isMenuOpen && (
          <div className="md:hidden mt-6 relative animate-menu-slide-down">
            {/* Background with pattern */}
            <div className="absolute inset-0 pattern-circuit opacity-20 rounded-xl"></div>
            <div className="relative bg-black/90 backdrop-blur-xl rounded-xl p-6 border border-neon-orange/40 shadow-lg shadow-neon-orange/20 overflow-hidden">
              {/* Particle effects background */}
              <div className="absolute inset-0 opacity-30 pointer-events-none">
                <div className="absolute top-2 left-4 w-1 h-1 bg-neon-orange rounded-full animate-ping"></div>
                <div className="absolute top-4 right-6 w-1.5 h-1.5 bg-yellow-400 rounded-full animate-pulse"></div>
                <div className="absolute bottom-6 left-3 w-1 h-1 bg-neon-orange rounded-full animate-bounce"></div>
                <div className="absolute bottom-3 right-4 w-1 h-1 bg-yellow-300 rounded-full animate-ping" style={{animationDelay: '0.5s'}}></div>
                <div className="absolute top-1/2 left-1/2 w-0.5 h-0.5 bg-neon-orange rounded-full animate-pulse" style={{animationDelay: '0.3s'}}></div>
                <div className="absolute top-8 right-2 w-0.5 h-0.5 bg-yellow-500 rounded-full animate-bounce" style={{animationDelay: '0.7s'}}></div>
              </div>
              <div className="flex flex-col space-y-4">
                <Link
                  href="/"
                  className="relative px-4 py-3 text-white font-medium transition-all duration-300 group rounded-lg"
                  onClick={() => setIsMenuOpen(false)}
                >
                  <div className={`absolute inset-0 bg-gradient-to-r from-neon-orange/10 to-neon-red/10 rounded-lg transition-all duration-300 ${
                    isActive("/") ? "opacity-30" : "opacity-0 group-hover:opacity-100"
                  }`}></div>
                  <span className={`relative z-10 transition-colors duration-300 ${
                    isActive("/") ? "text-neon-orange" : "group-hover:text-neon-orange"
                  }`}>
                    Home
                    {isActive("/") && <span className="ml-2 text-neon-orange animate-pulse">✦</span>}
                  </span>
                </Link>
                <Link
                  href="/about"
                  className="relative px-4 py-3 text-white font-medium transition-all duration-300 group rounded-lg"
                  onClick={() => setIsMenuOpen(false)}
                >
                  <div className={`absolute inset-0 bg-gradient-to-r from-neon-orange/10 to-neon-red/10 rounded-lg transition-all duration-300 ${
                    isActive("/about") ? "opacity-30" : "opacity-0 group-hover:opacity-100"
                  }`}></div>
                  <span className={`relative z-10 transition-colors duration-300 ${
                    isActive("/about") ? "text-neon-orange" : "group-hover:text-neon-orange"
                  }`}>
                    About
                    {isActive("/about") && <span className="ml-2 text-neon-orange animate-pulse">✦</span>}
                  </span>
                </Link>
                <Link
                  href="/features"
                  className="relative px-4 py-3 text-white font-medium transition-all duration-300 group rounded-lg"
                  onClick={() => setIsMenuOpen(false)}
                >
                  <div className={`absolute inset-0 bg-gradient-to-r from-neon-orange/10 to-neon-red/10 rounded-lg transition-all duration-300 ${
                    isActive("/features") ? "opacity-30" : "opacity-0 group-hover:opacity-100"
                  }`}></div>
                  <span className={`relative z-10 transition-colors duration-300 ${
                    isActive("/features") ? "text-neon-orange" : "group-hover:text-neon-orange"
                  }`}>
                    Features
                    {isActive("/features") && <span className="ml-2 text-neon-orange animate-pulse">✦</span>}
                  </span>
                </Link>
                <Link
                  href="/rules"
                  className="relative px-4 py-3 text-white font-medium transition-all duration-300 group rounded-lg"
                  onClick={() => setIsMenuOpen(false)}
                >
                  <div className={`absolute inset-0 bg-gradient-to-r from-neon-orange/10 to-neon-red/10 rounded-lg transition-all duration-300 ${
                    isActive("/rules") ? "opacity-30" : "opacity-0 group-hover:opacity-100"
                  }`}></div>
                  <span className={`relative z-10 transition-colors duration-300 ${
                    isActive("/rules") ? "text-neon-orange" : "group-hover:text-neon-orange"
                  }`}>
                    Rules
                    {isActive("/rules") && <span className="ml-2 text-neon-orange animate-pulse">✦</span>}
                  </span>
                </Link>
                <Link
                  href="/vip"
                  className="relative px-4 py-3 text-white font-medium transition-all duration-300 group rounded-lg overflow-hidden"
                  onClick={() => setIsMenuOpen(false)}
                >
                  {/* Purple heart with particles */}
                  <div className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-all duration-500">
                    {/* Floating particles */}
                    <div className="absolute top-2 left-3 w-1 h-1 bg-purple-400 rounded-full animate-ping"></div>
                    <div className="absolute top-4 right-4 w-1.5 h-1.5 bg-pink-400 rounded-full animate-pulse"></div>
                    <div className="absolute bottom-3 left-2 w-1 h-1 bg-purple-300 rounded-full animate-bounce"></div>
                    <div className="absolute bottom-2 right-3 w-1 h-1 bg-pink-300 rounded-full animate-ping" style={{animationDelay: '0.5s'}}></div>
                    <div className="absolute top-3 left-1/2 w-0.5 h-0.5 bg-purple-500 rounded-full animate-pulse" style={{animationDelay: '0.3s'}}></div>
                    <div className="absolute bottom-4 right-2 w-0.5 h-0.5 bg-pink-500 rounded-full animate-bounce" style={{animationDelay: '0.7s'}}></div>
                  </div>

                  {/* Purple gradient background */}
                  <div className="absolute inset-0 bg-gradient-to-r from-purple-600/20 via-pink-500/20 to-purple-600/20 rounded-lg opacity-0 group-hover:opacity-100 transition-all duration-300"></div>

                  {/* Glow effect */}
                  <div className="absolute inset-0 bg-gradient-to-r from-purple-500/30 to-pink-500/30 rounded-lg blur-sm opacity-0 group-hover:opacity-60 transition-all duration-500 scale-110"></div>

                  <span className={`relative z-10 transition-colors duration-300 flex items-center ${
                    isActive("/vip") ? "text-purple-300" : "group-hover:text-purple-300"
                  }`}>
                    <i className={`fas fa-heart mr-2 transition-colors duration-300 ${
                      isActive("/vip")
                        ? "text-pink-400 animate-pulse"
                        : "text-purple-400 group-hover:text-pink-400 group-hover:animate-pulse"
                    }`}></i>
                    VIP
                    {isActive("/vip") && <span className="ml-2 text-purple-400 animate-pulse">♥</span>}
                  </span>
                </Link>

                {/* Divider */}
                <div className="h-px bg-gradient-to-r from-transparent via-neon-orange/50 to-transparent my-2"></div>

                {/* Enhanced Mobile Play Now Button */}
                <Link
                  href="cfx.re/join/o57lj7"
                  className="relative px-6 py-3 rounded-full font-medium transition-all duration-300 group overflow-hidden text-center"
                  onClick={() => setIsMenuOpen(false)}
                >
                  {/* Animated background */}
                  <div className="absolute inset-0 bg-gradient-to-r from-neon-orange to-neon-red transition-all duration-300"></div>
                  <div className="absolute inset-0 bg-gradient-to-r from-neon-orange-light to-neon-red opacity-0 group-hover:opacity-100 transition-all duration-300"></div>

                  {/* Glow effect */}
                  <div className="absolute inset-0 rounded-full bg-gradient-to-r from-neon-orange to-neon-red blur-sm opacity-0 group-hover:opacity-60 transition-all duration-300"></div>

                  {/* Button text */}
                  <span className="relative z-10 text-white font-semibold tracking-wide group-hover:text-black transition-colors duration-300">
                    Play Now
                  </span>
                </Link>
              </div>
            </div>
          </div>
        )}
      </div>
    </nav>
  );
};

export default Navbar;
