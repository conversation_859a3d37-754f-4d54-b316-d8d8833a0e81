'use client';

import { useState, useEffect } from 'react';

interface ServerData {
  clients: number;
  sv_maxclients: number;
  svMaxclients: number;
  hostname: string;
}

interface ApiResponse {
  Data: ServerData;
}

export default function MobileServerPulse() {
  const [playerCount, setPlayerCount] = useState<number | null>(null);
  const [maxPlayers, setMaxPlayers] = useState<number>(48);
  const [isVisible, setIsVisible] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(false);

  const fetchPlayerCount = async () => {
    try {
      const response = await fetch('https://servers-frontend.fivem.net/api/servers/single/o57lj7');
      
      if (!response.ok) {
        throw new Error('Failed to fetch server data');
      }
      
      const data: ApiResponse = await response.json();
      
      if (data.Data) {
        setPlayerCount(data.Data.clients);
        setMaxPlayers(data.Data.svMaxclients || data.Data.sv_maxclients);
        setError(false);
      } else {
        throw new Error('Invalid server data');
      }
    } catch (err) {
      console.error('Error fetching player count:', err);
      setError(true);
      setPlayerCount(null);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchPlayerCount();
    const interval = setInterval(fetchPlayerCount, 30000);
    return () => clearInterval(interval);
  }, []);

  // Show pulse notification every 2 minutes for 8 seconds
  useEffect(() => {
    if (!isLoading && !error && playerCount !== null) {
      const showPulse = () => {
        setIsVisible(true);
        setTimeout(() => setIsVisible(false), 8000); // Hide after 8 seconds
      };

      // Show immediately on load
      setTimeout(showPulse, 2000);
      
      // Then show every 2 minutes
      const pulseInterval = setInterval(showPulse, 120000);
      
      return () => clearInterval(pulseInterval);
    }
  }, [isLoading, error, playerCount]);

  if (isLoading || error || playerCount === null) {
    return null;
  }

  const isServerFull = playerCount >= maxPlayers;
  const isHighPopulation = playerCount >= maxPlayers * 0.8;

  return (
    <div className={`md:hidden fixed top-20 left-4 right-4 z-40 transition-all duration-700 ease-out ${
      isVisible ? 'transform translate-y-0 opacity-100' : 'transform -translate-y-full opacity-0'
    }`}>
      {/* Notification Card */}
      <div className="relative">
        {/* Glow Effect */}
        <div className={`absolute inset-0 rounded-xl blur-lg transition-all duration-500 ${
          isServerFull 
            ? 'bg-red-500/40' 
            : isHighPopulation 
              ? 'bg-yellow-500/40' 
              : 'bg-green-500/40'
        }`}></div>

        {/* Main Card */}
        <div className={`relative bg-black/95 backdrop-blur-xl rounded-xl border p-4 transition-all duration-500 ${
          isServerFull 
            ? 'border-red-500/60 shadow-lg shadow-red-500/20' 
            : isHighPopulation 
              ? 'border-yellow-500/60 shadow-lg shadow-yellow-500/20' 
              : 'border-green-500/60 shadow-lg shadow-green-500/20'
        }`}>
          
          {/* Animated Background Pattern */}
          <div className="absolute inset-0 overflow-hidden rounded-xl pointer-events-none">
            <div className="absolute inset-0 pattern-dots opacity-10"></div>
            {/* Floating particles */}
            <div className={`absolute top-2 left-4 w-1 h-1 rounded-full animate-ping ${
              isServerFull ? 'bg-red-400' : isHighPopulation ? 'bg-yellow-400' : 'bg-green-400'
            }`}></div>
            <div className={`absolute top-4 right-6 w-1.5 h-1.5 rounded-full animate-pulse ${
              isServerFull ? 'bg-red-300' : isHighPopulation ? 'bg-yellow-300' : 'bg-green-300'
            }`} style={{animationDelay: '0.5s'}}></div>
            <div className={`absolute bottom-3 left-3 w-0.5 h-0.5 rounded-full animate-bounce ${
              isServerFull ? 'bg-red-500' : isHighPopulation ? 'bg-yellow-500' : 'bg-green-500'
            }`} style={{animationDelay: '0.3s'}}></div>
            <div className={`absolute bottom-2 right-4 w-0.5 h-0.5 rounded-full animate-ping ${
              isServerFull ? 'bg-red-400' : isHighPopulation ? 'bg-yellow-400' : 'bg-green-400'
            }`} style={{animationDelay: '0.7s'}}></div>
          </div>

          {/* Content */}
          <div className="relative z-10 flex items-center justify-between">
            {/* Left Side - Server Info */}
            <div className="flex items-center space-x-3">
              {/* Pulsing Status */}
              <div className="relative">
                <div className={`w-4 h-4 rounded-full transition-all duration-300 ${
                  isServerFull 
                    ? 'bg-red-500' 
                    : isHighPopulation 
                      ? 'bg-yellow-500' 
                      : 'bg-green-500'
                }`}></div>
                <div className={`absolute inset-0 w-4 h-4 rounded-full animate-ping ${
                  isServerFull 
                    ? 'bg-red-500' 
                    : isHighPopulation 
                      ? 'bg-yellow-500' 
                      : 'bg-green-500'
                }`}></div>
              </div>

              {/* Server Status */}
              <div>
                <div className="flex items-center space-x-2">
                  <span className="text-white text-sm font-semibold">TGRS Server</span>
                  <span className={`text-xs px-2 py-0.5 rounded-full font-medium ${
                    isServerFull 
                      ? 'bg-red-500/20 text-red-400 border border-red-500/30' 
                      : isHighPopulation 
                        ? 'bg-yellow-500/20 text-yellow-400 border border-yellow-500/30' 
                        : 'bg-green-500/20 text-green-400 border border-green-500/30'
                  }`}>
                    LIVE
                  </span>
                </div>
                <p className="text-gray-400 text-xs">
                  {isServerFull 
                    ? 'Server is full!' 
                    : isHighPopulation 
                      ? 'High activity right now' 
                      : 'Join the action now'}
                </p>
              </div>
            </div>

            {/* Right Side - Player Count */}
            <div className="text-right">
              <div className="flex items-baseline space-x-1">
                <span className={`text-xl font-bold transition-colors duration-300 ${
                  isServerFull 
                    ? 'text-red-400' 
                    : isHighPopulation 
                      ? 'text-yellow-400' 
                      : 'text-green-400'
                }`}>
                  {playerCount}
                </span>
                <span className="text-gray-400 text-sm">/{maxPlayers}</span>
              </div>
              <div className="flex items-center justify-end space-x-1 mt-1">
                <i className="fas fa-users text-gray-400 text-xs"></i>
                <span className="text-gray-400 text-xs">online</span>
              </div>
            </div>
          </div>

          {/* Progress Bar */}
          <div className="mt-3 w-full bg-gray-700 rounded-full h-1.5 overflow-hidden">
            <div 
              className={`h-full transition-all duration-1000 ease-out ${
                isServerFull 
                  ? 'bg-gradient-to-r from-red-500 to-red-400' 
                  : isHighPopulation 
                    ? 'bg-gradient-to-r from-yellow-500 to-yellow-400' 
                    : 'bg-gradient-to-r from-green-500 to-green-400'
              }`}
              style={{ width: `${(playerCount / maxPlayers) * 100}%` }}
            ></div>
          </div>

          {/* Close Button */}
          <button 
            onClick={() => setIsVisible(false)}
            className="absolute top-2 right-2 w-6 h-6 rounded-full bg-gray-800/50 hover:bg-gray-700/50 transition-colors duration-200 flex items-center justify-center group"
          >
            <i className="fas fa-times text-gray-400 text-xs group-hover:text-white transition-colors duration-200"></i>
          </button>
        </div>

        {/* Action Indicator */}
        <div className="absolute -bottom-2 left-1/2 transform -translate-x-1/2">
          <div className={`px-3 py-1 rounded-full text-xs font-medium transition-all duration-500 ${
            isServerFull 
              ? 'bg-red-500 text-white' 
              : isHighPopulation 
                ? 'bg-yellow-500 text-black' 
                : 'bg-green-500 text-black'
          }`}>
            {isServerFull ? '🔥 FULL' : isHighPopulation ? '⚡ BUSY' : '✨ JOIN'}
          </div>
        </div>
      </div>
    </div>
  );
}
