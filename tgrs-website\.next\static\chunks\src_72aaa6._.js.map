{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/TGRS%20Website/tgrs-website/src/components/Navbar.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport Link from \"next/link\";\nimport Image from \"next/image\";\nimport { usePathname } from \"next/navigation\";\n\nconst Navbar = () => {\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n  const [scrolled, setScrolled] = useState(false);\n  const pathname = usePathname();\n\n  // Helper function to check if a link is active\n  const isActive = (href: string) => {\n    if (href === \"/\") {\n      return pathname === \"/\";\n    }\n    return pathname.startsWith(href);\n  };\n\n  useEffect(() => {\n    const handleScroll = () => {\n      if (window.scrollY > 10) {\n        setScrolled(true);\n      } else {\n        setScrolled(false);\n      }\n    };\n\n    window.addEventListener(\"scroll\", handleScroll);\n    return () => window.removeEventListener(\"scroll\", handleScroll);\n  }, []);\n\n  const toggleMenu = () => {\n    setIsMenuOpen(!isMenuOpen);\n  };\n\n  return (\n    <nav\n      className={`fixed top-0 left-0 w-full z-50 transition-all duration-500 ${\n        scrolled\n          ? \"bg-black/90 backdrop-blur-xl py-2 shadow-lg shadow-neon-orange/10\"\n          : \"bg-transparent py-4\"\n      }`}\n    >\n      {/* Subtle pattern overlay */}\n      <div className=\"absolute inset-0 pattern-dots opacity-20 pointer-events-none\"></div>\n\n      <div className=\"container mx-auto px-4 md:px-6 relative\">\n        <div className=\"flex items-center justify-between\">\n          {/* Enhanced Logo */}\n          <Link href=\"/\" className=\"flex items-center group\">\n            <div className=\"relative h-12 w-12 mr-3 transition-all duration-300 group-hover:scale-110 group-hover:rotate-3\">\n              <div className=\"absolute inset-0 bg-gradient-to-br from-neon-orange/20 to-neon-red/20 rounded-full blur-sm group-hover:blur-md transition-all duration-300\"></div>\n              <Image\n                src=\"/assets/tgrs-logo.png\"\n                alt=\"TGRS Logo\"\n                width={48}\n                height={48}\n                className=\"w-full h-full object-contain relative z-10 drop-shadow-lg\"\n                priority\n              />\n            </div>\n            <span className=\"text-white font-display font-bold text-xl tracking-wider group-hover:text-neon-orange transition-all duration-300\">\n              TGRS\n            </span>\n          </Link>\n\n          {/* Enhanced Desktop Navigation */}\n          <div className=\"hidden md:flex items-center space-x-1\">\n            {/* Navigation Links */}\n            <div className=\"flex items-center space-x-1 mr-6\">\n              <Link\n                href=\"/\"\n                className=\"relative px-4 py-2 text-white font-medium transition-all duration-300 group\"\n              >\n                <span className={`relative z-10 transition-colors duration-300 ${\n                  isActive(\"/\") ? \"text-neon-orange\" : \"group-hover:text-neon-orange\"\n                }`}>\n                  Home\n                </span>\n\n                {/* Active state particle effects */}\n                {isActive(\"/\") && (\n                  <div className=\"absolute inset-0 opacity-100 transition-all duration-500\">\n                    {/* Floating particles */}\n                    <div className=\"absolute top-1 left-2 w-1 h-1 bg-neon-orange rounded-full animate-ping\"></div>\n                    <div className=\"absolute top-2 right-3 w-1.5 h-1.5 bg-yellow-400 rounded-full animate-pulse\"></div>\n                    <div className=\"absolute bottom-2 left-3 w-1 h-1 bg-neon-orange rounded-full animate-bounce\"></div>\n                    <div className=\"absolute bottom-1 right-2 w-1 h-1 bg-yellow-300 rounded-full animate-ping\" style={{animationDelay: '0.5s'}}></div>\n                    <div className=\"absolute top-3 left-1/2 w-0.5 h-0.5 bg-neon-orange rounded-full animate-pulse\" style={{animationDelay: '0.3s'}}></div>\n                  </div>\n                )}\n\n                <div className={`absolute inset-0 bg-gradient-to-r from-neon-orange/10 to-neon-red/10 rounded-lg transition-all duration-300 scale-95 ${\n                  isActive(\"/\") ? \"opacity-30 scale-100\" : \"opacity-0 group-hover:opacity-100 group-hover:scale-100\"\n                }`}></div>\n              </Link>\n              <Link\n                href=\"/about\"\n                className=\"relative px-4 py-2 text-white font-medium transition-all duration-300 group\"\n              >\n                <span className={`relative z-10 transition-colors duration-300 ${\n                  isActive(\"/about\") ? \"text-neon-orange\" : \"group-hover:text-neon-orange\"\n                }`}>\n                  About\n                </span>\n\n                {/* Active state particle effects */}\n                {isActive(\"/about\") && (\n                  <div className=\"absolute inset-0 opacity-100 transition-all duration-500\">\n                    {/* Floating particles */}\n                    <div className=\"absolute top-1 left-2 w-1 h-1 bg-neon-orange rounded-full animate-ping\"></div>\n                    <div className=\"absolute top-2 right-3 w-1.5 h-1.5 bg-yellow-400 rounded-full animate-pulse\"></div>\n                    <div className=\"absolute bottom-2 left-3 w-1 h-1 bg-neon-orange rounded-full animate-bounce\"></div>\n                    <div className=\"absolute bottom-1 right-2 w-1 h-1 bg-yellow-300 rounded-full animate-ping\" style={{animationDelay: '0.5s'}}></div>\n                    <div className=\"absolute top-3 left-1/2 w-0.5 h-0.5 bg-neon-orange rounded-full animate-pulse\" style={{animationDelay: '0.3s'}}></div>\n                  </div>\n                )}\n\n                <div className={`absolute inset-0 bg-gradient-to-r from-neon-orange/10 to-neon-red/10 rounded-lg transition-all duration-300 scale-95 ${\n                  isActive(\"/about\") ? \"opacity-30 scale-100\" : \"opacity-0 group-hover:opacity-100 group-hover:scale-100\"\n                }`}></div>\n              </Link>\n              <Link\n                href=\"/features\"\n                className=\"relative px-4 py-2 text-white font-medium transition-all duration-300 group\"\n              >\n                <span className={`relative z-10 transition-colors duration-300 ${\n                  isActive(\"/features\") ? \"text-neon-orange\" : \"group-hover:text-neon-orange\"\n                }`}>\n                  Features\n                </span>\n\n                {/* Active state particle effects */}\n                {isActive(\"/features\") && (\n                  <div className=\"absolute inset-0 opacity-100 transition-all duration-500\">\n                    {/* Floating particles */}\n                    <div className=\"absolute top-1 left-2 w-1 h-1 bg-neon-orange rounded-full animate-ping\"></div>\n                    <div className=\"absolute top-2 right-3 w-1.5 h-1.5 bg-yellow-400 rounded-full animate-pulse\"></div>\n                    <div className=\"absolute bottom-2 left-3 w-1 h-1 bg-neon-orange rounded-full animate-bounce\"></div>\n                    <div className=\"absolute bottom-1 right-2 w-1 h-1 bg-yellow-300 rounded-full animate-ping\" style={{animationDelay: '0.5s'}}></div>\n                    <div className=\"absolute top-3 left-1/2 w-0.5 h-0.5 bg-neon-orange rounded-full animate-pulse\" style={{animationDelay: '0.3s'}}></div>\n                  </div>\n                )}\n\n                <div className={`absolute inset-0 bg-gradient-to-r from-neon-orange/10 to-neon-red/10 rounded-lg transition-all duration-300 scale-95 ${\n                  isActive(\"/features\") ? \"opacity-30 scale-100\" : \"opacity-0 group-hover:opacity-100 group-hover:scale-100\"\n                }`}></div>\n              </Link>\n              <Link\n                href=\"/rules\"\n                className=\"relative px-4 py-2 text-white font-medium transition-all duration-300 group\"\n              >\n                <span className={`relative z-10 transition-colors duration-300 ${\n                  isActive(\"/rules\") ? \"text-neon-orange\" : \"group-hover:text-neon-orange\"\n                }`}>\n                  Rules\n                </span>\n\n                {/* Active state particle effects */}\n                {isActive(\"/rules\") && (\n                  <div className=\"absolute inset-0 opacity-100 transition-all duration-500\">\n                    {/* Floating particles */}\n                    <div className=\"absolute top-1 left-2 w-1 h-1 bg-neon-orange rounded-full animate-ping\"></div>\n                    <div className=\"absolute top-2 right-3 w-1.5 h-1.5 bg-yellow-400 rounded-full animate-pulse\"></div>\n                    <div className=\"absolute bottom-2 left-3 w-1 h-1 bg-neon-orange rounded-full animate-bounce\"></div>\n                    <div className=\"absolute bottom-1 right-2 w-1 h-1 bg-yellow-300 rounded-full animate-ping\" style={{animationDelay: '0.5s'}}></div>\n                    <div className=\"absolute top-3 left-1/2 w-0.5 h-0.5 bg-neon-orange rounded-full animate-pulse\" style={{animationDelay: '0.3s'}}></div>\n                  </div>\n                )}\n\n                <div className={`absolute inset-0 bg-gradient-to-r from-neon-orange/10 to-neon-red/10 rounded-lg transition-all duration-300 scale-95 ${\n                  isActive(\"/rules\") ? \"opacity-30 scale-100\" : \"opacity-0 group-hover:opacity-100 group-hover:scale-100\"\n                }`}></div>\n              </Link>\n              <Link\n                href=\"/vip\"\n                className=\"relative px-4 py-2 text-white font-medium transition-all duration-300 group overflow-hidden\"\n              >\n                {/* Purple heart with particles */}\n                <div className={`absolute inset-0 transition-all duration-500 ${\n                  isActive(\"/vip\") ? \"opacity-100\" : \"opacity-0 group-hover:opacity-100\"\n                }`}>\n                  {/* Floating particles */}\n                  <div className=\"absolute top-1 left-2 w-1 h-1 bg-purple-400 rounded-full animate-ping\"></div>\n                  <div className=\"absolute top-3 right-3 w-1.5 h-1.5 bg-pink-400 rounded-full animate-pulse\"></div>\n                  <div className=\"absolute bottom-2 left-1 w-1 h-1 bg-purple-300 rounded-full animate-bounce\"></div>\n                  <div className=\"absolute bottom-1 right-2 w-1 h-1 bg-pink-300 rounded-full animate-ping\" style={{animationDelay: '0.5s'}}></div>\n                  <div className=\"absolute top-2 left-1/2 w-0.5 h-0.5 bg-purple-500 rounded-full animate-pulse\" style={{animationDelay: '0.3s'}}></div>\n                  <div className=\"absolute bottom-3 right-1 w-0.5 h-0.5 bg-pink-500 rounded-full animate-bounce\" style={{animationDelay: '0.7s'}}></div>\n                </div>\n\n                {/* Purple gradient background */}\n                <div className={`absolute inset-0 bg-gradient-to-r from-purple-600/20 via-pink-500/20 to-purple-600/20 rounded-lg transition-all duration-300 scale-95 ${\n                  isActive(\"/vip\") ? \"opacity-100 scale-100\" : \"opacity-0 group-hover:opacity-100 group-hover:scale-100\"\n                }`}></div>\n\n                {/* Glow effect */}\n                <div className={`absolute inset-0 bg-gradient-to-r from-purple-500/30 to-pink-500/30 rounded-lg blur-sm transition-all duration-500 scale-110 ${\n                  isActive(\"/vip\") ? \"opacity-60\" : \"opacity-0 group-hover:opacity-60\"\n                }`}></div>\n\n                <span className={`relative z-10 transition-colors duration-300 flex items-center ${\n                  isActive(\"/vip\") ? \"text-purple-300\" : \"group-hover:text-purple-300\"\n                }`}>\n                  <i className={`fas fa-heart mr-2 transition-colors duration-300 ${\n                    isActive(\"/vip\")\n                      ? \"text-pink-400 animate-pulse\"\n                      : \"text-purple-400 group-hover:text-pink-400 group-hover:animate-pulse\"\n                  }`}></i>\n                  VIP\n                </span>\n\n              </Link>\n            </div>\n\n            {/* Enhanced Play Now Button */}\n            <Link\n              href=\"cfx.re/join/o57lj7\"\n              className=\"relative px-6 py-2.5 rounded-full font-medium transition-all duration-300 group overflow-hidden\"\n            >\n              {/* Animated background */}\n              <div className=\"absolute inset-0 bg-gradient-to-r from-neon-orange to-neon-red transition-all duration-300 group-hover:scale-105\"></div>\n              <div className=\"absolute inset-0 bg-gradient-to-r from-neon-orange-light to-neon-red opacity-0 group-hover:opacity-100 transition-all duration-300\"></div>\n\n              {/* Glow effect */}\n              <div className=\"absolute inset-0 rounded-full bg-gradient-to-r from-neon-orange to-neon-red blur-md opacity-0 group-hover:opacity-50 transition-all duration-300 scale-110\"></div>\n\n              {/* Button text */}\n              <span className=\"relative z-10 text-white font-semibold tracking-wide group-hover:text-black transition-colors duration-300\">\n                Play Now\n              </span>\n            </Link>\n          </div>\n\n          {/* Enhanced Mobile Menu Button */}\n          <button\n            className=\"md:hidden relative p-2 text-white focus:outline-none group transition-all duration-300\"\n            onClick={toggleMenu}\n          >\n            <div className=\"absolute inset-0 bg-gradient-to-r from-neon-orange/20 to-neon-red/20 rounded-lg opacity-0 group-hover:opacity-100 transition-all duration-300\"></div>\n            <svg\n              className={`w-6 h-6 relative z-10 transition-all duration-300 ${isMenuOpen ? 'rotate-90 text-neon-orange' : 'rotate-0'}`}\n              fill=\"none\"\n              stroke=\"currentColor\"\n              viewBox=\"0 0 24 24\"\n              xmlns=\"http://www.w3.org/2000/svg\"\n            >\n              {isMenuOpen ? (\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  strokeWidth={2}\n                  d=\"M6 18L18 6M6 6l12 12\"\n                />\n              ) : (\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  strokeWidth={2}\n                  d=\"M4 6h16M4 12h16M4 18h16\"\n                />\n              )}\n            </svg>\n          </button>\n        </div>\n\n        {/* Enhanced Mobile Menu */}\n        {isMenuOpen && (\n          <div className=\"md:hidden mt-6 relative animate-menu-slide-down\">\n            {/* Background with pattern */}\n            <div className=\"absolute inset-0 pattern-circuit opacity-20 rounded-xl\"></div>\n            <div className=\"relative bg-black/90 backdrop-blur-xl rounded-xl p-6 border border-neon-orange/40 shadow-lg shadow-neon-orange/20 overflow-hidden\">\n              {/* Particle effects background */}\n              <div className=\"absolute inset-0 opacity-30 pointer-events-none\">\n                <div className=\"absolute top-2 left-4 w-1 h-1 bg-neon-orange rounded-full animate-ping\"></div>\n                <div className=\"absolute top-4 right-6 w-1.5 h-1.5 bg-yellow-400 rounded-full animate-pulse\"></div>\n                <div className=\"absolute bottom-6 left-3 w-1 h-1 bg-neon-orange rounded-full animate-bounce\"></div>\n                <div className=\"absolute bottom-3 right-4 w-1 h-1 bg-yellow-300 rounded-full animate-ping\" style={{animationDelay: '0.5s'}}></div>\n                <div className=\"absolute top-1/2 left-1/2 w-0.5 h-0.5 bg-neon-orange rounded-full animate-pulse\" style={{animationDelay: '0.3s'}}></div>\n                <div className=\"absolute top-8 right-2 w-0.5 h-0.5 bg-yellow-500 rounded-full animate-bounce\" style={{animationDelay: '0.7s'}}></div>\n              </div>\n              <div className=\"flex flex-col space-y-4\">\n                <Link\n                  href=\"/\"\n                  className=\"relative px-4 py-3 text-white font-medium transition-all duration-300 group rounded-lg\"\n                  onClick={() => setIsMenuOpen(false)}\n                >\n                  <div className={`absolute inset-0 bg-gradient-to-r from-neon-orange/10 to-neon-red/10 rounded-lg transition-all duration-300 ${\n                    isActive(\"/\") ? \"opacity-30\" : \"opacity-0 group-hover:opacity-100\"\n                  }`}></div>\n                  <span className={`relative z-10 transition-colors duration-300 ${\n                    isActive(\"/\") ? \"text-neon-orange\" : \"group-hover:text-neon-orange\"\n                  }`}>\n                    Home\n                    {isActive(\"/\") && <span className=\"ml-2 text-neon-orange animate-pulse\">✦</span>}\n                  </span>\n                </Link>\n                <Link\n                  href=\"/about\"\n                  className=\"relative px-4 py-3 text-white font-medium transition-all duration-300 group rounded-lg\"\n                  onClick={() => setIsMenuOpen(false)}\n                >\n                  <div className={`absolute inset-0 bg-gradient-to-r from-neon-orange/10 to-neon-red/10 rounded-lg transition-all duration-300 ${\n                    isActive(\"/about\") ? \"opacity-30\" : \"opacity-0 group-hover:opacity-100\"\n                  }`}></div>\n                  <span className={`relative z-10 transition-colors duration-300 ${\n                    isActive(\"/about\") ? \"text-neon-orange\" : \"group-hover:text-neon-orange\"\n                  }`}>\n                    About\n                    {isActive(\"/about\") && <span className=\"ml-2 text-neon-orange animate-pulse\">✦</span>}\n                  </span>\n                </Link>\n                <Link\n                  href=\"/features\"\n                  className=\"relative px-4 py-3 text-white font-medium transition-all duration-300 group rounded-lg\"\n                  onClick={() => setIsMenuOpen(false)}\n                >\n                  <div className={`absolute inset-0 bg-gradient-to-r from-neon-orange/10 to-neon-red/10 rounded-lg transition-all duration-300 ${\n                    isActive(\"/features\") ? \"opacity-30\" : \"opacity-0 group-hover:opacity-100\"\n                  }`}></div>\n                  <span className={`relative z-10 transition-colors duration-300 ${\n                    isActive(\"/features\") ? \"text-neon-orange\" : \"group-hover:text-neon-orange\"\n                  }`}>\n                    Features\n                    {isActive(\"/features\") && <span className=\"ml-2 text-neon-orange animate-pulse\">✦</span>}\n                  </span>\n                </Link>\n                <Link\n                  href=\"/rules\"\n                  className=\"relative px-4 py-3 text-white font-medium transition-all duration-300 group rounded-lg\"\n                  onClick={() => setIsMenuOpen(false)}\n                >\n                  <div className={`absolute inset-0 bg-gradient-to-r from-neon-orange/10 to-neon-red/10 rounded-lg transition-all duration-300 ${\n                    isActive(\"/rules\") ? \"opacity-30\" : \"opacity-0 group-hover:opacity-100\"\n                  }`}></div>\n                  <span className={`relative z-10 transition-colors duration-300 ${\n                    isActive(\"/rules\") ? \"text-neon-orange\" : \"group-hover:text-neon-orange\"\n                  }`}>\n                    Rules\n                    {isActive(\"/rules\") && <span className=\"ml-2 text-neon-orange animate-pulse\">✦</span>}\n                  </span>\n                </Link>\n                <Link\n                  href=\"/vip\"\n                  className=\"relative px-4 py-3 text-white font-medium transition-all duration-300 group rounded-lg overflow-hidden\"\n                  onClick={() => setIsMenuOpen(false)}\n                >\n                  {/* Purple heart with particles */}\n                  <div className=\"absolute inset-0 opacity-0 group-hover:opacity-100 transition-all duration-500\">\n                    {/* Floating particles */}\n                    <div className=\"absolute top-2 left-3 w-1 h-1 bg-purple-400 rounded-full animate-ping\"></div>\n                    <div className=\"absolute top-4 right-4 w-1.5 h-1.5 bg-pink-400 rounded-full animate-pulse\"></div>\n                    <div className=\"absolute bottom-3 left-2 w-1 h-1 bg-purple-300 rounded-full animate-bounce\"></div>\n                    <div className=\"absolute bottom-2 right-3 w-1 h-1 bg-pink-300 rounded-full animate-ping\" style={{animationDelay: '0.5s'}}></div>\n                    <div className=\"absolute top-3 left-1/2 w-0.5 h-0.5 bg-purple-500 rounded-full animate-pulse\" style={{animationDelay: '0.3s'}}></div>\n                    <div className=\"absolute bottom-4 right-2 w-0.5 h-0.5 bg-pink-500 rounded-full animate-bounce\" style={{animationDelay: '0.7s'}}></div>\n                  </div>\n\n                  {/* Purple gradient background */}\n                  <div className=\"absolute inset-0 bg-gradient-to-r from-purple-600/20 via-pink-500/20 to-purple-600/20 rounded-lg opacity-0 group-hover:opacity-100 transition-all duration-300\"></div>\n\n                  {/* Glow effect */}\n                  <div className=\"absolute inset-0 bg-gradient-to-r from-purple-500/30 to-pink-500/30 rounded-lg blur-sm opacity-0 group-hover:opacity-60 transition-all duration-500 scale-110\"></div>\n\n                  <span className={`relative z-10 transition-colors duration-300 flex items-center ${\n                    isActive(\"/vip\") ? \"text-purple-300\" : \"group-hover:text-purple-300\"\n                  }`}>\n                    <i className={`fas fa-heart mr-2 transition-colors duration-300 ${\n                      isActive(\"/vip\")\n                        ? \"text-pink-400 animate-pulse\"\n                        : \"text-purple-400 group-hover:text-pink-400 group-hover:animate-pulse\"\n                    }`}></i>\n                    VIP\n                    {isActive(\"/vip\") && <span className=\"ml-2 text-purple-400 animate-pulse\">♥</span>}\n                  </span>\n                </Link>\n\n                {/* Divider */}\n                <div className=\"h-px bg-gradient-to-r from-transparent via-neon-orange/50 to-transparent my-2\"></div>\n\n                {/* Enhanced Mobile Play Now Button */}\n                <Link\n                  href=\"cfx.re/join/o57lj7\"\n                  className=\"relative px-6 py-3 rounded-full font-medium transition-all duration-300 group overflow-hidden text-center\"\n                  onClick={() => setIsMenuOpen(false)}\n                >\n                  {/* Animated background */}\n                  <div className=\"absolute inset-0 bg-gradient-to-r from-neon-orange to-neon-red transition-all duration-300\"></div>\n                  <div className=\"absolute inset-0 bg-gradient-to-r from-neon-orange-light to-neon-red opacity-0 group-hover:opacity-100 transition-all duration-300\"></div>\n\n                  {/* Glow effect */}\n                  <div className=\"absolute inset-0 rounded-full bg-gradient-to-r from-neon-orange to-neon-red blur-sm opacity-0 group-hover:opacity-60 transition-all duration-300\"></div>\n\n                  {/* Button text */}\n                  <span className=\"relative z-10 text-white font-semibold tracking-wide group-hover:text-black transition-colors duration-300\">\n                    Play Now\n                  </span>\n                </Link>\n              </div>\n            </div>\n          </div>\n        )}\n      </div>\n    </nav>\n  );\n};\n\nexport default Navbar;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAOA,MAAM,SAAS;;IACb,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,+CAA+C;IAC/C,MAAM,WAAW,CAAC;QAChB,IAAI,SAAS,KAAK;YAChB,OAAO,aAAa;QACtB;QACA,OAAO,SAAS,UAAU,CAAC;IAC7B;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACR,MAAM;iDAAe;oBACnB,IAAI,OAAO,OAAO,GAAG,IAAI;wBACvB,YAAY;oBACd,OAAO;wBACL,YAAY;oBACd;gBACF;;YAEA,OAAO,gBAAgB,CAAC,UAAU;YAClC;oCAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;;QACpD;2BAAG,EAAE;IAEL,MAAM,aAAa;QACjB,cAAc,CAAC;IACjB;IAEA,qBACE,6LAAC;QACC,WAAW,CAAC,2DAA2D,EACrE,WACI,sEACA,uBACJ;;0BAGF,6LAAC;gBAAI,WAAU;;;;;;0BAEf,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;;kDACvB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC,gIAAA,CAAA,UAAK;gDACJ,KAAI;gDACJ,KAAI;gDACJ,OAAO;gDACP,QAAQ;gDACR,WAAU;gDACV,QAAQ;;;;;;;;;;;;kDAGZ,6LAAC;wCAAK,WAAU;kDAAoH;;;;;;;;;;;;0CAMtI,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;;kEAEV,6LAAC;wDAAK,WAAW,CAAC,6CAA6C,EAC7D,SAAS,OAAO,qBAAqB,gCACrC;kEAAE;;;;;;oDAKH,SAAS,sBACR,6LAAC;wDAAI,WAAU;;0EAEb,6LAAC;gEAAI,WAAU;;;;;;0EACf,6LAAC;gEAAI,WAAU;;;;;;0EACf,6LAAC;gEAAI,WAAU;;;;;;0EACf,6LAAC;gEAAI,WAAU;gEAA4E,OAAO;oEAAC,gBAAgB;gEAAM;;;;;;0EACzH,6LAAC;gEAAI,WAAU;gEAAgF,OAAO;oEAAC,gBAAgB;gEAAM;;;;;;;;;;;;kEAIjI,6LAAC;wDAAI,WAAW,CAAC,qHAAqH,EACpI,SAAS,OAAO,yBAAyB,2DACzC;;;;;;;;;;;;0DAEJ,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;;kEAEV,6LAAC;wDAAK,WAAW,CAAC,6CAA6C,EAC7D,SAAS,YAAY,qBAAqB,gCAC1C;kEAAE;;;;;;oDAKH,SAAS,2BACR,6LAAC;wDAAI,WAAU;;0EAEb,6LAAC;gEAAI,WAAU;;;;;;0EACf,6LAAC;gEAAI,WAAU;;;;;;0EACf,6LAAC;gEAAI,WAAU;;;;;;0EACf,6LAAC;gEAAI,WAAU;gEAA4E,OAAO;oEAAC,gBAAgB;gEAAM;;;;;;0EACzH,6LAAC;gEAAI,WAAU;gEAAgF,OAAO;oEAAC,gBAAgB;gEAAM;;;;;;;;;;;;kEAIjI,6LAAC;wDAAI,WAAW,CAAC,qHAAqH,EACpI,SAAS,YAAY,yBAAyB,2DAC9C;;;;;;;;;;;;0DAEJ,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;;kEAEV,6LAAC;wDAAK,WAAW,CAAC,6CAA6C,EAC7D,SAAS,eAAe,qBAAqB,gCAC7C;kEAAE;;;;;;oDAKH,SAAS,8BACR,6LAAC;wDAAI,WAAU;;0EAEb,6LAAC;gEAAI,WAAU;;;;;;0EACf,6LAAC;gEAAI,WAAU;;;;;;0EACf,6LAAC;gEAAI,WAAU;;;;;;0EACf,6LAAC;gEAAI,WAAU;gEAA4E,OAAO;oEAAC,gBAAgB;gEAAM;;;;;;0EACzH,6LAAC;gEAAI,WAAU;gEAAgF,OAAO;oEAAC,gBAAgB;gEAAM;;;;;;;;;;;;kEAIjI,6LAAC;wDAAI,WAAW,CAAC,qHAAqH,EACpI,SAAS,eAAe,yBAAyB,2DACjD;;;;;;;;;;;;0DAEJ,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;;kEAEV,6LAAC;wDAAK,WAAW,CAAC,6CAA6C,EAC7D,SAAS,YAAY,qBAAqB,gCAC1C;kEAAE;;;;;;oDAKH,SAAS,2BACR,6LAAC;wDAAI,WAAU;;0EAEb,6LAAC;gEAAI,WAAU;;;;;;0EACf,6LAAC;gEAAI,WAAU;;;;;;0EACf,6LAAC;gEAAI,WAAU;;;;;;0EACf,6LAAC;gEAAI,WAAU;gEAA4E,OAAO;oEAAC,gBAAgB;gEAAM;;;;;;0EACzH,6LAAC;gEAAI,WAAU;gEAAgF,OAAO;oEAAC,gBAAgB;gEAAM;;;;;;;;;;;;kEAIjI,6LAAC;wDAAI,WAAW,CAAC,qHAAqH,EACpI,SAAS,YAAY,yBAAyB,2DAC9C;;;;;;;;;;;;0DAEJ,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;;kEAGV,6LAAC;wDAAI,WAAW,CAAC,6CAA6C,EAC5D,SAAS,UAAU,gBAAgB,qCACnC;;0EAEA,6LAAC;gEAAI,WAAU;;;;;;0EACf,6LAAC;gEAAI,WAAU;;;;;;0EACf,6LAAC;gEAAI,WAAU;;;;;;0EACf,6LAAC;gEAAI,WAAU;gEAA0E,OAAO;oEAAC,gBAAgB;gEAAM;;;;;;0EACvH,6LAAC;gEAAI,WAAU;gEAA+E,OAAO;oEAAC,gBAAgB;gEAAM;;;;;;0EAC5H,6LAAC;gEAAI,WAAU;gEAAgF,OAAO;oEAAC,gBAAgB;gEAAM;;;;;;;;;;;;kEAI/H,6LAAC;wDAAI,WAAW,CAAC,sIAAsI,EACrJ,SAAS,UAAU,0BAA0B,2DAC7C;;;;;;kEAGF,6LAAC;wDAAI,WAAW,CAAC,6HAA6H,EAC5I,SAAS,UAAU,eAAe,oCAClC;;;;;;kEAEF,6LAAC;wDAAK,WAAW,CAAC,+DAA+D,EAC/E,SAAS,UAAU,oBAAoB,+BACvC;;0EACA,6LAAC;gEAAE,WAAW,CAAC,iDAAiD,EAC9D,SAAS,UACL,gCACA,uEACJ;;;;;;4DAAM;;;;;;;;;;;;;;;;;;;kDAQd,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;;0DAGV,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAI,WAAU;;;;;;0DAGf,6LAAC;gDAAI,WAAU;;;;;;0DAGf,6LAAC;gDAAK,WAAU;0DAA6G;;;;;;;;;;;;;;;;;;0CAOjI,6LAAC;gCACC,WAAU;gCACV,SAAS;;kDAET,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCACC,WAAW,CAAC,kDAAkD,EAAE,aAAa,+BAA+B,YAAY;wCACxH,MAAK;wCACL,QAAO;wCACP,SAAQ;wCACR,OAAM;kDAEL,2BACC,6LAAC;4CACC,eAAc;4CACd,gBAAe;4CACf,aAAa;4CACb,GAAE;;;;;iEAGJ,6LAAC;4CACC,eAAc;4CACd,gBAAe;4CACf,aAAa;4CACb,GAAE;;;;;;;;;;;;;;;;;;;;;;;oBAQX,4BACC,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAI,WAAU;gDAA4E,OAAO;oDAAC,gBAAgB;gDAAM;;;;;;0DACzH,6LAAC;gDAAI,WAAU;gDAAkF,OAAO;oDAAC,gBAAgB;gDAAM;;;;;;0DAC/H,6LAAC;gDAAI,WAAU;gDAA+E,OAAO;oDAAC,gBAAgB;gDAAM;;;;;;;;;;;;kDAE9H,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;gDACV,SAAS,IAAM,cAAc;;kEAE7B,6LAAC;wDAAI,WAAW,CAAC,4GAA4G,EAC3H,SAAS,OAAO,eAAe,qCAC/B;;;;;;kEACF,6LAAC;wDAAK,WAAW,CAAC,6CAA6C,EAC7D,SAAS,OAAO,qBAAqB,gCACrC;;4DAAE;4DAED,SAAS,sBAAQ,6LAAC;gEAAK,WAAU;0EAAsC;;;;;;;;;;;;;;;;;;0DAG5E,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;gDACV,SAAS,IAAM,cAAc;;kEAE7B,6LAAC;wDAAI,WAAW,CAAC,4GAA4G,EAC3H,SAAS,YAAY,eAAe,qCACpC;;;;;;kEACF,6LAAC;wDAAK,WAAW,CAAC,6CAA6C,EAC7D,SAAS,YAAY,qBAAqB,gCAC1C;;4DAAE;4DAED,SAAS,2BAAa,6LAAC;gEAAK,WAAU;0EAAsC;;;;;;;;;;;;;;;;;;0DAGjF,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;gDACV,SAAS,IAAM,cAAc;;kEAE7B,6LAAC;wDAAI,WAAW,CAAC,4GAA4G,EAC3H,SAAS,eAAe,eAAe,qCACvC;;;;;;kEACF,6LAAC;wDAAK,WAAW,CAAC,6CAA6C,EAC7D,SAAS,eAAe,qBAAqB,gCAC7C;;4DAAE;4DAED,SAAS,8BAAgB,6LAAC;gEAAK,WAAU;0EAAsC;;;;;;;;;;;;;;;;;;0DAGpF,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;gDACV,SAAS,IAAM,cAAc;;kEAE7B,6LAAC;wDAAI,WAAW,CAAC,4GAA4G,EAC3H,SAAS,YAAY,eAAe,qCACpC;;;;;;kEACF,6LAAC;wDAAK,WAAW,CAAC,6CAA6C,EAC7D,SAAS,YAAY,qBAAqB,gCAC1C;;4DAAE;4DAED,SAAS,2BAAa,6LAAC;gEAAK,WAAU;0EAAsC;;;;;;;;;;;;;;;;;;0DAGjF,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;gDACV,SAAS,IAAM,cAAc;;kEAG7B,6LAAC;wDAAI,WAAU;;0EAEb,6LAAC;gEAAI,WAAU;;;;;;0EACf,6LAAC;gEAAI,WAAU;;;;;;0EACf,6LAAC;gEAAI,WAAU;;;;;;0EACf,6LAAC;gEAAI,WAAU;gEAA0E,OAAO;oEAAC,gBAAgB;gEAAM;;;;;;0EACvH,6LAAC;gEAAI,WAAU;gEAA+E,OAAO;oEAAC,gBAAgB;gEAAM;;;;;;0EAC5H,6LAAC;gEAAI,WAAU;gEAAgF,OAAO;oEAAC,gBAAgB;gEAAM;;;;;;;;;;;;kEAI/H,6LAAC;wDAAI,WAAU;;;;;;kEAGf,6LAAC;wDAAI,WAAU;;;;;;kEAEf,6LAAC;wDAAK,WAAW,CAAC,+DAA+D,EAC/E,SAAS,UAAU,oBAAoB,+BACvC;;0EACA,6LAAC;gEAAE,WAAW,CAAC,iDAAiD,EAC9D,SAAS,UACL,gCACA,uEACJ;;;;;;4DAAM;4DAEP,SAAS,yBAAW,6LAAC;gEAAK,WAAU;0EAAqC;;;;;;;;;;;;;;;;;;0DAK9E,6LAAC;gDAAI,WAAU;;;;;;0DAGf,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;gDACV,SAAS,IAAM,cAAc;;kEAG7B,6LAAC;wDAAI,WAAU;;;;;;kEACf,6LAAC;wDAAI,WAAU;;;;;;kEAGf,6LAAC;wDAAI,WAAU;;;;;;kEAGf,6LAAC;wDAAK,WAAU;kEAA6G;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAW/I;GAhZM;;QAGa,qIAAA,CAAA,cAAW;;;KAHxB;uCAkZS"}}, {"offset": {"line": 1047, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1053, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/TGRS%20Website/tgrs-website/src/components/VIPCardNew.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Image from 'next/image';\nimport Button from './Button';\n\ninterface Feature {\n  text: string;\n  details?: string;\n  highlight?: string;\n  example?: string;\n}\n\ninterface TimeFeature {\n  text: string;\n  duration: string;\n}\n\ninterface Ped {\n  name: string;\n  image: string;\n}\n\ninterface ClothingItem {\n  name: string;\n  description: string;\n  variants: number;\n}\n\ninterface VIPCardProps {\n  tier: number;\n  price: number;\n  title?: string;\n  description: string;\n  icon: string;\n  mainFeatures: Feature[];\n  timeFeatures: TimeFeature[];\n  clothing: ClothingItem[];\n  peds: Ped[];\n  isPopular?: boolean;\n  tiltDirection?: 'left' | 'right';\n  pattern?: string;\n  className?: string;\n  currency?: string;\n}\n\nconst VIPCard = ({\n  tier,\n  price,\n  title,\n  description,\n  icon,\n  mainFeatures,\n  timeFeatures,\n  clothing,\n  peds,\n  isPopular = false,\n  className = \"\",\n  currency = \"₹\"\n}: VIPCardProps) => {\n  const [activeTab, setActiveTab] = useState<'features' | 'peds' | 'clothing'>('features');\n  const [isHovered, setIsHovered] = useState(false);\n\n  // Get tier-specific styling and icons\n  let tierBorder = \"border-orange-500\";\n  let tierText = \"text-orange-400\";\n  let tierAccent = \"bg-orange-500\";\n  let tierGradient = \"from-orange-500/30 to-yellow-500/30\";\n  let tierGlow = \"shadow-orange-500/30\";\n  let tierIcon = \"fas fa-star\";\n  let isSpecialTier = false;\n\n  if (tier === 1) {\n    // Maroon color\n    tierBorder = \"border-red-800\";\n    tierText = \"text-red-300\";\n    tierAccent = \"bg-red-800\";\n    tierGradient = \"from-red-800/30 to-red-700/30\";\n    tierGlow = \"shadow-red-800/30\";\n    tierIcon = \"fas fa-medal\";\n  } else if (tier === 2) {\n    // Silver color\n    tierBorder = \"border-gray-400\";\n    tierText = \"text-gray-300\";\n    tierAccent = \"bg-gray-400\";\n    tierGradient = \"from-gray-400/30 to-gray-300/30\";\n    tierGlow = \"shadow-gray-400/30\";\n    tierIcon = \"fas fa-award\";\n  } else if (tier === 3) {\n    // Bronze color\n    tierBorder = \"border-amber-600\";\n    tierText = \"text-amber-400\";\n    tierAccent = \"bg-amber-600\";\n    tierGradient = \"from-amber-600/30 to-amber-500/30\";\n    tierGlow = \"shadow-amber-600/30\";\n    tierIcon = \"fas fa-trophy\";\n  } else if (tier === 4) {\n    // Violet color\n    tierBorder = \"border-violet-500\";\n    tierText = \"text-violet-400\";\n    tierAccent = \"bg-violet-500\";\n    tierGradient = \"from-violet-500/30 to-violet-400/30\";\n    tierGlow = \"shadow-violet-500/30\";\n    tierIcon = \"fas fa-gem\";\n    isSpecialTier = true;\n  } else if (tier === 5) {\n    // Golden color (brighter)\n    tierBorder = \"border-yellow-400\";\n    tierText = \"text-yellow-300\";\n    tierAccent = \"bg-yellow-400\";\n    tierGradient = \"from-yellow-400/30 to-yellow-300/30\";\n    tierGlow = \"shadow-yellow-400/30\";\n    tierIcon = \"fas fa-crown\";\n    isSpecialTier = true;\n  }\n\n  return (\n    <div className={`relative ${className} group`}>\n      {/* Popular Badge */}\n      {isPopular && (\n        <div className=\"absolute -top-2 right-4 z-30\">\n          <div className=\"bg-gradient-to-r from-yellow-400 to-orange-500 px-3 py-1 rounded-md text-black text-xs font-bold shadow-lg\">\n            POPULAR\n          </div>\n        </div>\n      )}\n\n      {/* Card Glow Effect - All Cards */}\n      <div className={`absolute inset-0 rounded-3xl bg-gradient-to-r ${tierGradient} opacity-20 blur-xl pointer-events-none`}></div>\n\n      {/* Enhanced Glow for Special Tiers */}\n      {isSpecialTier && (\n        <div className={`absolute inset-0 rounded-3xl bg-gradient-to-r ${tierGradient} opacity-40 blur-2xl pointer-events-none animate-pulse`}></div>\n      )}\n\n      <div className={`bg-black/95 backdrop-blur-sm rounded-3xl border-2 ${tierBorder} hover:border-opacity-100 transition-all duration-500 overflow-hidden relative h-full flex flex-col shadow-xl ${tierGlow} group-hover:shadow-2xl group-hover:${tierGlow.replace('/30', '/50')}`}>\n\n        {/* Header */}\n        <div className=\"p-8 border-b border-gray-700/30 bg-gradient-to-r from-gray-900/50 to-black/50 relative overflow-hidden\">\n          {/* Shine Effect */}\n          <div className=\"absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent -skew-x-12 translate-x-[-100%] group-hover:translate-x-[200%] transition-transform duration-1000 ease-out\"></div>\n          <div className=\"flex items-center justify-between mb-6\">\n            <div className=\"flex items-center space-x-4\">\n              <div className={`w-16 h-16 rounded-2xl bg-gradient-to-br ${tierGradient} border-2 ${tierBorder} flex items-center justify-center ${isSpecialTier ? 'animate-pulse shadow-lg shadow-current/50' : 'shadow-lg'}`}>\n                <i className={`${tierIcon} ${tierText} text-2xl`}></i>\n              </div>\n              <div>\n                <h3 className=\"text-3xl font-bold text-white mb-1\">{title || `VIP ${tier}`}</h3>\n                <p className=\"text-gray-300 text-lg font-medium\">{description}</p>\n              </div>\n            </div>\n            <div className=\"text-right\">\n              <div className=\"text-5xl font-bold text-white mb-1\">{currency}{price.toLocaleString()}</div>\n              <div className=\"text-gray-400 text-base font-medium\">One-time purchase</div>\n            </div>\n          </div>\n        </div>\n\n        {/* Navigation */}\n        <div className=\"px-8 py-4 border-b border-gray-700/30 bg-gray-900/30\">\n          <div className=\"flex space-x-3\">\n            <button\n              onClick={() => setActiveTab('features')}\n              className={`flex-1 px-4 py-3 rounded-xl text-sm font-bold transition-all duration-300 text-center whitespace-nowrap ${\n                activeTab === 'features'\n                  ? `${tierText} bg-gray-800/90 shadow-lg border-2 border-current/40`\n                  : 'text-gray-400 hover:text-white hover:bg-gray-800/50 border-2 border-transparent'\n              }`}\n            >\n              Features\n            </button>\n            {peds.length > 0 && (\n              <button\n                onClick={() => setActiveTab('peds')}\n                className={`flex-1 px-4 py-3 rounded-xl text-sm font-bold transition-all duration-300 text-center whitespace-nowrap ${\n                  activeTab === 'peds'\n                    ? `${tierText} bg-gray-800/90 shadow-lg border-2 border-current/40`\n                    : 'text-gray-400 hover:text-white hover:bg-gray-800/50 border-2 border-transparent'\n                }`}\n              >\n                Peds ({peds.length})\n              </button>\n            )}\n            {clothing.length > 0 && (\n              <button\n                onClick={() => setActiveTab('clothing')}\n                className={`flex-1 px-4 py-3 rounded-xl text-sm font-bold transition-all duration-300 text-center whitespace-nowrap ${\n                  activeTab === 'clothing'\n                    ? `${tierText} bg-gray-800/90 shadow-lg border-2 border-current/40`\n                    : 'text-gray-400 hover:text-white hover:bg-gray-800/50 border-2 border-transparent'\n                }`}\n              >\n                Clothing\n              </button>\n            )}\n          </div>\n        </div>\n\n        {/* Content */}\n        <div className=\"p-8 flex-grow\">\n          {/* Features Tab */}\n          {activeTab === 'features' && (\n            <div className=\"space-y-5\">\n              {mainFeatures.map((feature, index) => (\n                <div key={index} className=\"flex items-start space-x-4 p-5 rounded-2xl bg-gray-800/40 border border-gray-700/40 hover:bg-gray-800/60 hover:border-gray-600/60 transition-all duration-300 shadow-lg\">\n                  <div className={`w-3 h-3 rounded-full ${tierAccent} mt-3 flex-shrink-0 shadow-lg`}></div>\n                  <div className=\"flex-grow\">\n                    <div className=\"text-white font-bold text-lg mb-3\">{feature.text}</div>\n                    {feature.details && (\n                      <div className=\"text-gray-300 text-base mb-4\">{feature.details}</div>\n                    )}\n                    <div className=\"flex flex-wrap gap-3\">\n                      {feature.highlight && (\n                        <span className={`${tierText} text-base font-bold bg-gray-800/80 px-4 py-2 rounded-xl border-2 ${tierBorder} shadow-lg`}>\n                          {feature.highlight}\n                        </span>\n                      )}\n                      {feature.example && (\n                        <span className=\"text-gray-200 text-base font-mono bg-gray-900/80 px-4 py-2 rounded-xl border border-gray-600 shadow-lg\">\n                          {feature.example}\n                        </span>\n                      )}\n                    </div>\n                  </div>\n                </div>\n              ))}\n\n              {/* Time Features */}\n              {timeFeatures.length > 0 && (\n                <div className=\"mt-8 p-6 bg-gray-800/30 rounded-2xl border border-gray-700/40 shadow-lg\">\n                  <h5 className=\"text-white font-bold mb-5 text-xl\">Time-Limited Benefits</h5>\n                  <div className=\"space-y-4\">\n                    {timeFeatures.map((feature, index) => (\n                      <div key={index} className=\"flex items-center justify-between p-4 bg-gray-800/50 rounded-xl border border-gray-700/50 hover:bg-gray-800/70 transition-all duration-300\">\n                        <span className=\"text-white text-lg font-semibold\">{feature.text}</span>\n                        <span className={`${tierText} text-base font-bold px-4 py-2 rounded-xl bg-gray-800/80 border-2 ${tierBorder} shadow-lg`}>\n                          {feature.duration}\n                        </span>\n                      </div>\n                    ))}\n                  </div>\n                </div>\n              )}\n            </div>\n          )}\n\n          {/* Peds Tab */}\n          {activeTab === 'peds' && peds.length > 0 && (\n            <div>\n              <h5 className=\"text-white font-semibold mb-6 text-lg\">Character Models ({peds.length})</h5>\n              <div className=\"grid grid-cols-3 gap-4\">\n                {peds.map((ped, index) => (\n                  <div key={index} className=\"group relative\">\n                    <div className=\"aspect-square rounded-xl overflow-hidden bg-gray-800/50 border border-gray-700/50 group-hover:border-gray-600 transition-all duration-300 shadow-lg\">\n                      <Image\n                        src={ped.image}\n                        alt={ped.name}\n                        width={120}\n                        height={120}\n                        className=\"w-full h-full object-cover group-hover:scale-110 transition-transform duration-300\"\n                      />\n                      <div className=\"absolute inset-0 bg-gradient-to-t from-black/80 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"></div>\n                      <div className=\"absolute bottom-2 left-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300\">\n                        <div className=\"text-white text-sm font-semibold truncate text-center bg-black/90 rounded-lg px-3 py-2\">{ped.name}</div>\n                      </div>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </div>\n          )}\n\n          {/* Clothing Tab */}\n          {activeTab === 'clothing' && clothing.length > 0 && (\n            <div>\n              <h5 className=\"text-white font-semibold mb-4 text-base\">Exclusive Clothing</h5>\n              <div className=\"space-y-4\">\n                {clothing.map((item, index) => (\n                  <div key={index} className=\"flex items-center space-x-4 p-4 bg-gray-800/30 rounded-lg border border-gray-700/30 hover:bg-gray-800/50 transition-colors\">\n                    <div className=\"w-10 h-10 rounded-lg bg-orange-500/20 flex items-center justify-center\">\n                      <i className=\"fas fa-tshirt text-orange-400 text-base\"></i>\n                    </div>\n                    <div className=\"flex-grow\">\n                      <h6 className=\"text-white font-semibold text-base mb-1\">{item.name}</h6>\n                      <p className=\"text-gray-400 text-sm mb-2\">{item.description}</p>\n                      {item.variants > 0 && (\n                        <span className=\"text-orange-400 text-sm font-semibold bg-orange-500/10 px-3 py-1 rounded border border-orange-500/20\">\n                          {item.variants} variants\n                        </span>\n                      )}\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </div>\n          )}\n        </div>\n\n        {/* Footer */}\n        <div className=\"p-6 border-t border-gray-700/30 bg-gray-900/20 flex justify-center\">\n          <button\n            className={`relative overflow-hidden px-6 py-2 rounded-lg font-bold text-base transition-all duration-300 ${\n              isSpecialTier\n                ? `bg-gradient-to-r ${tierGradient} ${tierText} border-2 ${tierBorder} hover:shadow-lg hover:shadow-current/40`\n                : 'bg-gray-800/80 text-white border-2 border-gray-600/50 hover:bg-gray-700/80 hover:border-gray-500/70'\n            } hover:scale-110 flex items-center justify-center gap-2 group shadow-md`}\n            onMouseEnter={() => {\n              if (tier === 4 || tier === 5) {\n                setIsHovered(true);\n              }\n            }}\n            onMouseLeave={() => {\n              if (tier === 4 || tier === 5) {\n                setIsHovered(false);\n              }\n            }}\n            onClick={() => window.open('https://discord.gg/GAMravHDnB', '_blank')}\n          >\n            <span className=\"text-base\">🎫</span>\n            <span>Grab Now</span>\n\n            {/* VIP 4 Violet Glow Effect */}\n            {tier === 4 && isHovered && (\n              <div className=\"absolute inset-0 bg-violet-500/30 rounded-lg animate-pulse pointer-events-none\"></div>\n            )}\n\n            {/* VIP 5 Shimmer Effect */}\n            {tier === 5 && isHovered && (\n              <>\n                <div className=\"absolute inset-0 bg-yellow-400/30 rounded-lg animate-pulse pointer-events-none\"></div>\n                <div className=\"absolute inset-0 bg-gradient-to-r from-transparent via-yellow-300/50 to-transparent -skew-x-12 translate-x-[-100%] animate-shimmer pointer-events-none\"></div>\n              </>\n            )}\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default VIPCard;\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AA8CA,MAAM,UAAU,CAAC,EACf,IAAI,EACJ,KAAK,EACL,KAAK,EACL,WAAW,EACX,IAAI,EACJ,YAAY,EACZ,YAAY,EACZ,QAAQ,EACR,IAAI,EACJ,YAAY,KAAK,EACjB,YAAY,EAAE,EACd,WAAW,GAAG,EACD;;IACb,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoC;IAC7E,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,sCAAsC;IACtC,IAAI,aAAa;IACjB,IAAI,WAAW;IACf,IAAI,aAAa;IACjB,IAAI,eAAe;IACnB,IAAI,WAAW;IACf,IAAI,WAAW;IACf,IAAI,gBAAgB;IAEpB,IAAI,SAAS,GAAG;QACd,eAAe;QACf,aAAa;QACb,WAAW;QACX,aAAa;QACb,eAAe;QACf,WAAW;QACX,WAAW;IACb,OAAO,IAAI,SAAS,GAAG;QACrB,eAAe;QACf,aAAa;QACb,WAAW;QACX,aAAa;QACb,eAAe;QACf,WAAW;QACX,WAAW;IACb,OAAO,IAAI,SAAS,GAAG;QACrB,eAAe;QACf,aAAa;QACb,WAAW;QACX,aAAa;QACb,eAAe;QACf,WAAW;QACX,WAAW;IACb,OAAO,IAAI,SAAS,GAAG;QACrB,eAAe;QACf,aAAa;QACb,WAAW;QACX,aAAa;QACb,eAAe;QACf,WAAW;QACX,WAAW;QACX,gBAAgB;IAClB,OAAO,IAAI,SAAS,GAAG;QACrB,0BAA0B;QAC1B,aAAa;QACb,WAAW;QACX,aAAa;QACb,eAAe;QACf,WAAW;QACX,WAAW;QACX,gBAAgB;IAClB;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAC,SAAS,EAAE,UAAU,MAAM,CAAC;;YAE1C,2BACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BAA6G;;;;;;;;;;;0BAOhI,6LAAC;gBAAI,WAAW,CAAC,8CAA8C,EAAE,aAAa,uCAAuC,CAAC;;;;;;YAGrH,+BACC,6LAAC;gBAAI,WAAW,CAAC,8CAA8C,EAAE,aAAa,sDAAsD,CAAC;;;;;;0BAGvI,6LAAC;gBAAI,WAAW,CAAC,kDAAkD,EAAE,WAAW,8GAA8G,EAAE,SAAS,oCAAoC,EAAE,SAAS,OAAO,CAAC,OAAO,QAAQ;;kCAG7Q,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAW,CAAC,wCAAwC,EAAE,aAAa,UAAU,EAAE,WAAW,kCAAkC,EAAE,gBAAgB,8CAA8C,aAAa;0DAC5M,cAAA,6LAAC;oDAAE,WAAW,GAAG,SAAS,CAAC,EAAE,SAAS,SAAS,CAAC;;;;;;;;;;;0DAElD,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAAsC,SAAS,CAAC,IAAI,EAAE,MAAM;;;;;;kEAC1E,6LAAC;wDAAE,WAAU;kEAAqC;;;;;;;;;;;;;;;;;;kDAGtD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;oDAAsC;oDAAU,MAAM,cAAc;;;;;;;0DACnF,6LAAC;gDAAI,WAAU;0DAAsC;;;;;;;;;;;;;;;;;;;;;;;;kCAM3D,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS,IAAM,aAAa;oCAC5B,WAAW,CAAC,wGAAwG,EAClH,cAAc,aACV,GAAG,SAAS,oDAAoD,CAAC,GACjE,mFACJ;8CACH;;;;;;gCAGA,KAAK,MAAM,GAAG,mBACb,6LAAC;oCACC,SAAS,IAAM,aAAa;oCAC5B,WAAW,CAAC,wGAAwG,EAClH,cAAc,SACV,GAAG,SAAS,oDAAoD,CAAC,GACjE,mFACJ;;wCACH;wCACQ,KAAK,MAAM;wCAAC;;;;;;;gCAGtB,SAAS,MAAM,GAAG,mBACjB,6LAAC;oCACC,SAAS,IAAM,aAAa;oCAC5B,WAAW,CAAC,wGAAwG,EAClH,cAAc,aACV,GAAG,SAAS,oDAAoD,CAAC,GACjE,mFACJ;8CACH;;;;;;;;;;;;;;;;;kCAQP,6LAAC;wBAAI,WAAU;;4BAEZ,cAAc,4BACb,6LAAC;gCAAI,WAAU;;oCACZ,aAAa,GAAG,CAAC,CAAC,SAAS,sBAC1B,6LAAC;4CAAgB,WAAU;;8DACzB,6LAAC;oDAAI,WAAW,CAAC,qBAAqB,EAAE,WAAW,6BAA6B,CAAC;;;;;;8DACjF,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEAAqC,QAAQ,IAAI;;;;;;wDAC/D,QAAQ,OAAO,kBACd,6LAAC;4DAAI,WAAU;sEAAgC,QAAQ,OAAO;;;;;;sEAEhE,6LAAC;4DAAI,WAAU;;gEACZ,QAAQ,SAAS,kBAChB,6LAAC;oEAAK,WAAW,GAAG,SAAS,kEAAkE,EAAE,WAAW,UAAU,CAAC;8EACpH,QAAQ,SAAS;;;;;;gEAGrB,QAAQ,OAAO,kBACd,6LAAC;oEAAK,WAAU;8EACb,QAAQ,OAAO;;;;;;;;;;;;;;;;;;;2CAfhB;;;;;oCAwBX,aAAa,MAAM,GAAG,mBACrB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAoC;;;;;;0DAClD,6LAAC;gDAAI,WAAU;0DACZ,aAAa,GAAG,CAAC,CAAC,SAAS,sBAC1B,6LAAC;wDAAgB,WAAU;;0EACzB,6LAAC;gEAAK,WAAU;0EAAoC,QAAQ,IAAI;;;;;;0EAChE,6LAAC;gEAAK,WAAW,GAAG,SAAS,kEAAkE,EAAE,WAAW,UAAU,CAAC;0EACpH,QAAQ,QAAQ;;;;;;;uDAHX;;;;;;;;;;;;;;;;;;;;;;4BAcrB,cAAc,UAAU,KAAK,MAAM,GAAG,mBACrC,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;;4CAAwC;4CAAmB,KAAK,MAAM;4CAAC;;;;;;;kDACrF,6LAAC;wCAAI,WAAU;kDACZ,KAAK,GAAG,CAAC,CAAC,KAAK,sBACd,6LAAC;gDAAgB,WAAU;0DACzB,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,gIAAA,CAAA,UAAK;4DACJ,KAAK,IAAI,KAAK;4DACd,KAAK,IAAI,IAAI;4DACb,OAAO;4DACP,QAAQ;4DACR,WAAU;;;;;;sEAEZ,6LAAC;4DAAI,WAAU;;;;;;sEACf,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAI,WAAU;0EAA0F,IAAI,IAAI;;;;;;;;;;;;;;;;;+CAX7G;;;;;;;;;;;;;;;;4BAqBjB,cAAc,cAAc,SAAS,MAAM,GAAG,mBAC7C,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAA0C;;;;;;kDACxD,6LAAC;wCAAI,WAAU;kDACZ,SAAS,GAAG,CAAC,CAAC,MAAM,sBACnB,6LAAC;gDAAgB,WAAU;;kEACzB,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAE,WAAU;;;;;;;;;;;kEAEf,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAG,WAAU;0EAA2C,KAAK,IAAI;;;;;;0EAClE,6LAAC;gEAAE,WAAU;0EAA8B,KAAK,WAAW;;;;;;4DAC1D,KAAK,QAAQ,GAAG,mBACf,6LAAC;gEAAK,WAAU;;oEACb,KAAK,QAAQ;oEAAC;;;;;;;;;;;;;;+CATb;;;;;;;;;;;;;;;;;;;;;;kCAqBpB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BACC,WAAW,CAAC,8FAA8F,EACxG,gBACI,CAAC,iBAAiB,EAAE,aAAa,CAAC,EAAE,SAAS,UAAU,EAAE,WAAW,wCAAwC,CAAC,GAC7G,sGACL,uEAAuE,CAAC;4BACzE,cAAc;gCACZ,IAAI,SAAS,KAAK,SAAS,GAAG;oCAC5B,aAAa;gCACf;4BACF;4BACA,cAAc;gCACZ,IAAI,SAAS,KAAK,SAAS,GAAG;oCAC5B,aAAa;gCACf;4BACF;4BACA,SAAS,IAAM,OAAO,IAAI,CAAC,iCAAiC;;8CAE5D,6LAAC;oCAAK,WAAU;8CAAY;;;;;;8CAC5B,6LAAC;8CAAK;;;;;;gCAGL,SAAS,KAAK,2BACb,6LAAC;oCAAI,WAAU;;;;;;gCAIhB,SAAS,KAAK,2BACb;;sDACE,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ/B;GApSM;KAAA;uCAsSS"}}, {"offset": {"line": 1680, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1686, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/TGRS%20Website/tgrs-website/src/components/CashFlyingAnimation.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useRef } from 'react';\n\ninterface CashBill {\n  x: number;\n  y: number;\n  vx: number;\n  vy: number;\n  rotation: number;\n  rotationSpeed: number;\n  size: number;\n  opacity: number;\n  type: 'dollar' | 'euro' | 'rupee' | 'bitcoin';\n  color: string;\n  life: number;\n  maxLife: number;\n  swayOffset: number;\n  swaySpeed: number;\n}\n\nconst CashFlyingAnimation = () => {\n  const canvasRef = useRef<HTMLCanvasElement>(null);\n  const animationRef = useRef<number | undefined>(undefined);\n  const cashBillsRef = useRef<CashBill[]>([]);\n  const mouseRef = useRef({ x: 0, y: 0 });\n\n  useEffect(() => {\n    const canvas = canvasRef.current;\n    if (!canvas) return;\n\n    const ctx = canvas.getContext('2d');\n    if (!ctx) return;\n\n    // Set canvas size\n    const resizeCanvas = () => {\n      canvas.width = canvas.offsetWidth;\n      canvas.height = canvas.offsetHeight;\n    };\n\n    resizeCanvas();\n    window.addEventListener('resize', resizeCanvas);\n\n    // Mouse tracking for interaction\n    const handleMouseMove = (e: MouseEvent) => {\n      const rect = canvas.getBoundingClientRect();\n      mouseRef.current = {\n        x: e.clientX - rect.left,\n        y: e.clientY - rect.top\n      };\n    };\n\n    canvas.addEventListener('mousemove', handleMouseMove);\n\n    // Cash bill colors (premium theme)\n    const cashColors = [\n      '#10B981', // Emerald (Dollar)\n      '#3B82F6', // Blue (Euro)\n      '#F59E0B', // Amber (Rupee)\n      '#EF4444', // Red (Yuan)\n      '#8B5CF6', // Purple (Pound)\n      '#F97316', // Orange (Bitcoin)\n      '#06B6D4', // Cyan (Yen)\n    ];\n\n    const cashTypes: Array<'dollar' | 'euro' | 'rupee' | 'bitcoin'> = ['dollar', 'euro', 'rupee', 'bitcoin'];\n\n    // Create cash bill\n    const createCashBill = (): CashBill => {\n      const spawnSide = Math.random();\n      let x, y, vx, vy;\n\n      if (spawnSide < 0.3) {\n        // From left\n        x = -50;\n        y = Math.random() * canvas.height;\n        vx = Math.random() * 3 + 1;\n        vy = (Math.random() - 0.5) * 2;\n      } else if (spawnSide < 0.6) {\n        // From top\n        x = Math.random() * canvas.width;\n        y = -50;\n        vx = (Math.random() - 0.5) * 2;\n        vy = Math.random() * 3 + 1;\n      } else {\n        // From right (cash blowing in)\n        x = canvas.width + 50;\n        y = Math.random() * canvas.height;\n        vx = -(Math.random() * 3 + 1);\n        vy = (Math.random() - 0.5) * 2;\n      }\n\n      return {\n        x,\n        y,\n        vx,\n        vy,\n        rotation: Math.random() * Math.PI * 2,\n        rotationSpeed: (Math.random() - 0.5) * 0.05,\n        size: Math.random() * 25 + 20,\n        opacity: Math.random() * 0.9 + 0.4,\n        type: cashTypes[Math.floor(Math.random() * cashTypes.length)],\n        color: cashColors[Math.floor(Math.random() * cashColors.length)],\n        life: 0,\n        maxLife: Math.random() * 400 + 300,\n        swayOffset: Math.random() * Math.PI * 2,\n        swaySpeed: Math.random() * 0.02 + 0.01\n      };\n    };\n\n    // Initialize cash bills\n    const initCashBills = () => {\n      cashBillsRef.current = [];\n      for (let i = 0; i < 50; i++) {\n        cashBillsRef.current.push(createCashBill());\n      }\n    };\n\n    initCashBills();\n\n    // Draw cash bill\n    const drawCashBill = (ctx: CanvasRenderingContext2D, bill: CashBill) => {\n      ctx.save();\n      ctx.translate(bill.x, bill.y);\n      ctx.rotate(bill.rotation);\n      ctx.globalAlpha = bill.opacity * (1 - bill.life / bill.maxLife);\n\n      const width = bill.size * 1.8;\n      const height = bill.size * 0.8;\n\n      // Create gradient for cash bill\n      const gradient = ctx.createLinearGradient(-width/2, -height/2, width/2, height/2);\n      gradient.addColorStop(0, bill.color);\n      gradient.addColorStop(0.5, '#ffffff40');\n      gradient.addColorStop(1, bill.color);\n\n      // Draw cash bill background\n      ctx.fillStyle = gradient;\n      ctx.strokeStyle = bill.color;\n      ctx.lineWidth = 2;\n\n      // Rounded rectangle for bill\n      const radius = 4;\n      ctx.beginPath();\n      ctx.roundRect(-width/2, -height/2, width, height, radius);\n      ctx.fill();\n      ctx.stroke();\n\n      // Draw currency symbol\n      ctx.fillStyle = '#ffffff';\n      ctx.font = `bold ${bill.size * 0.6}px Arial`;\n      ctx.textAlign = 'center';\n      ctx.textBaseline = 'middle';\n\n      let symbol = '$';\n      switch (bill.type) {\n        case 'euro': symbol = '€'; break;\n        case 'rupee': symbol = '₹'; break;\n        case 'bitcoin': symbol = '₿'; break;\n        default: symbol = '$';\n      }\n\n      ctx.fillText(symbol, 0, 0);\n\n      // Add shine effect\n      const shineGradient = ctx.createLinearGradient(-width/2, -height/2, width/2, height/2);\n      shineGradient.addColorStop(0, 'rgba(255,255,255,0)');\n      shineGradient.addColorStop(0.5, 'rgba(255,255,255,0.3)');\n      shineGradient.addColorStop(1, 'rgba(255,255,255,0)');\n\n      ctx.fillStyle = shineGradient;\n      ctx.beginPath();\n      ctx.roundRect(-width/2, -height/2, width, height, radius);\n      ctx.fill();\n\n      ctx.restore();\n    };\n\n    // Animation loop\n    const animate = () => {\n      ctx.clearRect(0, 0, canvas.width, canvas.height);\n\n      const bills = cashBillsRef.current;\n\n      // Update cash bills\n      for (let i = bills.length - 1; i >= 0; i--) {\n        const bill = bills[i];\n\n        // Mouse interaction - bills get attracted to mouse\n        const dx = mouseRef.current.x - bill.x;\n        const dy = mouseRef.current.y - bill.y;\n        const distance = Math.sqrt(dx * dx + dy * dy);\n\n        if (distance < 150) {\n          const force = (150 - distance) / 150;\n          bill.vx += (dx / distance) * force * 0.02;\n          bill.vy += (dy / distance) * force * 0.02;\n        }\n\n        // Add floating/swaying motion\n        bill.swayOffset += bill.swaySpeed;\n        const swayX = Math.sin(bill.swayOffset) * 0.5;\n        const swayY = Math.cos(bill.swayOffset * 0.7) * 0.3;\n\n        // Update position with sway\n        bill.x += bill.vx + swayX;\n        bill.y += bill.vy + swayY;\n        bill.rotation += bill.rotationSpeed;\n        bill.life++;\n\n        // Add some air resistance and gravity\n        bill.vx *= 0.995;\n        bill.vy += 0.01; // Slight gravity\n        bill.vy *= 0.995;\n\n        // Limit velocity\n        const maxVel = 4;\n        const vel = Math.sqrt(bill.vx * bill.vx + bill.vy * bill.vy);\n        if (vel > maxVel) {\n          bill.vx = (bill.vx / vel) * maxVel;\n          bill.vy = (bill.vy / vel) * maxVel;\n        }\n\n        // Remove bills that are off screen or too old\n        if (\n          bill.x > canvas.width + 100 ||\n          bill.y > canvas.height + 100 ||\n          bill.x < -100 ||\n          bill.y < -100 ||\n          bill.life > bill.maxLife\n        ) {\n          bills.splice(i, 1);\n        } else {\n          drawCashBill(ctx, bill);\n        }\n      }\n\n      // Add new cash bills\n      if (bills.length < 80 && Math.random() < 0.6) {\n        bills.push(createCashBill());\n      }\n\n      // Draw connecting lines between nearby bills (money flow effect)\n      ctx.strokeStyle = 'rgba(16, 185, 129, 0.2)';\n      ctx.lineWidth = 1.5;\n      for (let i = 0; i < bills.length; i++) {\n        for (let j = i + 1; j < bills.length; j++) {\n          const dx = bills[i].x - bills[j].x;\n          const dy = bills[i].y - bills[j].y;\n          const distance = Math.sqrt(dx * dx + dy * dy);\n\n          if (distance < 120) {\n            const opacity = (120 - distance) / 120 * 0.25;\n            ctx.globalAlpha = opacity;\n            ctx.beginPath();\n            ctx.moveTo(bills[i].x, bills[i].y);\n            ctx.lineTo(bills[j].x, bills[j].y);\n            ctx.stroke();\n            ctx.globalAlpha = 1;\n          }\n        }\n      }\n\n      animationRef.current = requestAnimationFrame(animate);\n    };\n\n    animate();\n\n    return () => {\n      window.removeEventListener('resize', resizeCanvas);\n      canvas.removeEventListener('mousemove', handleMouseMove);\n      if (animationRef.current) {\n        cancelAnimationFrame(animationRef.current);\n      }\n    };\n  }, []);\n\n  return (\n    <canvas\n      ref={canvasRef}\n      className=\"absolute inset-0 w-full h-full pointer-events-none\"\n      style={{ zIndex: 1 }}\n    />\n  );\n};\n\nexport default CashFlyingAnimation;\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AAqBA,MAAM,sBAAsB;;IAC1B,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAqB;IAC5C,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAsB;IAChD,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAc,EAAE;IAC1C,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;QAAE,GAAG;QAAG,GAAG;IAAE;IAErC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;yCAAE;YACR,MAAM,SAAS,UAAU,OAAO;YAChC,IAAI,CAAC,QAAQ;YAEb,MAAM,MAAM,OAAO,UAAU,CAAC;YAC9B,IAAI,CAAC,KAAK;YAEV,kBAAkB;YAClB,MAAM;8DAAe;oBACnB,OAAO,KAAK,GAAG,OAAO,WAAW;oBACjC,OAAO,MAAM,GAAG,OAAO,YAAY;gBACrC;;YAEA;YACA,OAAO,gBAAgB,CAAC,UAAU;YAElC,iCAAiC;YACjC,MAAM;iEAAkB,CAAC;oBACvB,MAAM,OAAO,OAAO,qBAAqB;oBACzC,SAAS,OAAO,GAAG;wBACjB,GAAG,EAAE,OAAO,GAAG,KAAK,IAAI;wBACxB,GAAG,EAAE,OAAO,GAAG,KAAK,GAAG;oBACzB;gBACF;;YAEA,OAAO,gBAAgB,CAAC,aAAa;YAErC,mCAAmC;YACnC,MAAM,aAAa;gBACjB;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YAED,MAAM,YAA4D;gBAAC;gBAAU;gBAAQ;gBAAS;aAAU;YAExG,mBAAmB;YACnB,MAAM;gEAAiB;oBACrB,MAAM,YAAY,KAAK,MAAM;oBAC7B,IAAI,GAAG,GAAG,IAAI;oBAEd,IAAI,YAAY,KAAK;wBACnB,YAAY;wBACZ,IAAI,CAAC;wBACL,IAAI,KAAK,MAAM,KAAK,OAAO,MAAM;wBACjC,KAAK,KAAK,MAAM,KAAK,IAAI;wBACzB,KAAK,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;oBAC/B,OAAO,IAAI,YAAY,KAAK;wBAC1B,WAAW;wBACX,IAAI,KAAK,MAAM,KAAK,OAAO,KAAK;wBAChC,IAAI,CAAC;wBACL,KAAK,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;wBAC7B,KAAK,KAAK,MAAM,KAAK,IAAI;oBAC3B,OAAO;wBACL,+BAA+B;wBAC/B,IAAI,OAAO,KAAK,GAAG;wBACnB,IAAI,KAAK,MAAM,KAAK,OAAO,MAAM;wBACjC,KAAK,CAAC,CAAC,KAAK,MAAM,KAAK,IAAI,CAAC;wBAC5B,KAAK,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;oBAC/B;oBAEA,OAAO;wBACL;wBACA;wBACA;wBACA;wBACA,UAAU,KAAK,MAAM,KAAK,KAAK,EAAE,GAAG;wBACpC,eAAe,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;wBACvC,MAAM,KAAK,MAAM,KAAK,KAAK;wBAC3B,SAAS,KAAK,MAAM,KAAK,MAAM;wBAC/B,MAAM,SAAS,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,UAAU,MAAM,EAAE;wBAC7D,OAAO,UAAU,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,WAAW,MAAM,EAAE;wBAChE,MAAM;wBACN,SAAS,KAAK,MAAM,KAAK,MAAM;wBAC/B,YAAY,KAAK,MAAM,KAAK,KAAK,EAAE,GAAG;wBACtC,WAAW,KAAK,MAAM,KAAK,OAAO;oBACpC;gBACF;;YAEA,wBAAwB;YACxB,MAAM;+DAAgB;oBACpB,aAAa,OAAO,GAAG,EAAE;oBACzB,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,IAAK;wBAC3B,aAAa,OAAO,CAAC,IAAI,CAAC;oBAC5B;gBACF;;YAEA;YAEA,iBAAiB;YACjB,MAAM;8DAAe,CAAC,KAA+B;oBACnD,IAAI,IAAI;oBACR,IAAI,SAAS,CAAC,KAAK,CAAC,EAAE,KAAK,CAAC;oBAC5B,IAAI,MAAM,CAAC,KAAK,QAAQ;oBACxB,IAAI,WAAW,GAAG,KAAK,OAAO,GAAG,CAAC,IAAI,KAAK,IAAI,GAAG,KAAK,OAAO;oBAE9D,MAAM,QAAQ,KAAK,IAAI,GAAG;oBAC1B,MAAM,SAAS,KAAK,IAAI,GAAG;oBAE3B,gCAAgC;oBAChC,MAAM,WAAW,IAAI,oBAAoB,CAAC,CAAC,QAAM,GAAG,CAAC,SAAO,GAAG,QAAM,GAAG,SAAO;oBAC/E,SAAS,YAAY,CAAC,GAAG,KAAK,KAAK;oBACnC,SAAS,YAAY,CAAC,KAAK;oBAC3B,SAAS,YAAY,CAAC,GAAG,KAAK,KAAK;oBAEnC,4BAA4B;oBAC5B,IAAI,SAAS,GAAG;oBAChB,IAAI,WAAW,GAAG,KAAK,KAAK;oBAC5B,IAAI,SAAS,GAAG;oBAEhB,6BAA6B;oBAC7B,MAAM,SAAS;oBACf,IAAI,SAAS;oBACb,IAAI,SAAS,CAAC,CAAC,QAAM,GAAG,CAAC,SAAO,GAAG,OAAO,QAAQ;oBAClD,IAAI,IAAI;oBACR,IAAI,MAAM;oBAEV,uBAAuB;oBACvB,IAAI,SAAS,GAAG;oBAChB,IAAI,IAAI,GAAG,CAAC,KAAK,EAAE,KAAK,IAAI,GAAG,IAAI,QAAQ,CAAC;oBAC5C,IAAI,SAAS,GAAG;oBAChB,IAAI,YAAY,GAAG;oBAEnB,IAAI,SAAS;oBACb,OAAQ,KAAK,IAAI;wBACf,KAAK;4BAAQ,SAAS;4BAAK;wBAC3B,KAAK;4BAAS,SAAS;4BAAK;wBAC5B,KAAK;4BAAW,SAAS;4BAAK;wBAC9B;4BAAS,SAAS;oBACpB;oBAEA,IAAI,QAAQ,CAAC,QAAQ,GAAG;oBAExB,mBAAmB;oBACnB,MAAM,gBAAgB,IAAI,oBAAoB,CAAC,CAAC,QAAM,GAAG,CAAC,SAAO,GAAG,QAAM,GAAG,SAAO;oBACpF,cAAc,YAAY,CAAC,GAAG;oBAC9B,cAAc,YAAY,CAAC,KAAK;oBAChC,cAAc,YAAY,CAAC,GAAG;oBAE9B,IAAI,SAAS,GAAG;oBAChB,IAAI,SAAS;oBACb,IAAI,SAAS,CAAC,CAAC,QAAM,GAAG,CAAC,SAAO,GAAG,OAAO,QAAQ;oBAClD,IAAI,IAAI;oBAER,IAAI,OAAO;gBACb;;YAEA,iBAAiB;YACjB,MAAM;yDAAU;oBACd,IAAI,SAAS,CAAC,GAAG,GAAG,OAAO,KAAK,EAAE,OAAO,MAAM;oBAE/C,MAAM,QAAQ,aAAa,OAAO;oBAElC,oBAAoB;oBACpB,IAAK,IAAI,IAAI,MAAM,MAAM,GAAG,GAAG,KAAK,GAAG,IAAK;wBAC1C,MAAM,OAAO,KAAK,CAAC,EAAE;wBAErB,mDAAmD;wBACnD,MAAM,KAAK,SAAS,OAAO,CAAC,CAAC,GAAG,KAAK,CAAC;wBACtC,MAAM,KAAK,SAAS,OAAO,CAAC,CAAC,GAAG,KAAK,CAAC;wBACtC,MAAM,WAAW,KAAK,IAAI,CAAC,KAAK,KAAK,KAAK;wBAE1C,IAAI,WAAW,KAAK;4BAClB,MAAM,QAAQ,CAAC,MAAM,QAAQ,IAAI;4BACjC,KAAK,EAAE,IAAI,AAAC,KAAK,WAAY,QAAQ;4BACrC,KAAK,EAAE,IAAI,AAAC,KAAK,WAAY,QAAQ;wBACvC;wBAEA,8BAA8B;wBAC9B,KAAK,UAAU,IAAI,KAAK,SAAS;wBACjC,MAAM,QAAQ,KAAK,GAAG,CAAC,KAAK,UAAU,IAAI;wBAC1C,MAAM,QAAQ,KAAK,GAAG,CAAC,KAAK,UAAU,GAAG,OAAO;wBAEhD,4BAA4B;wBAC5B,KAAK,CAAC,IAAI,KAAK,EAAE,GAAG;wBACpB,KAAK,CAAC,IAAI,KAAK,EAAE,GAAG;wBACpB,KAAK,QAAQ,IAAI,KAAK,aAAa;wBACnC,KAAK,IAAI;wBAET,sCAAsC;wBACtC,KAAK,EAAE,IAAI;wBACX,KAAK,EAAE,IAAI,MAAM,iBAAiB;wBAClC,KAAK,EAAE,IAAI;wBAEX,iBAAiB;wBACjB,MAAM,SAAS;wBACf,MAAM,MAAM,KAAK,IAAI,CAAC,KAAK,EAAE,GAAG,KAAK,EAAE,GAAG,KAAK,EAAE,GAAG,KAAK,EAAE;wBAC3D,IAAI,MAAM,QAAQ;4BAChB,KAAK,EAAE,GAAG,AAAC,KAAK,EAAE,GAAG,MAAO;4BAC5B,KAAK,EAAE,GAAG,AAAC,KAAK,EAAE,GAAG,MAAO;wBAC9B;wBAEA,8CAA8C;wBAC9C,IACE,KAAK,CAAC,GAAG,OAAO,KAAK,GAAG,OACxB,KAAK,CAAC,GAAG,OAAO,MAAM,GAAG,OACzB,KAAK,CAAC,GAAG,CAAC,OACV,KAAK,CAAC,GAAG,CAAC,OACV,KAAK,IAAI,GAAG,KAAK,OAAO,EACxB;4BACA,MAAM,MAAM,CAAC,GAAG;wBAClB,OAAO;4BACL,aAAa,KAAK;wBACpB;oBACF;oBAEA,qBAAqB;oBACrB,IAAI,MAAM,MAAM,GAAG,MAAM,KAAK,MAAM,KAAK,KAAK;wBAC5C,MAAM,IAAI,CAAC;oBACb;oBAEA,iEAAiE;oBACjE,IAAI,WAAW,GAAG;oBAClB,IAAI,SAAS,GAAG;oBAChB,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;wBACrC,IAAK,IAAI,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;4BACzC,MAAM,KAAK,KAAK,CAAC,EAAE,CAAC,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC;4BAClC,MAAM,KAAK,KAAK,CAAC,EAAE,CAAC,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC;4BAClC,MAAM,WAAW,KAAK,IAAI,CAAC,KAAK,KAAK,KAAK;4BAE1C,IAAI,WAAW,KAAK;gCAClB,MAAM,UAAU,CAAC,MAAM,QAAQ,IAAI,MAAM;gCACzC,IAAI,WAAW,GAAG;gCAClB,IAAI,SAAS;gCACb,IAAI,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,CAAC,CAAC;gCACjC,IAAI,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,CAAC,CAAC;gCACjC,IAAI,MAAM;gCACV,IAAI,WAAW,GAAG;4BACpB;wBACF;oBACF;oBAEA,aAAa,OAAO,GAAG,sBAAsB;gBAC/C;;YAEA;YAEA;iDAAO;oBACL,OAAO,mBAAmB,CAAC,UAAU;oBACrC,OAAO,mBAAmB,CAAC,aAAa;oBACxC,IAAI,aAAa,OAAO,EAAE;wBACxB,qBAAqB,aAAa,OAAO;oBAC3C;gBACF;;QACF;wCAAG,EAAE;IAEL,qBACE,6LAAC;QACC,KAAK;QACL,WAAU;QACV,OAAO;YAAE,QAAQ;QAAE;;;;;;AAGzB;GAvQM;KAAA;uCAyQS"}}, {"offset": {"line": 1956, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}