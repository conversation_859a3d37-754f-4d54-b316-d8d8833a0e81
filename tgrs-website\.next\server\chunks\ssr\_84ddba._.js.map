{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/TGRS%20Website/tgrs-website/src/components/Navbar.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/Navbar.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Navbar.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA6R,GAC1T,2DACA"}}, {"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 21, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/TGRS%20Website/tgrs-website/src/components/Navbar.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/Navbar.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Navbar.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAyQ,GACtS,uCACA"}}, {"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 35, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": ""}}, {"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 45, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/TGRS%20Website/tgrs-website/src/components/Footer.tsx"], "sourcesContent": ["import Link from \"next/link\";\nimport Image from \"next/image\";\n\nconst Footer = () => {\n  return (\n    <footer className=\"bg-black/50 pattern-dots-footer border-t border-neon-orange/30 pt-12 pb-6 relative\">\n      <div className=\"container mx-auto px-4 md:px-6\">\n        <div className=\"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-6 sm:gap-8 mb-6 sm:mb-8\">\n          {/* Logo and About */}\n          <div className=\"col-span-1 sm:col-span-2 md:col-span-1\">\n            <Link href=\"/\" className=\"flex items-center mb-4\">\n              <div className=\"relative h-8 w-8 sm:h-10 sm:w-10 mr-3\">\n                <Image\n                  src=\"/assets/tgrs-logo.png\"\n                  alt=\"TGRS Logo\"\n                  width={40}\n                  height={40}\n                  className=\"w-full h-full object-contain\"\n                />\n              </div>\n              <span className=\"text-white font-display font-bold text-lg sm:text-xl tracking-wider\">\n                TGRS\n              </span>\n            </Link>\n            <p className=\"text-gray-400 text-xs sm:text-sm mb-4\">\n              The premier Telugu community FiveM roleplay server with immersive experiences and unique features.\n            </p>\n            <div className=\"flex space-x-3 sm:space-x-4\">\n              {/* YouTube */}\n              <a href=\"https://www.youtube.com/@GTA5RPTGRSCITY\" target=\"_blank\" rel=\"noopener noreferrer\" className=\"text-white hover:text-red-500 transition-colors\" aria-label=\"YouTube\">\n                <svg className=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\" aria-hidden=\"true\">\n                  <path d=\"M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z\"/>\n                </svg>\n              </a>\n              {/* Discord */}\n              <a href=\"https://discord.gg/GAMravHDnB\" target=\"_blank\" rel=\"noopener noreferrer\" className=\"text-white hover:text-[#5865F2] transition-colors\" aria-label=\"Discord\">\n                <svg className=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\" aria-hidden=\"true\">\n                  <path d=\"M20.317 4.3698a19.7913 19.7913 0 00-4.8851-1.5152.0741.0741 0 00-.0785.0371c-.211.3753-.4447.8648-.6083 1.2495-1.8447-.2762-3.68-.2762-5.4868 0-.1636-.3933-.4058-.8742-.6177-1.2495a.077.077 0 00-.0785-.037 19.7363 19.7363 0 00-4.8852 1.515.0699.0699 0 00-.0321.0277C.5334 9.0458-.319 13.5799.0992 18.0578a.0824.0824 0 00.0312.0561c2.0528 1.5076 4.0413 2.4228 5.9929 3.0294a.0777.0777 0 00.0842-.0276c.4616-.6304.8731-1.2952 1.226-1.9942a.076.076 0 00-.0416-.1057c-.6528-.2476-1.2743-.5495-1.8722-.8923a.077.077 0 01-.0076-.1277c.1258-.0943.2517-.1923.3718-.2914a.0743.0743 0 01.0776-.0105c3.9278 1.7933 8.18 1.7933 12.0614 0a.0739.0739 0 01.0785.0095c.1202.099.246.1981.3728.2924a.077.077 0 01-.0066.1276 12.2986 12.2986 0 01-1.873.8914.0766.0766 0 00-.0407.1067c.3604.698.7719 1.3628 1.225 1.9932a.076.076 0 00.0842.0286c1.961-.6067 3.9495-1.5219 6.0023-3.0294a.077.077 0 00.0313-.0552c.5004-5.177-.8382-9.6739-3.5485-13.6604a.061.061 0 00-.0312-.0286zM8.02 15.3312c-1.1825 0-2.1569-1.0857-2.1569-2.419 0-1.3332.9555-2.4189 2.157-2.4189 1.2108 0 2.1757 1.0952 2.1568 2.419-.0002 1.3332-.9555 2.4189-2.1569 2.4189zm7.9748 0c-1.1825 0-2.1569-1.0857-2.1569-2.419 0-1.3332.9554-2.4189 2.1569-2.4189 1.2108 0 2.1757 1.0952 2.1568 2.419 0 1.3332-.9554 2.4189-2.1568 2.4189Z\"/>\n                </svg>\n              </a>\n              {/* Website */}\n              <a href=\"/\" className=\"text-white hover:text-neon-orange transition-colors\" aria-label=\"Website\">\n                <svg className=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\" aria-hidden=\"true\">\n                  <path d=\"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-1 17.93c-3.94-.49-7-3.85-7-7.93 0-.62.08-1.21.21-1.79L9 15v1c0 1.1.9 2 2 2v1.93zm6.9-2.54c-.26-.81-1-1.39-1.9-1.39h-1v-3c0-.55-.45-1-1-1H8v-2h2c.55 0 1-.45 1-1V7h2c1.1 0 2-.9 2-2v-.41c2.93 1.19 5 4.06 5 7.41 0 2.08-.8 3.97-2.1 5.39z\"/>\n                </svg>\n              </a>\n\n            </div>\n          </div>\n\n          {/* Quick Links */}\n          <div className=\"col-span-1\">\n            <h3 className=\"text-white font-display font-semibold text-base sm:text-lg mb-3 sm:mb-4\">Quick Links</h3>\n            <ul className=\"space-y-1 sm:space-y-2\">\n              <li>\n                <Link href=\"/\" className=\"text-gray-400 hover:text-neon-orange transition-colors text-xs sm:text-sm\">\n                  Home\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/about\" className=\"text-gray-400 hover:text-neon-orange transition-colors text-xs sm:text-sm\">\n                  About\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/features\" className=\"text-gray-400 hover:text-neon-orange transition-colors text-xs sm:text-sm\">\n                  Features\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/rules\" className=\"text-gray-400 hover:text-neon-orange transition-colors text-xs sm:text-sm\">\n                  Rules\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/contact\" className=\"text-gray-400 hover:text-neon-orange transition-colors text-xs sm:text-sm\">\n                  Contact\n                </Link>\n              </li>\n            </ul>\n          </div>\n\n          {/* Server Info */}\n          <div className=\"col-span-1\">\n            <h3 className=\"text-white font-display font-semibold text-base sm:text-lg mb-3 sm:mb-4\">Server Info</h3>\n            <ul className=\"space-y-1 sm:space-y-2\">\n              <li className=\"text-gray-400 text-xs sm:text-sm\">\n                <span className=\"text-neon-orange mr-2\">•</span> 24/7 Uptime\n              </li>\n              <li className=\"text-gray-400 text-xs sm:text-sm\">\n                <span className=\"text-neon-orange mr-2\">•</span> Active Staff\n              </li>\n              <li className=\"text-gray-400 text-xs sm:text-sm\">\n                <span className=\"text-neon-orange mr-2\">•</span> Custom Scripts\n              </li>\n              <li className=\"text-gray-400 text-xs sm:text-sm\">\n                <span className=\"text-neon-orange mr-2\">•</span> Regular Updates\n              </li>\n              <li className=\"text-gray-400 text-xs sm:text-sm\">\n                <span className=\"text-neon-orange mr-2\">•</span> Community Events\n              </li>\n            </ul>\n          </div>\n\n          {/* Discord */}\n          <div className=\"col-span-1 sm:col-span-2 md:col-span-1\">\n            <h3 className=\"text-white font-display font-semibold text-base sm:text-lg mb-3 sm:mb-4\">Join Our Discord</h3>\n            <p className=\"text-gray-400 text-xs sm:text-sm mb-3 sm:mb-4\">\n              Join our Discord community for server updates, events, and to connect with other players.\n            </p>\n            <a\n              href=\"https://discord.gg/GAMravHDnB\"\n              target=\"_blank\"\n              rel=\"noopener noreferrer\"\n              className=\"px-3 py-2 sm:px-4 sm:py-2 rounded-md bg-[#5865F2] hover:bg-[#4752C4] text-white font-medium transition-colors flex items-center justify-center space-x-2 w-full sm:w-auto text-xs sm:text-sm\"\n            >\n              <svg className=\"w-4 h-4 sm:w-5 sm:h-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path d=\"M20.317 4.3698a19.7913 19.7913 0 00-4.8851-1.5152.0741.0741 0 00-.0785.0371c-.211.3753-.4447.8648-.6083 1.2495-1.8447-.2762-3.68-.2762-5.4868 0-.1636-.3933-.4058-.8742-.6177-1.2495a.077.077 0 00-.0785-.037 19.7363 19.7363 0 00-4.8852 1.515.0699.0699 0 00-.0321.0277C.5334 9.0458-.319 13.5799.0992 18.0578a.0824.0824 0 00.0312.0561c2.0528 1.5076 4.0413 2.4228 5.9929 3.0294a.0777.0777 0 00.0842-.0276c.4616-.6304.8731-1.2952 1.226-1.9942a.076.076 0 00-.0416-.1057c-.6528-.2476-1.2743-.5495-1.8722-.8923a.077.077 0 01-.0076-.1277c.1258-.0943.2517-.1923.3718-.2914a.0743.0743 0 01.0776-.0105c3.9278 1.7933 8.18 1.7933 12.0614 0a.0739.0739 0 01.0785.0095c.1202.099.246.1981.3728.2924a.077.077 0 01-.0066.1276 12.2986 12.2986 0 01-1.873.8914.0766.0766 0 00-.0407.1067c.3604.698.7719 1.3628 1.225 1.9932a.076.076 0 00.0842.0286c1.961-.6067 3.9495-1.5219 6.0023-3.0294a.077.077 0 00.0313-.0552c.5004-5.177-.8382-9.6739-3.5485-13.6604a.061.061 0 00-.0312-.0286zM8.02 15.3312c-1.1825 0-2.1569-1.0857-2.1569-2.419 0-1.3332.9555-2.4189 2.157-2.4189 1.2108 0 2.1757 1.0952 2.1568 2.419 0 1.3332-.9555 2.4189-2.1569 2.4189zm7.9748 0c-1.1825 0-2.1569-1.0857-2.1569-2.419 0-1.3332.9554-2.4189 2.1569-2.4189 1.2108 0 2.1757 1.0952 2.1568 2.419 0 1.3332-.946 2.4189-2.1568 2.4189Z\"></path>\n              </svg>\n              <span>Join Discord</span>\n            </a>\n          </div>\n        </div>\n\n        {/* Copyright */}\n        <div className=\"border-t border-gray-800 pt-4 sm:pt-6 mt-4 sm:mt-6 text-center\">\n          <p className=\"text-gray-500 text-xs sm:text-sm\">\n            &copy; {new Date().getFullYear()} TGRS - Telugu Gaming Roleplay Server. All rights reserved.\n          </p>\n        </div>\n      </div>\n    </footer>\n  );\n};\n\nexport default Footer;\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAEA,MAAM,SAAS;IACb,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAI,WAAU;;sDACvB,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;gDACJ,KAAI;gDACJ,KAAI;gDACJ,OAAO;gDACP,QAAQ;gDACR,WAAU;;;;;;;;;;;sDAGd,8OAAC;4CAAK,WAAU;sDAAsE;;;;;;;;;;;;8CAIxF,8OAAC;oCAAE,WAAU;8CAAwC;;;;;;8CAGrD,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC;4CAAE,MAAK;4CAA0C,QAAO;4CAAS,KAAI;4CAAsB,WAAU;4CAAkD,cAAW;sDACjK,cAAA,8OAAC;gDAAI,WAAU;gDAAU,MAAK;gDAAe,SAAQ;gDAAY,eAAY;0DAC3E,cAAA,8OAAC;oDAAK,GAAE;;;;;;;;;;;;;;;;sDAIZ,8OAAC;4CAAE,MAAK;4CAAgC,QAAO;4CAAS,KAAI;4CAAsB,WAAU;4CAAoD,cAAW;sDACzJ,cAAA,8OAAC;gDAAI,WAAU;gDAAU,MAAK;gDAAe,SAAQ;gDAAY,eAAY;0DAC3E,cAAA,8OAAC;oDAAK,GAAE;;;;;;;;;;;;;;;;sDAIZ,8OAAC;4CAAE,MAAK;4CAAI,WAAU;4CAAsD,cAAW;sDACrF,cAAA,8OAAC;gDAAI,WAAU;gDAAU,MAAK;gDAAe,SAAQ;gDAAY,eAAY;0DAC3E,cAAA,8OAAC;oDAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQhB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA0E;;;;;;8CACxF,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAI,WAAU;0DAA4E;;;;;;;;;;;sDAIvG,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAS,WAAU;0DAA4E;;;;;;;;;;;sDAI5G,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAY,WAAU;0DAA4E;;;;;;;;;;;sDAI/G,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAS,WAAU;0DAA4E;;;;;;;;;;;sDAI5G,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;0DAA4E;;;;;;;;;;;;;;;;;;;;;;;sCAQlH,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA0E;;;;;;8CACxF,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAK,WAAU;8DAAwB;;;;;;gDAAQ;;;;;;;sDAElD,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAK,WAAU;8DAAwB;;;;;;gDAAQ;;;;;;;sDAElD,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAK,WAAU;8DAAwB;;;;;;gDAAQ;;;;;;;sDAElD,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAK,WAAU;8DAAwB;;;;;;gDAAQ;;;;;;;sDAElD,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAK,WAAU;8DAAwB;;;;;;gDAAQ;;;;;;;;;;;;;;;;;;;sCAMtD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA0E;;;;;;8CACxF,8OAAC;oCAAE,WAAU;8CAAgD;;;;;;8CAG7D,8OAAC;oCACC,MAAK;oCACL,QAAO;oCACP,KAAI;oCACJ,WAAU;;sDAEV,8OAAC;4CAAI,WAAU;4CAAwB,MAAK;4CAAe,SAAQ;sDACjE,cAAA,8OAAC;gDAAK,GAAE;;;;;;;;;;;sDAEV,8OAAC;sDAAK;;;;;;;;;;;;;;;;;;;;;;;;8BAMZ,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAE,WAAU;;4BAAmC;4BACtC,IAAI,OAAO,WAAW;4BAAG;;;;;;;;;;;;;;;;;;;;;;;AAM7C;uCAEe"}}, {"offset": {"line": 522, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 528, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/TGRS%20Website/tgrs-website/src/components/Hero.tsx"], "sourcesContent": ["import React from \"react\";\nimport Button from \"./Button\";\n\ninterface HeroProps {\n  title: string;\n  subtitle: string;\n  primaryButtonText?: string;\n  primaryButtonHref?: string;\n  secondaryButtonText?: string;\n  secondaryButtonHref?: string;\n  backgroundImage?: string;\n}\n\nconst Hero = ({\n  title,\n  subtitle,\n  primaryButtonText,\n  primaryButtonHref,\n  secondaryButtonText,\n  secondaryButtonHref,\n  backgroundImage = '/assets/image-3.jpg',\n}: HeroProps) => {\n  return (\n    <div className=\"relative min-h-screen flex items-center justify-center overflow-hidden\">\n      {/* Background Image */}\n      <div\n        className=\"absolute inset-0 z-0 bg-cover bg-center bg-no-repeat\"\n        style={{\n          backgroundImage: `url('${backgroundImage}')`\n        }}\n      ></div>\n\n      {/* Background overlays */}\n      <div className=\"absolute inset-0 z-10\">\n        <div className=\"absolute inset-0 bg-black opacity-60\"></div>\n        <div className=\"absolute top-0 left-0 w-full h-full bg-gradient-to-b from-black/20 via-transparent to-black/80\"></div>\n\n        {/* Subtle animated grid lines (reduced opacity) */}\n        <div className=\"absolute inset-0 flex flex-col opacity-5\">\n          {Array.from({ length: 15 }).map((_, i) => (\n            <div key={`h-${i}`} className=\"h-px bg-neon-orange w-full animate-pulse-slow\" style={{ marginTop: `${i * 7}vh`, animationDelay: `${i * 0.2}s` }}></div>\n          ))}\n        </div>\n        <div className=\"absolute inset-0 flex flex-row opacity-5\">\n          {Array.from({ length: 15 }).map((_, i) => (\n            <div key={`v-${i}`} className=\"w-px bg-neon-orange h-full animate-pulse-slow\" style={{ marginLeft: `${i * 7}vw`, animationDelay: `${i * 0.2}s` }}></div>\n          ))}\n        </div>\n\n        {/* Subtle glowing orbs (reduced and repositioned) */}\n        <div className=\"absolute top-1/4 left-1/4 w-96 h-96 rounded-full bg-neon-orange/10 blur-3xl animate-float\"></div>\n        <div className=\"absolute bottom-1/4 right-1/4 w-64 h-64 rounded-full bg-neon-red/10 blur-3xl animate-float\" style={{ animationDelay: '3s' }}></div>\n      </div>\n\n      {/* Content */}\n      <div className=\"container mx-auto px-4 md:px-6 relative z-20 text-center flex flex-col justify-center min-h-[75vh] pt-20 md:pt-36\">\n        <div className=\"space-y-4 md:space-y-6\">\n          <h1 className=\"text-3xl sm:text-4xl md:text-6xl lg:text-7xl font-display font-bold tracking-wider text-white leading-tight\">\n            <span className=\"inline-block\">{title}</span>\n          </h1>\n          <p className=\"text-lg sm:text-xl md:text-2xl text-gray-300 max-w-3xl mx-auto leading-relaxed px-2\">\n            {subtitle}\n          </p>\n        </div>\n      </div>\n\n      {/* Scroll indicator */}\n      <div className=\"absolute bottom-6 md:bottom-10 left-1/2 transform -translate-x-1/2 flex flex-col items-center z-20\">\n        {/* Mobile: Swipe indicator */}\n        <div className=\"block md:hidden\">\n          <span className=\"text-white text-xs mb-2\">Swipe Up</span>\n          <div className=\"flex items-center justify-center\">\n            <div className=\"w-8 h-8 flex items-center justify-center\">\n              <svg className=\"w-6 h-6 text-white animate-bounce\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                {/* Finger */}\n                <path d=\"M12 2c-1.1 0-2 .9-2 2v8c0 .55-.45 1-1 1s-1-.45-1-1V8c0-1.1-.9-2-2-2s-2 .9-2 2v4c0 .55-.45 1-1 1s-1-.45-1-1V10c0-1.1-.9-2-2-2s-2 .9-2 2v8c0 3.31 2.69 6 6 6h6c3.31 0 6-2.69 6-6V4c0-1.1-.9-2-2-2s-2 .9-2 2v8c0 .55-.45 1-1 1s-1-.45-1-1V4c0-1.1-.9-2-2-2z\"/>\n                {/* Upward arrow */}\n                <path d=\"M12 8l-3 3h2v4h2v-4h2l-3-3z\" fill=\"white\" opacity=\"0.8\"/>\n              </svg>\n            </div>\n          </div>\n        </div>\n\n        {/* Desktop: Mouse scroll indicator */}\n        <div className=\"hidden md:flex md:flex-col md:items-center\">\n          <span className=\"text-white text-sm mb-2\">Scroll Down</span>\n          <div className=\"w-6 h-10 border-2 border-white rounded-full flex justify-center items-start pt-2\">\n            <div className=\"w-1 h-2 bg-white rounded-full animate-bounce\"></div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Hero;\n"], "names": [], "mappings": ";;;;;AAaA,MAAM,OAAO,CAAC,EACZ,KAAK,EACL,QAAQ,EACR,iBAAiB,EACjB,iBAAiB,EACjB,mBAAmB,EACnB,mBAAmB,EACnB,kBAAkB,qBAAqB,EAC7B;IACV,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBACC,WAAU;gBACV,OAAO;oBACL,iBAAiB,CAAC,KAAK,EAAE,gBAAgB,EAAE,CAAC;gBAC9C;;;;;;0BAIF,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;kCAGf,8OAAC;wBAAI,WAAU;kCACZ,MAAM,IAAI,CAAC;4BAAE,QAAQ;wBAAG,GAAG,GAAG,CAAC,CAAC,GAAG,kBAClC,8OAAC;gCAAmB,WAAU;gCAAgD,OAAO;oCAAE,WAAW,GAAG,IAAI,EAAE,EAAE,CAAC;oCAAE,gBAAgB,GAAG,IAAI,IAAI,CAAC,CAAC;gCAAC;+BAApI,CAAC,EAAE,EAAE,GAAG;;;;;;;;;;kCAGtB,8OAAC;wBAAI,WAAU;kCACZ,MAAM,IAAI,CAAC;4BAAE,QAAQ;wBAAG,GAAG,GAAG,CAAC,CAAC,GAAG,kBAClC,8OAAC;gCAAmB,WAAU;gCAAgD,OAAO;oCAAE,YAAY,GAAG,IAAI,EAAE,EAAE,CAAC;oCAAE,gBAAgB,GAAG,IAAI,IAAI,CAAC,CAAC;gCAAC;+BAArI,CAAC,EAAE,EAAE,GAAG;;;;;;;;;;kCAKtB,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;wBAA6F,OAAO;4BAAE,gBAAgB;wBAAK;;;;;;;;;;;;0BAI5I,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCACZ,cAAA,8OAAC;gCAAK,WAAU;0CAAgB;;;;;;;;;;;sCAElC,8OAAC;4BAAE,WAAU;sCACV;;;;;;;;;;;;;;;;;0BAMP,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAK,WAAU;0CAA0B;;;;;;0CAC1C,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;wCAAoC,MAAK;wCAAe,SAAQ;;0DAE7E,8OAAC;gDAAK,GAAE;;;;;;0DAER,8OAAC;gDAAK,GAAE;gDAA8B,MAAK;gDAAQ,SAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAOnE,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAK,WAAU;0CAA0B;;;;;;0CAC1C,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM3B;uCAEe"}}, {"offset": {"line": 772, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 778, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/TGRS%20Website/tgrs-website/src/components/VIPCardNew.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/VIPCardNew.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/VIPCardNew.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAiS,GAC9T,+DACA"}}, {"offset": {"line": 786, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 792, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/TGRS%20Website/tgrs-website/src/components/VIPCardNew.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/VIPCardNew.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/VIPCardNew.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA6Q,GAC1S,2CACA"}}, {"offset": {"line": 800, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 806, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": ""}}, {"offset": {"line": 810, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 816, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/TGRS%20Website/tgrs-website/src/components/CashFlyingAnimation.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/CashFlyingAnimation.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/CashFlyingAnimation.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA0S,GACvU,wEACA"}}, {"offset": {"line": 824, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 830, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/TGRS%20Website/tgrs-website/src/components/CashFlyingAnimation.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/CashFlyingAnimation.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/CashFlyingAnimation.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAsR,GACnT,oDACA"}}, {"offset": {"line": 838, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 844, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": ""}}, {"offset": {"line": 848, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 854, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/TGRS%20Website/tgrs-website/src/app/vip/page.tsx"], "sourcesContent": ["import Navbar from \"@/components/Navbar\";\nimport Footer from \"@/components/Footer\";\nimport Hero from \"@/components/Hero\";\nimport VIPCard from \"@/components/VIPCardNew\";\nimport CashFlyingAnimation from \"@/components/CashFlyingAnimation\";\nimport Button from \"@/components/Button\";\n\nexport default function VIPPage() {\n  const vipTiers = [\n    {\n      tier: 1,\n      price: 299,\n      title: \"VIP 1\",\n      description: \"Start your premium journey\",\n      icon: \"fas fa-medal\",\n      mainFeatures: [\n        {\n          text: \"Custom Phone Number\",\n          details: \"with 9 digits\",\n          highlight: \"(3 number sequence max)\",\n          example: \"111888222\",\n        },\n        {\n          text: \"Custom Car\",\n          details: \"from TGRS category\",\n          highlight: \"(< ₹10,00,000)\",\n        },\n        {\n          text: \"In-game Cash\",\n          details: \"₹20,000\",\n        },\n        {\n          text: \"Lucky Wheel Coins\",\n          details: \"500\",\n        },\n      ],\n      timeFeatures: [\n        { text: \"VIP Role\", duration: \"1 month\" },\n        { text: \"Priority Queue\", duration: \"1 month\" },\n      ],\n      clothing: [\n        {\n          name: \"Yeezes\",\n          variants: 5,\n          description: \"Premium footwear with unique design\",\n        },\n      ],\n      peds: [],\n      pets: [],\n      tiltDirection: \"left\" as const,\n      pattern: \"circuit\" as const,\n    },\n    {\n      tier: 2,\n      price: 699,\n      title: \"VIP 2\",\n      description: \"Enhanced premium experience\",\n      icon: \"fas fa-award\",\n      mainFeatures: [\n        {\n          text: \"Custom Phone Number\",\n          details: \"with 8 digits\",\n          highlight: \"(4 number sequence max)\",\n          example: \"44443333\",\n        },\n        {\n          text: \"Custom Car\",\n          details: \"from TGRS category\",\n          highlight: \"(< ₹18,00,000)\",\n        },\n        {\n          text: \"Custom Bike\",\n          details: \"from ANY category\",\n          highlight: \"(< ₹1,00,000)\",\n        },\n        {\n          text: \"Custom Numberplate\",\n          details: \"\",\n          highlight: \"(4 alphabets 4 numbers)\",\n          example: \"TGRS1234\",\n        },\n        {\n          text: \"In-game Cash\",\n          details: \"₹30,000\",\n        },\n        {\n          text: \"Lucky Wheel Coins\",\n          details: \"750\",\n        },\n      ],\n      timeFeatures: [\n        { text: \"VIP Role\", duration: \"1 month\" },\n        { text: \"Priority Queue\", duration: \"1 month\" },\n        { text: \"Rental Apartment\", duration: \"1 month\" },\n      ],\n      clothing: [\n        {\n          name: \"TGG Pant\",\n          variants: 4,\n          description: \"Exclusive pants with TGRS branding\",\n        },\n      ],\n      peds: [],\n      pets: [],\n      tiltDirection: \"right\" as const,\n      pattern: \"hexagon\" as const,\n    },\n    {\n      tier: 3,\n      price: 999,\n      title: \"VIP 3\",\n      description: \"Premium experience with exclusive content\",\n      icon: \"fas fa-trophy\",\n      isPopular: false,\n      mainFeatures: [\n        {\n          text: \"Custom Phone Number\",\n          details: \"with 6 digits\",\n          highlight: \"(5 number sequence max)\",\n          example: \"444441\",\n        },\n        {\n          text: \"Custom Car\",\n          details: \"from TGRS category\",\n          highlight: \"(< ₹25,00,000)\",\n        },\n        {\n          text: \"Custom Bike\",\n          details: \"from ANY category\",\n          highlight: \"(< ₹2,50,000)\",\n        },\n        {\n          text: \"Custom Numberplate x2\",\n          details: \"\",\n          highlight: \"(3 alphabets 3 numbers)\",\n          example: \"TGR123\",\n        },\n        {\n          text: \"In-game Cash\",\n          details: \"₹50,000\",\n        },\n        {\n          text: \"Lucky Wheel Coins\",\n          details: \"1250\",\n        },\n        {\n          text: \"Tax Free\",\n          details: \"No Taxes\",\n        },\n        {\n          text: \"Extra Character Slot\",\n          details: \"+1 slot\",\n        },\n      ],\n      timeFeatures: [\n        { text: \"VIP Role\", duration: \"1 month\" },\n        { text: \"Priority Queue\", duration: \"1 month\" },\n        { text: \"Tier 1 House\", duration: \"1 month\" },\n      ],\n      clothing: [\n        {\n          name: \"TGYeezes\",\n          variants: 5,\n          description: \"Premium TGRS edition footwear\",\n        },\n      ],\n      peds: [\n        { name: \"Brenda\", image: \"/assets/peds/Brenda.jpg\" },\n        { name: \"Kim Ji Won\", image: \"/assets/peds/Kim Ji Won.png\" },\n        { name: \"Rhodeey\", image: \"/assets/peds/Rhodeey.jpg\" },\n        { name: \"Leo Fric\", image: \"/assets/peds/Leo Fric.jpg\" },\n      ],\n      pets: [],\n      tiltDirection: \"left\" as const,\n      pattern: \"dots\" as const,\n    },\n    {\n      tier: 4,\n      price: 1699,\n      title: \"VIP 4\",\n      description: \"Elite tier with premium vehicles and housing\",\n      icon: \"fas fa-gem\",\n      mainFeatures: [\n        {\n          text: \"Custom Phone Number\",\n          details: \"with 5 digits\",\n          highlight: \"(4 number sequence max)\",\n          example: \"44441\",\n        },\n        {\n          text: \"Custom Car\",\n          details: \"from TGRS category\",\n          highlight: \"(< ₹25,00,000 & < ₹10,00,000)\",\n        },\n        {\n          text: \"Custom Bike\",\n          details: \"from TGRS category\",\n          highlight: \"(< ₹3,50,000)\",\n        },\n        {\n          text: \"Custom Numberplate x3\",\n          details: \"\",\n          highlight: \"(2 alphabets 2 numbers)\",\n          example: \"TG12\",\n        },\n        {\n          text: \"Boat\",\n          details: \"Standard model\",\n        },\n        {\n          text: \"In-game Cash\",\n          details: \"₹75,000\",\n        },\n        {\n          text: \"Lucky Wheel Coins\",\n          details: \"1500\",\n        },\n        {\n          text: \"Tax Free\",\n          details: \"No Taxes\",\n        },\n        {\n          text: \"Extra Character Slot\",\n          details: \"+1 slot\",\n        },\n        {\n          text: \"Mechanic Discount\",\n          details: \"5% off repairs\",\n        },\n        {\n          text: \"Permanent Apartment\",\n          details: \"Forever ownership\",\n        },\n      ],\n      timeFeatures: [\n        { text: \"VIP Role\", duration: \"1 month\" },\n        { text: \"Priority Queue Premium\", duration: \"1 month\" },\n        { text: \"Tier 2 House\", duration: \"1 month\" },\n      ],\n      clothing: [\n        {\n          name: \"TGYeezes\",\n          variants: 5,\n          description: \"Premium TGRS edition footwear\",\n        },\n      ],\n      peds: [\n        { name: \"Selena\", image: \"/assets/peds/Selena.jpg\" },\n        { name: \"Jimmy\", image: \"/assets/peds/Jimmy.jpg\" },\n        { name: \"Will Stone\", image: \"/assets/peds/Will Stone.jpg\" },\n        { name: \"Manuel\", image: \"/assets/peds/Manuel.jpg\" },\n        { name: \"Spencer\", image: \"/assets/peds/Spencer.jpg\" },\n        { name: \"Carter80\", image: \"/assets/peds/Carter80.png\" },\n        { name: \"P Ballas\", image: \"/assets/peds/P Ballas.png\" },\n        { name: \"Vicente\", image: \"/assets/peds/Vicente.jpg\" },\n        { name: \"Milton\", image: \"/assets/peds/Milton.jpg\" },\n      ],\n      pets: [],\n      tiltDirection: \"right\" as const,\n      pattern: \"grid\" as const,\n    },\n    {\n      tier: 5,\n      price: 2899,\n      title: \"VIP 5\",\n      description: \"Ultimate VIP experience with everything unlocked\",\n      icon: \"fas fa-crown\",\n      mainFeatures: [\n        {\n          text: \"Custom Phone Number\",\n          details: \"with 3 digits\",\n          highlight: \"(2 number sequence max)\",\n          example: \"101-999\",\n        },\n        {\n          text: \"Custom Car\",\n          details: \"from TGRS category\",\n          highlight: \"(< ₹45,00,000, ₹18,00,000)\",\n        },\n        {\n          text: \"Custom Bike\",\n          details: \"from TGRS category\",\n          highlight: \"(< ₹5,00,000)\",\n        },\n        {\n          text: \"Helicopter\",\n          details: \"1 premium model\",\n          highlight: \"(One time)\",\n        },\n        {\n          text: \"Premium Boat\",\n          details: \"1 luxury model\",\n          highlight: \"(One time)\",\n        },\n        {\n          text: \"Custom Numberplate x4\",\n          details: \"\",\n          highlight: \"(2 alphabets 2 numbers)\",\n          example: \"TG12\",\n        },\n        {\n          text: \"In-game Cash\",\n          details: \"₹100,000\",\n        },\n        {\n          text: \"Lucky Wheel Coins\",\n          details: \"2000\",\n        },\n        {\n          text: \"Tax Free\",\n          details: \"No Taxes\",\n        },\n        {\n          text: \"Extra Character Slot\",\n          details: \"+1 slot\",\n        },\n        {\n          text: \"Mechanic Discount\",\n          details: \"10% off repairs\",\n        },\n        {\n          text: \"Tier 1 House\",\n          details: \"Permanent ownership\",\n        },\n      ],\n      timeFeatures: [\n        { text: \"VIP Role\", duration: \"1 month\" },\n        { text: \"Priority Queue\", duration: \"1 month\" },\n        { text: \"Tier 5 House with Helipad\", duration: \"1 month\" },\n      ],\n      clothing: [\n        {\n          name: \"Full Clothing Set\",\n          variants: 0,\n          description: \"Complete collection from VIP1 to VIP5\",\n        },\n        {\n          name: \"TGYeezes\",\n          variants: 5,\n          description: \"Premium TGRS edition footwear\",\n        },\n      ],\n      peds: [\n        { name: \"Adeline\", image: \"/assets/peds/Adeline.jpg\" },\n        { name: \"Akira\", image: \"/assets/peds/Akira.jpg\" },\n        { name: \"Ana Jasmine\", image: \"/assets/peds/Ana Jasmine.jpg\" },\n        { name: \"Andreas\", image: \"/assets/peds/Andreas.png\" },\n        { name: \"Anita\", image: \"/assets/peds/Anita.png\" },\n        { name: \"Antonio\", image: \"/assets/peds/Antonio.png\" },\n        { name: \"Beany\", image: \"/assets/peds/Beany.jpg\" },\n        { name: \"Bjorn\", image: \"/assets/peds/Bjorn.jpg\" },\n        { name: \"Bobby\", image: \"/assets/peds/Bobby.jpg\" },\n        { name: \"Brucie Kibbutz\", image: \"/assets/peds/Brucie Kibbutz.png\" },\n        { name: \"Carlos\", image: \"/assets/peds/Carlos.jpg\" },\n        { name: \"Catline\", image: \"/assets/peds/Catline.jpg\" },\n        { name: \"Cheng\", image: \"/assets/peds/Cheng.jpg\" },\n        { name: \"Dario Mafia\", image: \"/assets/peds/Dario Mafia.jpg\" },\n        { name: \"Diyo80\", image: \"/assets/peds/Diyo80.jpg\" },\n        { name: \"Djarot\", image: \"/assets/peds/Djarot.jpg\" },\n        { name: \"Estina Meil\", image: \"/assets/peds/Estina Meil.jpg\" },\n        { name: \"Ethan\", image: \"/assets/peds/Ethan.jpg\" },\n        { name: \"Geo\", image: \"/assets/peds/Geo.jpg\" },\n        { name: \"Gregor\", image: \"/assets/peds/Gregor.jpg\" },\n        { name: \"Hao\", image: \"/assets/peds/Hao.png\" },\n        { name: \"Hendrix\", image: \"/assets/peds/Hendrix.jpg\" },\n        { name: \"Hendry80\", image: \"/assets/peds/Hendry80.png\" },\n        { name: \"Jacob\", image: \"/assets/peds/Jacob.jpg\" },\n        { name: \"Jennifer\", image: \"/assets/peds/Jennifer.jpg\" },\n        { name: \"Jimenez\", image: \"/assets/peds/Jimenez.png\" },\n        { name: \"Jonson\", image: \"/assets/peds/Jonson.jpg\" },\n        { name: \"Julio Bert\", image: \"/assets/peds/Julio Bert.jpg\" },\n        { name: \"Lucia\", image: \"/assets/peds/Lucia.jpg\" },\n        { name: \"Marcella\", image: \"/assets/peds/Marcella.jpg\" },\n        { name: \"Marlo\", image: \"/assets/peds/Marlo.jpg\" },\n        { name: \"Martinez\", image: \"/assets/peds/Martinez.jpg\" },\n        { name: \"Michelle\", image: \"/assets/peds/Michelle.jpg\" },\n        { name: \"Moretio\", image: \"/assets/peds/Moretio.jpg\" },\n        { name: \"Norman\", image: \"/assets/peds/Norman.jpg\" },\n        { name: \"Pablo\", image: \"/assets/peds/Pablo.jpg\" },\n        { name: \"Pietro\", image: \"/assets/peds/Pietro.jpg\" },\n        { name: \"Raymond\", image: \"/assets/peds/Raymond.jpg\" },\n        { name: \"Rocky\", image: \"/assets/peds/Rocky.png\" },\n        { name: \"Roscoe\", image: \"/assets/peds/Roscoe.png\" },\n        { name: \"Velden\", image: \"/assets/peds/Velden.jpg\" },\n        { name: \"Yuna\", image: \"/assets/peds/Yuna.jpg\" },\n      ],\n      pets: [],\n      isPopular: true,\n      tiltDirection: \"left\" as const,\n      pattern: \"circuit\" as const,\n    },\n  ];\n\n  return (\n    <>\n      <Navbar />\n\n      {/* Hero Section */}\n      <Hero\n        title=\"VIP PACKAGES\"\n        subtitle=\"Unlock exclusive features and enhance your roleplay experience with our premium VIP packages\"\n        primaryButtonText=\"Contact Admin\"\n        primaryButtonHref=\"https://discord.gg/GAMravHDnB\"\n        secondaryButtonText=\"View Features\"\n        secondaryButtonHref=\"#packages\"\n        backgroundImage=\"/assets/vip.jpg\"\n      />\n\n      {/* Section Divider */}\n      <div className=\"section-divider\"></div>\n\n      {/* VIP Benefits Overview */}\n      <section className=\"py-12 md:py-20 relative bg-black/50\">\n        <div className=\"container mx-auto px-4 md:px-6\">\n          <div className=\"text-center mb-12 md:mb-16\">\n            <h2 className=\"text-2xl sm:text-3xl md:text-4xl font-display font-bold text-white mb-4 wave-heading-bg\">\n              Why Choose <span className=\"text-neon-orange\">VIP</span>?\n            </h2>\n            <p className=\"text-sm sm:text-base text-gray-400 max-w-3xl mx-auto px-2\">\n              Our VIP packages are designed to enhance your roleplay experience with exclusive features,\n              priority support, and unique customization options that set you apart in the TGRS community.\n            </p>\n          </div>\n\n          <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 sm:gap-8 mb-12 md:mb-16\">\n            <div className=\"text-center group p-4\">\n              <div className=\"w-12 h-12 sm:w-16 sm:h-16 rounded-full bg-neon-orange/20 flex items-center justify-center mx-auto mb-3 sm:mb-4 group-hover:bg-neon-orange/30 transition-colors duration-300 group-hover:scale-110 transform\">\n                <i className=\"fas fa-star text-neon-orange text-lg sm:text-2xl\"></i>\n              </div>\n              <h3 className=\"text-white font-semibold mb-2 text-sm sm:text-base group-hover:text-neon-orange transition-colors duration-300\">Exclusive Features</h3>\n              <p className=\"text-gray-400 text-xs sm:text-sm\">Access unique customization options and premium content</p>\n            </div>\n\n            <div className=\"text-center group p-4\">\n              <div className=\"w-12 h-12 sm:w-16 sm:h-16 rounded-full bg-neon-orange/20 flex items-center justify-center mx-auto mb-3 sm:mb-4 group-hover:bg-neon-orange/30 transition-colors duration-300 group-hover:scale-110 transform\">\n                <i className=\"fas fa-headset text-neon-orange text-lg sm:text-2xl\"></i>\n              </div>\n              <h3 className=\"text-white font-semibold mb-2 text-sm sm:text-base group-hover:text-neon-orange transition-colors duration-300\">Priority Support</h3>\n              <p className=\"text-gray-400 text-xs sm:text-sm\">Get faster response times and dedicated assistance</p>\n            </div>\n\n            <div className=\"text-center group p-4\">\n              <div className=\"w-12 h-12 sm:w-16 sm:h-16 rounded-full bg-neon-orange/20 flex items-center justify-center mx-auto mb-3 sm:mb-4 group-hover:bg-neon-orange/30 transition-colors duration-300 group-hover:scale-110 transform\">\n                <i className=\"fas fa-users text-neon-orange text-lg sm:text-2xl\"></i>\n              </div>\n              <h3 className=\"text-white font-semibold mb-2 text-sm sm:text-base group-hover:text-neon-orange transition-colors duration-300\">VIP Community</h3>\n              <p className=\"text-gray-400 text-xs sm:text-sm\">Join an exclusive community of premium members</p>\n            </div>\n\n            <div className=\"text-center group p-4\">\n              <div className=\"w-12 h-12 sm:w-16 sm:h-16 rounded-full bg-neon-orange/20 flex items-center justify-center mx-auto mb-3 sm:mb-4 group-hover:bg-neon-orange/30 transition-colors duration-300 group-hover:scale-110 transform\">\n                <i className=\"fas fa-gem text-neon-orange text-lg sm:text-2xl\"></i>\n              </div>\n              <h3 className=\"text-white font-semibold mb-2 text-sm sm:text-base group-hover:text-neon-orange transition-colors duration-300\">Great Value</h3>\n              <p className=\"text-gray-400 text-xs sm:text-sm\">One-time purchase with lifetime benefits</p>\n            </div>\n          </div>\n\n          {/* VIP Statistics */}\n          <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4 sm:gap-6 md:gap-8\">\n            <div className=\"text-center p-3 sm:p-4\">\n              <div className=\"text-2xl sm:text-3xl md:text-4xl font-display font-bold text-neon-orange mb-2\">500+</div>\n              <div className=\"text-gray-400 text-xs sm:text-sm\">VIP Members</div>\n            </div>\n            <div className=\"text-center p-3 sm:p-4\">\n              <div className=\"text-2xl sm:text-3xl md:text-4xl font-display font-bold text-neon-orange mb-2\">50+</div>\n              <div className=\"text-gray-400 text-xs sm:text-sm\">Exclusive Peds</div>\n            </div>\n            <div className=\"text-center p-3 sm:p-4\">\n              <div className=\"text-2xl sm:text-3xl md:text-4xl font-display font-bold text-neon-orange mb-2\">100+</div>\n              <div className=\"text-gray-400 text-xs sm:text-sm\">Custom Vehicles</div>\n            </div>\n            <div className=\"text-center p-3 sm:p-4\">\n              <div className=\"text-2xl sm:text-3xl md:text-4xl font-display font-bold text-neon-orange mb-2\">24/7</div>\n              <div className=\"text-gray-400 text-xs sm:text-sm\">VIP Support</div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Section Divider */}\n      <div className=\"section-divider\"></div>\n\n      {/* VIP Packages Section */}\n      <section id=\"packages\" className=\"py-12 md:py-20 relative bg-black\">\n        <CashFlyingAnimation />\n        <div className=\"container mx-auto px-4 sm:px-6 md:px-8 lg:px-12 relative z-10\">\n          <div className=\"text-center mb-12 md:mb-16\">\n            <h2 className=\"text-2xl sm:text-3xl md:text-4xl font-display font-bold text-white mb-4 wave-heading-bg\">\n              Choose Your <span className=\"text-neon-orange\">VIP Tier</span>\n            </h2>\n            <p className=\"text-sm sm:text-base text-gray-400 max-w-3xl mx-auto px-2\">\n              Select the perfect VIP package that suits your needs and budget. All packages include lifetime access to their respective features.\n            </p>\n          </div>\n\n          {/* VIP Cards - Mobile: 1 per row, Tablet: 2 per row, Desktop: 3 on top, 2 on bottom */}\n          <div className=\"space-y-8 sm:space-y-12 max-w-7xl mx-auto\">\n            {/* Top Row - VIP 1, 2, 3 */}\n            <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8 items-stretch\">\n              {vipTiers.slice(0, 3).map((tier) => (\n                <VIPCard\n                  key={tier.tier}\n                  tier={tier.tier}\n                  price={tier.price}\n                  title={tier.title}\n                  description={tier.description}\n                  icon={tier.icon}\n                  mainFeatures={tier.mainFeatures}\n                  timeFeatures={tier.timeFeatures}\n                  clothing={tier.clothing}\n                  peds={tier.peds}\n                  isPopular={tier.isPopular}\n                  tiltDirection={tier.tiltDirection}\n                  pattern={tier.pattern}\n                  className=\"animate-slide-in-left\"\n                />\n              ))}\n            </div>\n\n            {/* Bottom Row - VIP 4, 5 (centered) */}\n            <div className=\"flex justify-center\">\n              <div className=\"grid grid-cols-1 sm:grid-cols-2 gap-6 sm:gap-8 w-full max-w-5xl items-stretch\">\n                {vipTiers.slice(3, 5).map((tier) => (\n                  <VIPCard\n                    key={tier.tier}\n                    tier={tier.tier}\n                    price={tier.price}\n                    title={tier.title}\n                    description={tier.description}\n                    icon={tier.icon}\n                    mainFeatures={tier.mainFeatures}\n                    timeFeatures={tier.timeFeatures}\n                    clothing={tier.clothing}\n                    peds={tier.peds}\n                    isPopular={tier.isPopular}\n                    tiltDirection={tier.tiltDirection}\n                    pattern={tier.pattern}\n                    className=\"animate-slide-in-left\"\n                  />\n                ))}\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Section Divider */}\n      <div className=\"section-divider\"></div>\n\n      {/* VIP Rules & Guidelines Section */}\n      <section className=\"py-12 md:py-20 relative bg-black\">\n        <div className=\"absolute inset-0 pattern-hexagon opacity-5\"></div>\n        <div className=\"container mx-auto px-4 md:px-6 relative z-10\">\n          <div className=\"text-center mb-12 md:mb-16\">\n            <h2 className=\"text-2xl sm:text-3xl md:text-4xl font-display font-bold text-white mb-4 wave-heading-bg\">\n              <i className=\"fas fa-gavel text-neon-orange mr-2 sm:mr-4 text-lg sm:text-xl md:text-2xl\"></i>\n              VIP Rules & <span className=\"text-neon-orange\">Guidelines</span>\n            </h2>\n            <p className=\"text-sm sm:text-base text-gray-400 max-w-3xl mx-auto px-2\">\n              Important information and guidelines for all VIP members. Please read carefully to ensure you understand all terms and conditions.\n            </p>\n          </div>\n\n          <div className=\"max-w-4xl mx-auto space-y-4 sm:space-y-6\">\n            {/* Rule 1 */}\n            <div className=\"flex items-start space-x-3 sm:space-x-6 p-4 sm:p-6 rounded-xl bg-gray-900/40 border-l-4 border-neon-orange hover:bg-gray-900/60 transition-all duration-300 group\">\n              <div className=\"flex-shrink-0\">\n                <div className=\"w-10 h-10 sm:w-12 sm:h-12 rounded-full bg-neon-orange/20 flex items-center justify-center group-hover:bg-neon-orange/30 transition-colors\">\n                  <i className=\"fas fa-clock text-neon-orange text-sm sm:text-lg\"></i>\n                </div>\n              </div>\n              <div className=\"flex-grow\">\n                <h3 className=\"text-white font-bold text-base sm:text-lg mb-2\">VIP Duration Policy</h3>\n                <p className=\"text-gray-300 leading-relaxed text-sm sm:text-base\">\n                  VIP benefits are active for <span className=\"text-neon-orange font-semibold\">1 month from the date of purchase</span> unless specified otherwise in your tier package.\n                </p>\n              </div>\n            </div>\n\n            {/* Rule 2 */}\n            <div className=\"flex items-start space-x-6 p-6 rounded-xl bg-gray-900/40 border-l-4 border-neon-orange hover:bg-gray-900/60 transition-all duration-300 group\">\n              <div className=\"flex-shrink-0\">\n                <div className=\"w-12 h-12 rounded-full bg-neon-orange/20 flex items-center justify-center group-hover:bg-neon-orange/30 transition-colors\">\n                  <i className=\"fas fa-car text-neon-orange text-lg\"></i>\n                </div>\n              </div>\n              <div className=\"flex-grow\">\n                <h3 className=\"text-white font-bold text-lg mb-2\">Vehicle Selection Rules</h3>\n                <p className=\"text-gray-300 leading-relaxed\">\n                  Custom vehicles must be selected from the <span className=\"text-neon-orange font-semibold\">TGRS category</span> within the specified price range for your VIP tier.\n                </p>\n              </div>\n            </div>\n\n            {/* Rule 3 */}\n            <div className=\"flex items-start space-x-6 p-6 rounded-xl bg-gray-900/40 border-l-4 border-neon-orange hover:bg-gray-900/60 transition-all duration-300 group\">\n              <div className=\"flex-shrink-0\">\n                <div className=\"w-12 h-12 rounded-full bg-neon-orange/20 flex items-center justify-center group-hover:bg-neon-orange/30 transition-colors\">\n                  <i className=\"fas fa-mobile-alt text-neon-orange text-lg\"></i>\n                </div>\n              </div>\n              <div className=\"flex-grow\">\n                <h3 className=\"text-white font-bold text-lg mb-2\">Custom Numbers & Plates</h3>\n                <p className=\"text-gray-300 leading-relaxed\">\n                  Custom phone numbers and numberplates are <span className=\"text-neon-orange font-semibold\">subject to availability</span> and must follow server guidelines for appropriate content.\n                </p>\n              </div>\n            </div>\n\n            {/* Rule 4 */}\n            <div className=\"flex items-start space-x-6 p-6 rounded-xl bg-gray-900/40 border-l-4 border-red-500 hover:bg-gray-900/60 transition-all duration-300 group\">\n              <div className=\"flex-shrink-0\">\n                <div className=\"w-12 h-12 rounded-full bg-red-500/20 flex items-center justify-center group-hover:bg-red-500/30 transition-colors\">\n                  <i className=\"fas fa-exclamation-triangle text-red-400 text-lg\"></i>\n                </div>\n              </div>\n              <div className=\"flex-grow\">\n                <h3 className=\"text-white font-bold text-lg mb-2\">Phone Number Restrictions</h3>\n                <p className=\"text-gray-300 leading-relaxed\">\n                  Custom phone numbers cannot use <span className=\"text-red-400 font-semibold\">emergency numbers (e.g., 911)</span> and are limited to the sequence length specified in your VIP tier.\n                </p>\n              </div>\n            </div>\n\n            {/* Rule 5 */}\n            <div className=\"flex items-start space-x-6 p-6 rounded-xl bg-gray-900/40 border-l-4 border-neon-orange hover:bg-gray-900/60 transition-all duration-300 group\">\n              <div className=\"flex-shrink-0\">\n                <div className=\"w-12 h-12 rounded-full bg-neon-orange/20 flex items-center justify-center group-hover:bg-neon-orange/30 transition-colors\">\n                  <i className=\"fas fa-home text-neon-orange text-lg\"></i>\n                </div>\n              </div>\n              <div className=\"flex-grow\">\n                <h3 className=\"text-white font-bold text-lg mb-2\">Housing Benefits</h3>\n                <p className=\"text-gray-300 leading-relaxed\">\n                  Some housing benefits expire after <span className=\"text-neon-orange font-semibold\">1 month</span> and do not include furniture or decorations unless specifically mentioned.\n                </p>\n              </div>\n            </div>\n\n            {/* Rule 6 */}\n            <div className=\"flex items-start space-x-6 p-6 rounded-xl bg-gray-900/40 border-l-4 border-neon-orange hover:bg-gray-900/60 transition-all duration-300 group\">\n              <div className=\"flex-shrink-0\">\n                <div className=\"w-12 h-12 rounded-full bg-neon-orange/20 flex items-center justify-center group-hover:bg-neon-orange/30 transition-colors\">\n                  <i className=\"fas fa-shield-alt text-neon-orange text-lg\"></i>\n                </div>\n              </div>\n              <div className=\"flex-grow\">\n                <h3 className=\"text-white font-bold text-lg mb-2\">Server Rules Compliance</h3>\n                <p className=\"text-gray-300 leading-relaxed\">\n                  All VIP members must adhere to server rules. <span className=\"text-neon-orange font-semibold\">VIP status does not exempt players</span> from following community guidelines.\n                </p>\n              </div>\n            </div>\n          </div>\n\n          {/* Simple Notice */}\n          <div className=\"mt-12 max-w-4xl mx-auto text-center\">\n            <div className=\"glass rounded-xl p-6 border border-neon-orange/30\">\n              <h3 className=\"text-white font-bold text-xl mb-4\">Important Notice</h3>\n              <p className=\"text-gray-300 leading-relaxed mb-6\">\n                By purchasing VIP, you agree to follow these rules and guidelines. The staff team reserves the right to modify these terms at any time.\n              </p>\n              <a\n                href=\"https://discord.gg/GAMravHDnB\"\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n                className=\"inline-flex items-center px-6 py-3 bg-gradient-to-r from-neon-orange to-neon-red text-black font-semibold rounded-lg hover:shadow-neon-strong transition-all duration-300\"\n              >\n                <i className=\"fab fa-discord mr-2\"></i>\n                Join Discord for Support\n              </a>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Section Divider */}\n      <div className=\"section-divider\"></div>\n\n      {/* Contact Section */}\n      <section className=\"py-8 sm:py-12 md:py-16 relative bg-black/50\">\n        <div className=\"container mx-auto px-4 md:px-6\">\n          {/* Contact Information */}\n          <div className=\"text-center\">\n            <div className=\"glass rounded-lg p-4 sm:p-6 md:p-8 border border-neon-orange/30 max-w-4xl mx-auto\">\n              <h3 className=\"text-lg sm:text-xl md:text-2xl font-display font-bold text-white mb-3 sm:mb-4\">\n                Ready to Upgrade Your Experience?\n              </h3>\n              <p className=\"text-xs sm:text-sm md:text-base text-gray-400 mb-4 sm:mb-6 px-2\">\n                Contact our admin team on Discord to purchase your VIP package.\n                Payment methods and additional details will be provided upon contact.\n              </p>\n\n              {/* Quick Benefits Summary */}\n              <div className=\"grid grid-cols-1 sm:grid-cols-3 gap-3 sm:gap-4 md:gap-6\">\n                <div className=\"text-center p-2 sm:p-3\">\n                  <div className=\"w-8 h-8 sm:w-10 sm:h-10 md:w-12 md:h-12 rounded-full bg-neon-orange/20 flex items-center justify-center mx-auto mb-2 sm:mb-3\">\n                    <i className=\"fas fa-bolt text-neon-orange text-xs sm:text-sm md:text-base\"></i>\n                  </div>\n                  <h4 className=\"text-white font-semibold mb-1 sm:mb-2 text-xs sm:text-sm md:text-base\">Instant Activation</h4>\n                  <p className=\"text-gray-400 text-xs sm:text-sm\">VIP features activated immediately after purchase</p>\n                </div>\n                <div className=\"text-center p-2 sm:p-3\">\n                  <div className=\"w-8 h-8 sm:w-10 sm:h-10 md:w-12 md:h-12 rounded-full bg-neon-orange/20 flex items-center justify-center mx-auto mb-2 sm:mb-3\">\n                    <i className=\"fas fa-shield-alt text-neon-orange text-xs sm:text-sm md:text-base\"></i>\n                  </div>\n                  <h4 className=\"text-white font-semibold mb-1 sm:mb-2 text-xs sm:text-sm md:text-base\">Secure Payment</h4>\n                  <p className=\"text-gray-400 text-xs sm:text-sm\">Safe and secure payment processing</p>\n                </div>\n                <div className=\"text-center p-2 sm:p-3\">\n                  <div className=\"w-8 h-8 sm:w-10 sm:h-10 md:w-12 md:h-12 rounded-full bg-neon-orange/20 flex items-center justify-center mx-auto mb-2 sm:mb-3\">\n                    <i className=\"fas fa-infinity text-neon-orange text-xs sm:text-sm md:text-base\"></i>\n                  </div>\n                  <h4 className=\"text-white font-semibold mb-1 sm:mb-2 text-xs sm:text-sm md:text-base\">Lifetime Access</h4>\n                  <p className=\"text-gray-400 text-xs sm:text-sm\">One-time purchase, lifetime benefits</p>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      <Footer />\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;;;;;;;AAGe,SAAS;IACtB,MAAM,WAAW;QACf;YACE,MAAM;YACN,OAAO;YACP,OAAO;YACP,aAAa;YACb,MAAM;YACN,cAAc;gBACZ;oBACE,MAAM;oBACN,SAAS;oBACT,WAAW;oBACX,SAAS;gBACX;gBACA;oBACE,MAAM;oBACN,SAAS;oBACT,WAAW;gBACb;gBACA;oBACE,MAAM;oBACN,SAAS;gBACX;gBACA;oBACE,MAAM;oBACN,SAAS;gBACX;aACD;YACD,cAAc;gBACZ;oBAAE,MAAM;oBAAY,UAAU;gBAAU;gBACxC;oBAAE,MAAM;oBAAkB,UAAU;gBAAU;aAC/C;YACD,UAAU;gBACR;oBACE,MAAM;oBACN,UAAU;oBACV,aAAa;gBACf;aACD;YACD,MAAM,EAAE;YACR,MAAM,EAAE;YACR,eAAe;YACf,SAAS;QACX;QACA;YACE,MAAM;YACN,OAAO;YACP,OAAO;YACP,aAAa;YACb,MAAM;YACN,cAAc;gBACZ;oBACE,MAAM;oBACN,SAAS;oBACT,WAAW;oBACX,SAAS;gBACX;gBACA;oBACE,MAAM;oBACN,SAAS;oBACT,WAAW;gBACb;gBACA;oBACE,MAAM;oBACN,SAAS;oBACT,WAAW;gBACb;gBACA;oBACE,MAAM;oBACN,SAAS;oBACT,WAAW;oBACX,SAAS;gBACX;gBACA;oBACE,MAAM;oBACN,SAAS;gBACX;gBACA;oBACE,MAAM;oBACN,SAAS;gBACX;aACD;YACD,cAAc;gBACZ;oBAAE,MAAM;oBAAY,UAAU;gBAAU;gBACxC;oBAAE,MAAM;oBAAkB,UAAU;gBAAU;gBAC9C;oBAAE,MAAM;oBAAoB,UAAU;gBAAU;aACjD;YACD,UAAU;gBACR;oBACE,MAAM;oBACN,UAAU;oBACV,aAAa;gBACf;aACD;YACD,MAAM,EAAE;YACR,MAAM,EAAE;YACR,eAAe;YACf,SAAS;QACX;QACA;YACE,MAAM;YACN,OAAO;YACP,OAAO;YACP,aAAa;YACb,MAAM;YACN,WAAW;YACX,cAAc;gBACZ;oBACE,MAAM;oBACN,SAAS;oBACT,WAAW;oBACX,SAAS;gBACX;gBACA;oBACE,MAAM;oBACN,SAAS;oBACT,WAAW;gBACb;gBACA;oBACE,MAAM;oBACN,SAAS;oBACT,WAAW;gBACb;gBACA;oBACE,MAAM;oBACN,SAAS;oBACT,WAAW;oBACX,SAAS;gBACX;gBACA;oBACE,MAAM;oBACN,SAAS;gBACX;gBACA;oBACE,MAAM;oBACN,SAAS;gBACX;gBACA;oBACE,MAAM;oBACN,SAAS;gBACX;gBACA;oBACE,MAAM;oBACN,SAAS;gBACX;aACD;YACD,cAAc;gBACZ;oBAAE,MAAM;oBAAY,UAAU;gBAAU;gBACxC;oBAAE,MAAM;oBAAkB,UAAU;gBAAU;gBAC9C;oBAAE,MAAM;oBAAgB,UAAU;gBAAU;aAC7C;YACD,UAAU;gBACR;oBACE,MAAM;oBACN,UAAU;oBACV,aAAa;gBACf;aACD;YACD,MAAM;gBACJ;oBAAE,MAAM;oBAAU,OAAO;gBAA0B;gBACnD;oBAAE,MAAM;oBAAc,OAAO;gBAA8B;gBAC3D;oBAAE,MAAM;oBAAW,OAAO;gBAA2B;gBACrD;oBAAE,MAAM;oBAAY,OAAO;gBAA4B;aACxD;YACD,MAAM,EAAE;YACR,eAAe;YACf,SAAS;QACX;QACA;YACE,MAAM;YACN,OAAO;YACP,OAAO;YACP,aAAa;YACb,MAAM;YACN,cAAc;gBACZ;oBACE,MAAM;oBACN,SAAS;oBACT,WAAW;oBACX,SAAS;gBACX;gBACA;oBACE,MAAM;oBACN,SAAS;oBACT,WAAW;gBACb;gBACA;oBACE,MAAM;oBACN,SAAS;oBACT,WAAW;gBACb;gBACA;oBACE,MAAM;oBACN,SAAS;oBACT,WAAW;oBACX,SAAS;gBACX;gBACA;oBACE,MAAM;oBACN,SAAS;gBACX;gBACA;oBACE,MAAM;oBACN,SAAS;gBACX;gBACA;oBACE,MAAM;oBACN,SAAS;gBACX;gBACA;oBACE,MAAM;oBACN,SAAS;gBACX;gBACA;oBACE,MAAM;oBACN,SAAS;gBACX;gBACA;oBACE,MAAM;oBACN,SAAS;gBACX;gBACA;oBACE,MAAM;oBACN,SAAS;gBACX;aACD;YACD,cAAc;gBACZ;oBAAE,MAAM;oBAAY,UAAU;gBAAU;gBACxC;oBAAE,MAAM;oBAA0B,UAAU;gBAAU;gBACtD;oBAAE,MAAM;oBAAgB,UAAU;gBAAU;aAC7C;YACD,UAAU;gBACR;oBACE,MAAM;oBACN,UAAU;oBACV,aAAa;gBACf;aACD;YACD,MAAM;gBACJ;oBAAE,MAAM;oBAAU,OAAO;gBAA0B;gBACnD;oBAAE,MAAM;oBAAS,OAAO;gBAAyB;gBACjD;oBAAE,MAAM;oBAAc,OAAO;gBAA8B;gBAC3D;oBAAE,MAAM;oBAAU,OAAO;gBAA0B;gBACnD;oBAAE,MAAM;oBAAW,OAAO;gBAA2B;gBACrD;oBAAE,MAAM;oBAAY,OAAO;gBAA4B;gBACvD;oBAAE,MAAM;oBAAY,OAAO;gBAA4B;gBACvD;oBAAE,MAAM;oBAAW,OAAO;gBAA2B;gBACrD;oBAAE,MAAM;oBAAU,OAAO;gBAA0B;aACpD;YACD,MAAM,EAAE;YACR,eAAe;YACf,SAAS;QACX;QACA;YACE,MAAM;YACN,OAAO;YACP,OAAO;YACP,aAAa;YACb,MAAM;YACN,cAAc;gBACZ;oBACE,MAAM;oBACN,SAAS;oBACT,WAAW;oBACX,SAAS;gBACX;gBACA;oBACE,MAAM;oBACN,SAAS;oBACT,WAAW;gBACb;gBACA;oBACE,MAAM;oBACN,SAAS;oBACT,WAAW;gBACb;gBACA;oBACE,MAAM;oBACN,SAAS;oBACT,WAAW;gBACb;gBACA;oBACE,MAAM;oBACN,SAAS;oBACT,WAAW;gBACb;gBACA;oBACE,MAAM;oBACN,SAAS;oBACT,WAAW;oBACX,SAAS;gBACX;gBACA;oBACE,MAAM;oBACN,SAAS;gBACX;gBACA;oBACE,MAAM;oBACN,SAAS;gBACX;gBACA;oBACE,MAAM;oBACN,SAAS;gBACX;gBACA;oBACE,MAAM;oBACN,SAAS;gBACX;gBACA;oBACE,MAAM;oBACN,SAAS;gBACX;gBACA;oBACE,MAAM;oBACN,SAAS;gBACX;aACD;YACD,cAAc;gBACZ;oBAAE,MAAM;oBAAY,UAAU;gBAAU;gBACxC;oBAAE,MAAM;oBAAkB,UAAU;gBAAU;gBAC9C;oBAAE,MAAM;oBAA6B,UAAU;gBAAU;aAC1D;YACD,UAAU;gBACR;oBACE,MAAM;oBACN,UAAU;oBACV,aAAa;gBACf;gBACA;oBACE,MAAM;oBACN,UAAU;oBACV,aAAa;gBACf;aACD;YACD,MAAM;gBACJ;oBAAE,MAAM;oBAAW,OAAO;gBAA2B;gBACrD;oBAAE,MAAM;oBAAS,OAAO;gBAAyB;gBACjD;oBAAE,MAAM;oBAAe,OAAO;gBAA+B;gBAC7D;oBAAE,MAAM;oBAAW,OAAO;gBAA2B;gBACrD;oBAAE,MAAM;oBAAS,OAAO;gBAAyB;gBACjD;oBAAE,MAAM;oBAAW,OAAO;gBAA2B;gBACrD;oBAAE,MAAM;oBAAS,OAAO;gBAAyB;gBACjD;oBAAE,MAAM;oBAAS,OAAO;gBAAyB;gBACjD;oBAAE,MAAM;oBAAS,OAAO;gBAAyB;gBACjD;oBAAE,MAAM;oBAAkB,OAAO;gBAAkC;gBACnE;oBAAE,MAAM;oBAAU,OAAO;gBAA0B;gBACnD;oBAAE,MAAM;oBAAW,OAAO;gBAA2B;gBACrD;oBAAE,MAAM;oBAAS,OAAO;gBAAyB;gBACjD;oBAAE,MAAM;oBAAe,OAAO;gBAA+B;gBAC7D;oBAAE,MAAM;oBAAU,OAAO;gBAA0B;gBACnD;oBAAE,MAAM;oBAAU,OAAO;gBAA0B;gBACnD;oBAAE,MAAM;oBAAe,OAAO;gBAA+B;gBAC7D;oBAAE,MAAM;oBAAS,OAAO;gBAAyB;gBACjD;oBAAE,MAAM;oBAAO,OAAO;gBAAuB;gBAC7C;oBAAE,MAAM;oBAAU,OAAO;gBAA0B;gBACnD;oBAAE,MAAM;oBAAO,OAAO;gBAAuB;gBAC7C;oBAAE,MAAM;oBAAW,OAAO;gBAA2B;gBACrD;oBAAE,MAAM;oBAAY,OAAO;gBAA4B;gBACvD;oBAAE,MAAM;oBAAS,OAAO;gBAAyB;gBACjD;oBAAE,MAAM;oBAAY,OAAO;gBAA4B;gBACvD;oBAAE,MAAM;oBAAW,OAAO;gBAA2B;gBACrD;oBAAE,MAAM;oBAAU,OAAO;gBAA0B;gBACnD;oBAAE,MAAM;oBAAc,OAAO;gBAA8B;gBAC3D;oBAAE,MAAM;oBAAS,OAAO;gBAAyB;gBACjD;oBAAE,MAAM;oBAAY,OAAO;gBAA4B;gBACvD;oBAAE,MAAM;oBAAS,OAAO;gBAAyB;gBACjD;oBAAE,MAAM;oBAAY,OAAO;gBAA4B;gBACvD;oBAAE,MAAM;oBAAY,OAAO;gBAA4B;gBACvD;oBAAE,MAAM;oBAAW,OAAO;gBAA2B;gBACrD;oBAAE,MAAM;oBAAU,OAAO;gBAA0B;gBACnD;oBAAE,MAAM;oBAAS,OAAO;gBAAyB;gBACjD;oBAAE,MAAM;oBAAU,OAAO;gBAA0B;gBACnD;oBAAE,MAAM;oBAAW,OAAO;gBAA2B;gBACrD;oBAAE,MAAM;oBAAS,OAAO;gBAAyB;gBACjD;oBAAE,MAAM;oBAAU,OAAO;gBAA0B;gBACnD;oBAAE,MAAM;oBAAU,OAAO;gBAA0B;gBACnD;oBAAE,MAAM;oBAAQ,OAAO;gBAAwB;aAChD;YACD,MAAM,EAAE;YACR,WAAW;YACX,eAAe;YACf,SAAS;QACX;KACD;IAED,qBACE;;0BACE,8OAAC,4HAAA,CAAA,UAAM;;;;;0BAGP,8OAAC,0HAAA,CAAA,UAAI;gBACH,OAAM;gBACN,UAAS;gBACT,mBAAkB;gBAClB,mBAAkB;gBAClB,qBAAoB;gBACpB,qBAAoB;gBACpB,iBAAgB;;;;;;0BAIlB,8OAAC;gBAAI,WAAU;;;;;;0BAGf,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;;wCAA0F;sDAC3F,8OAAC;4CAAK,WAAU;sDAAmB;;;;;;wCAAU;;;;;;;8CAE1D,8OAAC;oCAAE,WAAU;8CAA4D;;;;;;;;;;;;sCAM3E,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAE,WAAU;;;;;;;;;;;sDAEf,8OAAC;4CAAG,WAAU;sDAAiH;;;;;;sDAC/H,8OAAC;4CAAE,WAAU;sDAAmC;;;;;;;;;;;;8CAGlD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAE,WAAU;;;;;;;;;;;sDAEf,8OAAC;4CAAG,WAAU;sDAAiH;;;;;;sDAC/H,8OAAC;4CAAE,WAAU;sDAAmC;;;;;;;;;;;;8CAGlD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAE,WAAU;;;;;;;;;;;sDAEf,8OAAC;4CAAG,WAAU;sDAAiH;;;;;;sDAC/H,8OAAC;4CAAE,WAAU;sDAAmC;;;;;;;;;;;;8CAGlD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAE,WAAU;;;;;;;;;;;sDAEf,8OAAC;4CAAG,WAAU;sDAAiH;;;;;;sDAC/H,8OAAC;4CAAE,WAAU;sDAAmC;;;;;;;;;;;;;;;;;;sCAKpD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAgF;;;;;;sDAC/F,8OAAC;4CAAI,WAAU;sDAAmC;;;;;;;;;;;;8CAEpD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAgF;;;;;;sDAC/F,8OAAC;4CAAI,WAAU;sDAAmC;;;;;;;;;;;;8CAEpD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAgF;;;;;;sDAC/F,8OAAC;4CAAI,WAAU;sDAAmC;;;;;;;;;;;;8CAEpD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAgF;;;;;;sDAC/F,8OAAC;4CAAI,WAAU;sDAAmC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO1D,8OAAC;gBAAI,WAAU;;;;;;0BAGf,8OAAC;gBAAQ,IAAG;gBAAW,WAAU;;kCAC/B,8OAAC,yIAAA,CAAA,UAAmB;;;;;kCACpB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;;4CAA0F;0DAC1F,8OAAC;gDAAK,WAAU;0DAAmB;;;;;;;;;;;;kDAEjD,8OAAC;wCAAE,WAAU;kDAA4D;;;;;;;;;;;;0CAM3E,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAI,WAAU;kDACZ,SAAS,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,qBACzB,8OAAC,gIAAA,CAAA,UAAO;gDAEN,MAAM,KAAK,IAAI;gDACf,OAAO,KAAK,KAAK;gDACjB,OAAO,KAAK,KAAK;gDACjB,aAAa,KAAK,WAAW;gDAC7B,MAAM,KAAK,IAAI;gDACf,cAAc,KAAK,YAAY;gDAC/B,cAAc,KAAK,YAAY;gDAC/B,UAAU,KAAK,QAAQ;gDACvB,MAAM,KAAK,IAAI;gDACf,WAAW,KAAK,SAAS;gDACzB,eAAe,KAAK,aAAa;gDACjC,SAAS,KAAK,OAAO;gDACrB,WAAU;+CAbL,KAAK,IAAI;;;;;;;;;;kDAmBpB,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;sDACZ,SAAS,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,qBACzB,8OAAC,gIAAA,CAAA,UAAO;oDAEN,MAAM,KAAK,IAAI;oDACf,OAAO,KAAK,KAAK;oDACjB,OAAO,KAAK,KAAK;oDACjB,aAAa,KAAK,WAAW;oDAC7B,MAAM,KAAK,IAAI;oDACf,cAAc,KAAK,YAAY;oDAC/B,cAAc,KAAK,YAAY;oDAC/B,UAAU,KAAK,QAAQ;oDACvB,MAAM,KAAK,IAAI;oDACf,WAAW,KAAK,SAAS;oDACzB,eAAe,KAAK,aAAa;oDACjC,SAAS,KAAK,OAAO;oDACrB,WAAU;mDAbL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAuB5B,8OAAC;gBAAI,WAAU;;;;;;0BAGf,8OAAC;gBAAQ,WAAU;;kCACjB,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;gDAAE,WAAU;;;;;;4CAAgF;0DACjF,8OAAC;gDAAK,WAAU;0DAAmB;;;;;;;;;;;;kDAEjD,8OAAC;wCAAE,WAAU;kDAA4D;;;;;;;;;;;;0CAK3E,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAE,WAAU;;;;;;;;;;;;;;;;0DAGjB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAiD;;;;;;kEAC/D,8OAAC;wDAAE,WAAU;;4DAAqD;0EACpC,8OAAC;gEAAK,WAAU;0EAAiC;;;;;;4DAAwC;;;;;;;;;;;;;;;;;;;kDAM3H,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAE,WAAU;;;;;;;;;;;;;;;;0DAGjB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAoC;;;;;;kEAClD,8OAAC;wDAAE,WAAU;;4DAAgC;0EACD,8OAAC;gEAAK,WAAU;0EAAiC;;;;;;4DAAoB;;;;;;;;;;;;;;;;;;;kDAMrH,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAE,WAAU;;;;;;;;;;;;;;;;0DAGjB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAoC;;;;;;kEAClD,8OAAC;wDAAE,WAAU;;4DAAgC;0EACD,8OAAC;gEAAK,WAAU;0EAAiC;;;;;;4DAA8B;;;;;;;;;;;;;;;;;;;kDAM/H,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAE,WAAU;;;;;;;;;;;;;;;;0DAGjB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAoC;;;;;;kEAClD,8OAAC;wDAAE,WAAU;;4DAAgC;0EACX,8OAAC;gEAAK,WAAU;0EAA6B;;;;;;4DAAoC;;;;;;;;;;;;;;;;;;;kDAMvH,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAE,WAAU;;;;;;;;;;;;;;;;0DAGjB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAoC;;;;;;kEAClD,8OAAC;wDAAE,WAAU;;4DAAgC;0EACR,8OAAC;gEAAK,WAAU;0EAAiC;;;;;;4DAAc;;;;;;;;;;;;;;;;;;;kDAMxG,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAE,WAAU;;;;;;;;;;;;;;;;0DAGjB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAoC;;;;;;kEAClD,8OAAC;wDAAE,WAAU;;4DAAgC;0EACE,8OAAC;gEAAK,WAAU;0EAAiC;;;;;;4DAAyC;;;;;;;;;;;;;;;;;;;;;;;;;0CAO/I,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAoC;;;;;;sDAClD,8OAAC;4CAAE,WAAU;sDAAqC;;;;;;sDAGlD,8OAAC;4CACC,MAAK;4CACL,QAAO;4CACP,KAAI;4CACJ,WAAU;;8DAEV,8OAAC;oDAAE,WAAU;;;;;;gDAA0B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASjD,8OAAC;gBAAI,WAAU;;;;;;0BAGf,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BAEb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAgF;;;;;;8CAG9F,8OAAC;oCAAE,WAAU;8CAAkE;;;;;;8CAM/E,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAE,WAAU;;;;;;;;;;;8DAEf,8OAAC;oDAAG,WAAU;8DAAwE;;;;;;8DACtF,8OAAC;oDAAE,WAAU;8DAAmC;;;;;;;;;;;;sDAElD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAE,WAAU;;;;;;;;;;;8DAEf,8OAAC;oDAAG,WAAU;8DAAwE;;;;;;8DACtF,8OAAC;oDAAE,WAAU;8DAAmC;;;;;;;;;;;;sDAElD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAE,WAAU;;;;;;;;;;;8DAEf,8OAAC;oDAAG,WAAU;8DAAwE;;;;;;8DACtF,8OAAC;oDAAE,WAAU;8DAAmC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQ5D,8OAAC,4HAAA,CAAA,UAAM;;;;;;;AAGb"}}, {"offset": {"line": 2666, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2677, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": ""}}, {"offset": {"line": 2677, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}