{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/TGRS%20Website/tgrs-website/src/components/Navbar.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport Link from \"next/link\";\nimport Image from \"next/image\";\nimport { usePathname } from \"next/navigation\";\n\nconst Navbar = () => {\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n  const [scrolled, setScrolled] = useState(false);\n  const pathname = usePathname();\n\n  // Helper function to check if a link is active\n  const isActive = (href: string) => {\n    if (href === \"/\") {\n      return pathname === \"/\";\n    }\n    return pathname.startsWith(href);\n  };\n\n  useEffect(() => {\n    const handleScroll = () => {\n      if (window.scrollY > 10) {\n        setScrolled(true);\n      } else {\n        setScrolled(false);\n      }\n    };\n\n    window.addEventListener(\"scroll\", handleScroll);\n    return () => window.removeEventListener(\"scroll\", handleScroll);\n  }, []);\n\n  const toggleMenu = () => {\n    setIsMenuOpen(!isMenuOpen);\n  };\n\n  return (\n    <nav\n      className={`fixed top-0 left-0 w-full z-50 transition-all duration-500 ${\n        scrolled\n          ? \"bg-black/90 backdrop-blur-xl py-2 shadow-lg shadow-neon-orange/10\"\n          : \"bg-transparent py-4\"\n      }`}\n    >\n      {/* Subtle pattern overlay */}\n      <div className=\"absolute inset-0 pattern-dots opacity-20 pointer-events-none\"></div>\n\n      <div className=\"container mx-auto px-4 md:px-6 relative\">\n        <div className=\"flex items-center justify-between\">\n          {/* Enhanced Logo */}\n          <Link href=\"/\" className=\"flex items-center group\">\n            <div className=\"relative h-12 w-12 mr-3 transition-all duration-300 group-hover:scale-110 group-hover:rotate-3\">\n              <div className=\"absolute inset-0 bg-gradient-to-br from-neon-orange/20 to-neon-red/20 rounded-full blur-sm group-hover:blur-md transition-all duration-300\"></div>\n              <Image\n                src=\"/assets/tgrs-logo.png\"\n                alt=\"TGRS Logo\"\n                width={48}\n                height={48}\n                className=\"w-full h-full object-contain relative z-10 drop-shadow-lg\"\n                priority\n              />\n            </div>\n            <span className=\"text-white font-display font-bold text-xl tracking-wider group-hover:text-neon-orange transition-all duration-300\">\n              TGRS\n            </span>\n          </Link>\n\n          {/* Enhanced Desktop Navigation */}\n          <div className=\"hidden md:flex items-center space-x-1\">\n            {/* Navigation Links */}\n            <div className=\"flex items-center space-x-1 mr-6\">\n              <Link\n                href=\"/\"\n                className=\"relative px-4 py-2 text-white font-medium transition-all duration-300 group\"\n              >\n                <span className={`relative z-10 transition-colors duration-300 ${\n                  isActive(\"/\") ? \"text-neon-orange\" : \"group-hover:text-neon-orange\"\n                }`}>\n                  Home\n                </span>\n\n                {/* Active state particle effects */}\n                {isActive(\"/\") && (\n                  <div className=\"absolute inset-0 opacity-100 transition-all duration-500\">\n                    {/* Floating particles */}\n                    <div className=\"absolute top-1 left-2 w-1 h-1 bg-neon-orange rounded-full animate-ping\"></div>\n                    <div className=\"absolute top-2 right-3 w-1.5 h-1.5 bg-yellow-400 rounded-full animate-pulse\"></div>\n                    <div className=\"absolute bottom-2 left-3 w-1 h-1 bg-neon-orange rounded-full animate-bounce\"></div>\n                    <div className=\"absolute bottom-1 right-2 w-1 h-1 bg-yellow-300 rounded-full animate-ping\" style={{animationDelay: '0.5s'}}></div>\n                    <div className=\"absolute top-3 left-1/2 w-0.5 h-0.5 bg-neon-orange rounded-full animate-pulse\" style={{animationDelay: '0.3s'}}></div>\n                  </div>\n                )}\n\n                <div className={`absolute inset-0 bg-gradient-to-r from-neon-orange/10 to-neon-red/10 rounded-lg transition-all duration-300 scale-95 ${\n                  isActive(\"/\") ? \"opacity-30 scale-100\" : \"opacity-0 group-hover:opacity-100 group-hover:scale-100\"\n                }`}></div>\n              </Link>\n              <Link\n                href=\"/about\"\n                className=\"relative px-4 py-2 text-white font-medium transition-all duration-300 group\"\n              >\n                <span className={`relative z-10 transition-colors duration-300 ${\n                  isActive(\"/about\") ? \"text-neon-orange\" : \"group-hover:text-neon-orange\"\n                }`}>\n                  About\n                </span>\n\n                {/* Active state particle effects */}\n                {isActive(\"/about\") && (\n                  <div className=\"absolute inset-0 opacity-100 transition-all duration-500\">\n                    {/* Floating particles */}\n                    <div className=\"absolute top-1 left-2 w-1 h-1 bg-neon-orange rounded-full animate-ping\"></div>\n                    <div className=\"absolute top-2 right-3 w-1.5 h-1.5 bg-yellow-400 rounded-full animate-pulse\"></div>\n                    <div className=\"absolute bottom-2 left-3 w-1 h-1 bg-neon-orange rounded-full animate-bounce\"></div>\n                    <div className=\"absolute bottom-1 right-2 w-1 h-1 bg-yellow-300 rounded-full animate-ping\" style={{animationDelay: '0.5s'}}></div>\n                    <div className=\"absolute top-3 left-1/2 w-0.5 h-0.5 bg-neon-orange rounded-full animate-pulse\" style={{animationDelay: '0.3s'}}></div>\n                  </div>\n                )}\n\n                <div className={`absolute inset-0 bg-gradient-to-r from-neon-orange/10 to-neon-red/10 rounded-lg transition-all duration-300 scale-95 ${\n                  isActive(\"/about\") ? \"opacity-30 scale-100\" : \"opacity-0 group-hover:opacity-100 group-hover:scale-100\"\n                }`}></div>\n              </Link>\n              <Link\n                href=\"/features\"\n                className=\"relative px-4 py-2 text-white font-medium transition-all duration-300 group\"\n              >\n                <span className={`relative z-10 transition-colors duration-300 ${\n                  isActive(\"/features\") ? \"text-neon-orange\" : \"group-hover:text-neon-orange\"\n                }`}>\n                  Features\n                </span>\n\n                {/* Active state particle effects */}\n                {isActive(\"/features\") && (\n                  <div className=\"absolute inset-0 opacity-100 transition-all duration-500\">\n                    {/* Floating particles */}\n                    <div className=\"absolute top-1 left-2 w-1 h-1 bg-neon-orange rounded-full animate-ping\"></div>\n                    <div className=\"absolute top-2 right-3 w-1.5 h-1.5 bg-yellow-400 rounded-full animate-pulse\"></div>\n                    <div className=\"absolute bottom-2 left-3 w-1 h-1 bg-neon-orange rounded-full animate-bounce\"></div>\n                    <div className=\"absolute bottom-1 right-2 w-1 h-1 bg-yellow-300 rounded-full animate-ping\" style={{animationDelay: '0.5s'}}></div>\n                    <div className=\"absolute top-3 left-1/2 w-0.5 h-0.5 bg-neon-orange rounded-full animate-pulse\" style={{animationDelay: '0.3s'}}></div>\n                  </div>\n                )}\n\n                <div className={`absolute inset-0 bg-gradient-to-r from-neon-orange/10 to-neon-red/10 rounded-lg transition-all duration-300 scale-95 ${\n                  isActive(\"/features\") ? \"opacity-30 scale-100\" : \"opacity-0 group-hover:opacity-100 group-hover:scale-100\"\n                }`}></div>\n              </Link>\n              <Link\n                href=\"/rules\"\n                className=\"relative px-4 py-2 text-white font-medium transition-all duration-300 group\"\n              >\n                <span className={`relative z-10 transition-colors duration-300 ${\n                  isActive(\"/rules\") ? \"text-neon-orange\" : \"group-hover:text-neon-orange\"\n                }`}>\n                  Rules\n                </span>\n\n                {/* Active state particle effects */}\n                {isActive(\"/rules\") && (\n                  <div className=\"absolute inset-0 opacity-100 transition-all duration-500\">\n                    {/* Floating particles */}\n                    <div className=\"absolute top-1 left-2 w-1 h-1 bg-neon-orange rounded-full animate-ping\"></div>\n                    <div className=\"absolute top-2 right-3 w-1.5 h-1.5 bg-yellow-400 rounded-full animate-pulse\"></div>\n                    <div className=\"absolute bottom-2 left-3 w-1 h-1 bg-neon-orange rounded-full animate-bounce\"></div>\n                    <div className=\"absolute bottom-1 right-2 w-1 h-1 bg-yellow-300 rounded-full animate-ping\" style={{animationDelay: '0.5s'}}></div>\n                    <div className=\"absolute top-3 left-1/2 w-0.5 h-0.5 bg-neon-orange rounded-full animate-pulse\" style={{animationDelay: '0.3s'}}></div>\n                  </div>\n                )}\n\n                <div className={`absolute inset-0 bg-gradient-to-r from-neon-orange/10 to-neon-red/10 rounded-lg transition-all duration-300 scale-95 ${\n                  isActive(\"/rules\") ? \"opacity-30 scale-100\" : \"opacity-0 group-hover:opacity-100 group-hover:scale-100\"\n                }`}></div>\n              </Link>\n              <Link\n                href=\"/vip\"\n                className=\"relative px-4 py-2 text-white font-medium transition-all duration-300 group overflow-hidden\"\n              >\n                {/* Purple heart with particles */}\n                <div className={`absolute inset-0 transition-all duration-500 ${\n                  isActive(\"/vip\") ? \"opacity-100\" : \"opacity-0 group-hover:opacity-100\"\n                }`}>\n                  {/* Floating particles */}\n                  <div className=\"absolute top-1 left-2 w-1 h-1 bg-purple-400 rounded-full animate-ping\"></div>\n                  <div className=\"absolute top-3 right-3 w-1.5 h-1.5 bg-pink-400 rounded-full animate-pulse\"></div>\n                  <div className=\"absolute bottom-2 left-1 w-1 h-1 bg-purple-300 rounded-full animate-bounce\"></div>\n                  <div className=\"absolute bottom-1 right-2 w-1 h-1 bg-pink-300 rounded-full animate-ping\" style={{animationDelay: '0.5s'}}></div>\n                  <div className=\"absolute top-2 left-1/2 w-0.5 h-0.5 bg-purple-500 rounded-full animate-pulse\" style={{animationDelay: '0.3s'}}></div>\n                  <div className=\"absolute bottom-3 right-1 w-0.5 h-0.5 bg-pink-500 rounded-full animate-bounce\" style={{animationDelay: '0.7s'}}></div>\n                </div>\n\n                {/* Purple gradient background */}\n                <div className={`absolute inset-0 bg-gradient-to-r from-purple-600/20 via-pink-500/20 to-purple-600/20 rounded-lg transition-all duration-300 scale-95 ${\n                  isActive(\"/vip\") ? \"opacity-100 scale-100\" : \"opacity-0 group-hover:opacity-100 group-hover:scale-100\"\n                }`}></div>\n\n                {/* Glow effect */}\n                <div className={`absolute inset-0 bg-gradient-to-r from-purple-500/30 to-pink-500/30 rounded-lg blur-sm transition-all duration-500 scale-110 ${\n                  isActive(\"/vip\") ? \"opacity-60\" : \"opacity-0 group-hover:opacity-60\"\n                }`}></div>\n\n                <span className={`relative z-10 transition-colors duration-300 flex items-center ${\n                  isActive(\"/vip\") ? \"text-purple-300\" : \"group-hover:text-purple-300\"\n                }`}>\n                  <i className={`fas fa-heart mr-2 transition-colors duration-300 ${\n                    isActive(\"/vip\")\n                      ? \"text-pink-400 animate-pulse\"\n                      : \"text-purple-400 group-hover:text-pink-400 group-hover:animate-pulse\"\n                  }`}></i>\n                  VIP\n                </span>\n\n              </Link>\n            </div>\n\n            {/* Enhanced Play Now Button */}\n            <Link\n              href=\"cfx.re/join/o57lj7\"\n              className=\"relative px-6 py-2.5 rounded-full font-medium transition-all duration-300 group overflow-hidden\"\n            >\n              {/* Animated background */}\n              <div className=\"absolute inset-0 bg-gradient-to-r from-neon-orange to-neon-red transition-all duration-300 group-hover:scale-105\"></div>\n              <div className=\"absolute inset-0 bg-gradient-to-r from-neon-orange-light to-neon-red opacity-0 group-hover:opacity-100 transition-all duration-300\"></div>\n\n              {/* Glow effect */}\n              <div className=\"absolute inset-0 rounded-full bg-gradient-to-r from-neon-orange to-neon-red blur-md opacity-0 group-hover:opacity-50 transition-all duration-300 scale-110\"></div>\n\n              {/* Button text */}\n              <span className=\"relative z-10 text-white font-semibold tracking-wide group-hover:text-black transition-colors duration-300\">\n                Play Now\n              </span>\n            </Link>\n          </div>\n\n          {/* Enhanced Mobile Menu Button */}\n          <button\n            className=\"md:hidden relative p-2 text-white focus:outline-none group transition-all duration-300\"\n            onClick={toggleMenu}\n          >\n            <div className=\"absolute inset-0 bg-gradient-to-r from-neon-orange/20 to-neon-red/20 rounded-lg opacity-0 group-hover:opacity-100 transition-all duration-300\"></div>\n            <svg\n              className={`w-6 h-6 relative z-10 transition-all duration-300 ${isMenuOpen ? 'rotate-90 text-neon-orange' : 'rotate-0'}`}\n              fill=\"none\"\n              stroke=\"currentColor\"\n              viewBox=\"0 0 24 24\"\n              xmlns=\"http://www.w3.org/2000/svg\"\n            >\n              {isMenuOpen ? (\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  strokeWidth={2}\n                  d=\"M6 18L18 6M6 6l12 12\"\n                />\n              ) : (\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  strokeWidth={2}\n                  d=\"M4 6h16M4 12h16M4 18h16\"\n                />\n              )}\n            </svg>\n          </button>\n        </div>\n\n        {/* Enhanced Mobile Menu */}\n        {isMenuOpen && (\n          <div className=\"md:hidden mt-6 relative animate-menu-slide-down\">\n            {/* Background with pattern */}\n            <div className=\"absolute inset-0 pattern-circuit opacity-20 rounded-xl\"></div>\n            <div className=\"relative bg-black/90 backdrop-blur-xl rounded-xl p-6 border border-neon-orange/40 shadow-lg shadow-neon-orange/20 animate-navbar-glow overflow-hidden\">\n              {/* Particle effects background */}\n              <div className=\"absolute inset-0 opacity-30 pointer-events-none\">\n                <div className=\"absolute top-2 left-4 w-1 h-1 bg-neon-orange rounded-full animate-ping\"></div>\n                <div className=\"absolute top-4 right-6 w-1.5 h-1.5 bg-yellow-400 rounded-full animate-pulse\"></div>\n                <div className=\"absolute bottom-6 left-3 w-1 h-1 bg-neon-orange rounded-full animate-bounce\"></div>\n                <div className=\"absolute bottom-3 right-4 w-1 h-1 bg-yellow-300 rounded-full animate-ping\" style={{animationDelay: '0.5s'}}></div>\n                <div className=\"absolute top-1/2 left-1/2 w-0.5 h-0.5 bg-neon-orange rounded-full animate-pulse\" style={{animationDelay: '0.3s'}}></div>\n                <div className=\"absolute top-8 right-2 w-0.5 h-0.5 bg-yellow-500 rounded-full animate-bounce\" style={{animationDelay: '0.7s'}}></div>\n              </div>\n              <div className=\"flex flex-col space-y-4\">\n                <Link\n                  href=\"/\"\n                  className=\"relative px-4 py-3 text-white font-medium transition-all duration-300 group rounded-lg\"\n                  onClick={() => setIsMenuOpen(false)}\n                >\n                  <div className={`absolute inset-0 bg-gradient-to-r from-neon-orange/10 to-neon-red/10 rounded-lg transition-all duration-300 ${\n                    isActive(\"/\") ? \"opacity-30\" : \"opacity-0 group-hover:opacity-100\"\n                  }`}></div>\n                  <span className={`relative z-10 transition-colors duration-300 ${\n                    isActive(\"/\") ? \"text-neon-orange\" : \"group-hover:text-neon-orange\"\n                  }`}>\n                    Home\n                    {isActive(\"/\") && <span className=\"ml-2 text-neon-orange animate-pulse\">✦</span>}\n                  </span>\n                </Link>\n                <Link\n                  href=\"/about\"\n                  className=\"relative px-4 py-3 text-white font-medium transition-all duration-300 group rounded-lg\"\n                  onClick={() => setIsMenuOpen(false)}\n                >\n                  <div className={`absolute inset-0 bg-gradient-to-r from-neon-orange/10 to-neon-red/10 rounded-lg transition-all duration-300 ${\n                    isActive(\"/about\") ? \"opacity-30\" : \"opacity-0 group-hover:opacity-100\"\n                  }`}></div>\n                  <span className={`relative z-10 transition-colors duration-300 ${\n                    isActive(\"/about\") ? \"text-neon-orange\" : \"group-hover:text-neon-orange\"\n                  }`}>\n                    About\n                    {isActive(\"/about\") && <span className=\"ml-2 text-neon-orange animate-pulse\">✦</span>}\n                  </span>\n                </Link>\n                <Link\n                  href=\"/features\"\n                  className=\"relative px-4 py-3 text-white font-medium transition-all duration-300 group rounded-lg\"\n                  onClick={() => setIsMenuOpen(false)}\n                >\n                  <div className={`absolute inset-0 bg-gradient-to-r from-neon-orange/10 to-neon-red/10 rounded-lg transition-all duration-300 ${\n                    isActive(\"/features\") ? \"opacity-30\" : \"opacity-0 group-hover:opacity-100\"\n                  }`}></div>\n                  <span className={`relative z-10 transition-colors duration-300 ${\n                    isActive(\"/features\") ? \"text-neon-orange\" : \"group-hover:text-neon-orange\"\n                  }`}>\n                    Features\n                    {isActive(\"/features\") && <span className=\"ml-2 text-neon-orange animate-pulse\">✦</span>}\n                  </span>\n                </Link>\n                <Link\n                  href=\"/rules\"\n                  className=\"relative px-4 py-3 text-white font-medium transition-all duration-300 group rounded-lg\"\n                  onClick={() => setIsMenuOpen(false)}\n                >\n                  <div className={`absolute inset-0 bg-gradient-to-r from-neon-orange/10 to-neon-red/10 rounded-lg transition-all duration-300 ${\n                    isActive(\"/rules\") ? \"opacity-30\" : \"opacity-0 group-hover:opacity-100\"\n                  }`}></div>\n                  <span className={`relative z-10 transition-colors duration-300 ${\n                    isActive(\"/rules\") ? \"text-neon-orange\" : \"group-hover:text-neon-orange\"\n                  }`}>\n                    Rules\n                    {isActive(\"/rules\") && <span className=\"ml-2 text-neon-orange animate-pulse\">✦</span>}\n                  </span>\n                </Link>\n                <Link\n                  href=\"/vip\"\n                  className=\"relative px-4 py-3 text-white font-medium transition-all duration-300 group rounded-lg overflow-hidden\"\n                  onClick={() => setIsMenuOpen(false)}\n                >\n                  {/* Purple heart with particles */}\n                  <div className=\"absolute inset-0 opacity-0 group-hover:opacity-100 transition-all duration-500\">\n                    {/* Floating particles */}\n                    <div className=\"absolute top-2 left-3 w-1 h-1 bg-purple-400 rounded-full animate-ping\"></div>\n                    <div className=\"absolute top-4 right-4 w-1.5 h-1.5 bg-pink-400 rounded-full animate-pulse\"></div>\n                    <div className=\"absolute bottom-3 left-2 w-1 h-1 bg-purple-300 rounded-full animate-bounce\"></div>\n                    <div className=\"absolute bottom-2 right-3 w-1 h-1 bg-pink-300 rounded-full animate-ping\" style={{animationDelay: '0.5s'}}></div>\n                    <div className=\"absolute top-3 left-1/2 w-0.5 h-0.5 bg-purple-500 rounded-full animate-pulse\" style={{animationDelay: '0.3s'}}></div>\n                    <div className=\"absolute bottom-4 right-2 w-0.5 h-0.5 bg-pink-500 rounded-full animate-bounce\" style={{animationDelay: '0.7s'}}></div>\n                  </div>\n\n                  {/* Purple gradient background */}\n                  <div className=\"absolute inset-0 bg-gradient-to-r from-purple-600/20 via-pink-500/20 to-purple-600/20 rounded-lg opacity-0 group-hover:opacity-100 transition-all duration-300\"></div>\n\n                  {/* Glow effect */}\n                  <div className=\"absolute inset-0 bg-gradient-to-r from-purple-500/30 to-pink-500/30 rounded-lg blur-sm opacity-0 group-hover:opacity-60 transition-all duration-500 scale-110\"></div>\n\n                  <span className={`relative z-10 transition-colors duration-300 flex items-center ${\n                    isActive(\"/vip\") ? \"text-purple-300\" : \"group-hover:text-purple-300\"\n                  }`}>\n                    <i className={`fas fa-heart mr-2 transition-colors duration-300 ${\n                      isActive(\"/vip\")\n                        ? \"text-pink-400 animate-pulse\"\n                        : \"text-purple-400 group-hover:text-pink-400 group-hover:animate-pulse\"\n                    }`}></i>\n                    VIP\n                    {isActive(\"/vip\") && <span className=\"ml-2 text-purple-400 animate-pulse\">♥</span>}\n                  </span>\n                </Link>\n\n                {/* Divider */}\n                <div className=\"h-px bg-gradient-to-r from-transparent via-neon-orange/50 to-transparent my-2\"></div>\n\n                {/* Enhanced Mobile Play Now Button */}\n                <Link\n                  href=\"cfx.re/join/o57lj7\"\n                  className=\"relative px-6 py-3 rounded-full font-medium transition-all duration-300 group overflow-hidden text-center\"\n                  onClick={() => setIsMenuOpen(false)}\n                >\n                  {/* Animated background */}\n                  <div className=\"absolute inset-0 bg-gradient-to-r from-neon-orange to-neon-red transition-all duration-300\"></div>\n                  <div className=\"absolute inset-0 bg-gradient-to-r from-neon-orange-light to-neon-red opacity-0 group-hover:opacity-100 transition-all duration-300\"></div>\n\n                  {/* Glow effect */}\n                  <div className=\"absolute inset-0 rounded-full bg-gradient-to-r from-neon-orange to-neon-red blur-sm opacity-0 group-hover:opacity-60 transition-all duration-300\"></div>\n\n                  {/* Button text */}\n                  <span className=\"relative z-10 text-white font-semibold tracking-wide group-hover:text-black transition-colors duration-300\">\n                    Play Now\n                  </span>\n                </Link>\n              </div>\n            </div>\n          </div>\n        )}\n      </div>\n    </nav>\n  );\n};\n\nexport default Navbar;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAOA,MAAM,SAAS;;IACb,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,+CAA+C;IAC/C,MAAM,WAAW,CAAC;QAChB,IAAI,SAAS,KAAK;YAChB,OAAO,aAAa;QACtB;QACA,OAAO,SAAS,UAAU,CAAC;IAC7B;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACR,MAAM;iDAAe;oBACnB,IAAI,OAAO,OAAO,GAAG,IAAI;wBACvB,YAAY;oBACd,OAAO;wBACL,YAAY;oBACd;gBACF;;YAEA,OAAO,gBAAgB,CAAC,UAAU;YAClC;oCAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;;QACpD;2BAAG,EAAE;IAEL,MAAM,aAAa;QACjB,cAAc,CAAC;IACjB;IAEA,qBACE,6LAAC;QACC,WAAW,CAAC,2DAA2D,EACrE,WACI,sEACA,uBACJ;;0BAGF,6LAAC;gBAAI,WAAU;;;;;;0BAEf,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;;kDACvB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC,gIAAA,CAAA,UAAK;gDACJ,KAAI;gDACJ,KAAI;gDACJ,OAAO;gDACP,QAAQ;gDACR,WAAU;gDACV,QAAQ;;;;;;;;;;;;kDAGZ,6LAAC;wCAAK,WAAU;kDAAoH;;;;;;;;;;;;0CAMtI,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;;kEAEV,6LAAC;wDAAK,WAAW,CAAC,6CAA6C,EAC7D,SAAS,OAAO,qBAAqB,gCACrC;kEAAE;;;;;;oDAKH,SAAS,sBACR,6LAAC;wDAAI,WAAU;;0EAEb,6LAAC;gEAAI,WAAU;;;;;;0EACf,6LAAC;gEAAI,WAAU;;;;;;0EACf,6LAAC;gEAAI,WAAU;;;;;;0EACf,6LAAC;gEAAI,WAAU;gEAA4E,OAAO;oEAAC,gBAAgB;gEAAM;;;;;;0EACzH,6LAAC;gEAAI,WAAU;gEAAgF,OAAO;oEAAC,gBAAgB;gEAAM;;;;;;;;;;;;kEAIjI,6LAAC;wDAAI,WAAW,CAAC,qHAAqH,EACpI,SAAS,OAAO,yBAAyB,2DACzC;;;;;;;;;;;;0DAEJ,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;;kEAEV,6LAAC;wDAAK,WAAW,CAAC,6CAA6C,EAC7D,SAAS,YAAY,qBAAqB,gCAC1C;kEAAE;;;;;;oDAKH,SAAS,2BACR,6LAAC;wDAAI,WAAU;;0EAEb,6LAAC;gEAAI,WAAU;;;;;;0EACf,6LAAC;gEAAI,WAAU;;;;;;0EACf,6LAAC;gEAAI,WAAU;;;;;;0EACf,6LAAC;gEAAI,WAAU;gEAA4E,OAAO;oEAAC,gBAAgB;gEAAM;;;;;;0EACzH,6LAAC;gEAAI,WAAU;gEAAgF,OAAO;oEAAC,gBAAgB;gEAAM;;;;;;;;;;;;kEAIjI,6LAAC;wDAAI,WAAW,CAAC,qHAAqH,EACpI,SAAS,YAAY,yBAAyB,2DAC9C;;;;;;;;;;;;0DAEJ,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;;kEAEV,6LAAC;wDAAK,WAAW,CAAC,6CAA6C,EAC7D,SAAS,eAAe,qBAAqB,gCAC7C;kEAAE;;;;;;oDAKH,SAAS,8BACR,6LAAC;wDAAI,WAAU;;0EAEb,6LAAC;gEAAI,WAAU;;;;;;0EACf,6LAAC;gEAAI,WAAU;;;;;;0EACf,6LAAC;gEAAI,WAAU;;;;;;0EACf,6LAAC;gEAAI,WAAU;gEAA4E,OAAO;oEAAC,gBAAgB;gEAAM;;;;;;0EACzH,6LAAC;gEAAI,WAAU;gEAAgF,OAAO;oEAAC,gBAAgB;gEAAM;;;;;;;;;;;;kEAIjI,6LAAC;wDAAI,WAAW,CAAC,qHAAqH,EACpI,SAAS,eAAe,yBAAyB,2DACjD;;;;;;;;;;;;0DAEJ,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;;kEAEV,6LAAC;wDAAK,WAAW,CAAC,6CAA6C,EAC7D,SAAS,YAAY,qBAAqB,gCAC1C;kEAAE;;;;;;oDAKH,SAAS,2BACR,6LAAC;wDAAI,WAAU;;0EAEb,6LAAC;gEAAI,WAAU;;;;;;0EACf,6LAAC;gEAAI,WAAU;;;;;;0EACf,6LAAC;gEAAI,WAAU;;;;;;0EACf,6LAAC;gEAAI,WAAU;gEAA4E,OAAO;oEAAC,gBAAgB;gEAAM;;;;;;0EACzH,6LAAC;gEAAI,WAAU;gEAAgF,OAAO;oEAAC,gBAAgB;gEAAM;;;;;;;;;;;;kEAIjI,6LAAC;wDAAI,WAAW,CAAC,qHAAqH,EACpI,SAAS,YAAY,yBAAyB,2DAC9C;;;;;;;;;;;;0DAEJ,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;;kEAGV,6LAAC;wDAAI,WAAW,CAAC,6CAA6C,EAC5D,SAAS,UAAU,gBAAgB,qCACnC;;0EAEA,6LAAC;gEAAI,WAAU;;;;;;0EACf,6LAAC;gEAAI,WAAU;;;;;;0EACf,6LAAC;gEAAI,WAAU;;;;;;0EACf,6LAAC;gEAAI,WAAU;gEAA0E,OAAO;oEAAC,gBAAgB;gEAAM;;;;;;0EACvH,6LAAC;gEAAI,WAAU;gEAA+E,OAAO;oEAAC,gBAAgB;gEAAM;;;;;;0EAC5H,6LAAC;gEAAI,WAAU;gEAAgF,OAAO;oEAAC,gBAAgB;gEAAM;;;;;;;;;;;;kEAI/H,6LAAC;wDAAI,WAAW,CAAC,sIAAsI,EACrJ,SAAS,UAAU,0BAA0B,2DAC7C;;;;;;kEAGF,6LAAC;wDAAI,WAAW,CAAC,6HAA6H,EAC5I,SAAS,UAAU,eAAe,oCAClC;;;;;;kEAEF,6LAAC;wDAAK,WAAW,CAAC,+DAA+D,EAC/E,SAAS,UAAU,oBAAoB,+BACvC;;0EACA,6LAAC;gEAAE,WAAW,CAAC,iDAAiD,EAC9D,SAAS,UACL,gCACA,uEACJ;;;;;;4DAAM;;;;;;;;;;;;;;;;;;;kDAQd,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;;0DAGV,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAI,WAAU;;;;;;0DAGf,6LAAC;gDAAI,WAAU;;;;;;0DAGf,6LAAC;gDAAK,WAAU;0DAA6G;;;;;;;;;;;;;;;;;;0CAOjI,6LAAC;gCACC,WAAU;gCACV,SAAS;;kDAET,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCACC,WAAW,CAAC,kDAAkD,EAAE,aAAa,+BAA+B,YAAY;wCACxH,MAAK;wCACL,QAAO;wCACP,SAAQ;wCACR,OAAM;kDAEL,2BACC,6LAAC;4CACC,eAAc;4CACd,gBAAe;4CACf,aAAa;4CACb,GAAE;;;;;iEAGJ,6LAAC;4CACC,eAAc;4CACd,gBAAe;4CACf,aAAa;4CACb,GAAE;;;;;;;;;;;;;;;;;;;;;;;oBAQX,4BACC,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAI,WAAU;gDAA4E,OAAO;oDAAC,gBAAgB;gDAAM;;;;;;0DACzH,6LAAC;gDAAI,WAAU;gDAAkF,OAAO;oDAAC,gBAAgB;gDAAM;;;;;;0DAC/H,6LAAC;gDAAI,WAAU;gDAA+E,OAAO;oDAAC,gBAAgB;gDAAM;;;;;;;;;;;;kDAE9H,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;gDACV,SAAS,IAAM,cAAc;;kEAE7B,6LAAC;wDAAI,WAAW,CAAC,4GAA4G,EAC3H,SAAS,OAAO,eAAe,qCAC/B;;;;;;kEACF,6LAAC;wDAAK,WAAW,CAAC,6CAA6C,EAC7D,SAAS,OAAO,qBAAqB,gCACrC;;4DAAE;4DAED,SAAS,sBAAQ,6LAAC;gEAAK,WAAU;0EAAsC;;;;;;;;;;;;;;;;;;0DAG5E,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;gDACV,SAAS,IAAM,cAAc;;kEAE7B,6LAAC;wDAAI,WAAW,CAAC,4GAA4G,EAC3H,SAAS,YAAY,eAAe,qCACpC;;;;;;kEACF,6LAAC;wDAAK,WAAW,CAAC,6CAA6C,EAC7D,SAAS,YAAY,qBAAqB,gCAC1C;;4DAAE;4DAED,SAAS,2BAAa,6LAAC;gEAAK,WAAU;0EAAsC;;;;;;;;;;;;;;;;;;0DAGjF,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;gDACV,SAAS,IAAM,cAAc;;kEAE7B,6LAAC;wDAAI,WAAW,CAAC,4GAA4G,EAC3H,SAAS,eAAe,eAAe,qCACvC;;;;;;kEACF,6LAAC;wDAAK,WAAW,CAAC,6CAA6C,EAC7D,SAAS,eAAe,qBAAqB,gCAC7C;;4DAAE;4DAED,SAAS,8BAAgB,6LAAC;gEAAK,WAAU;0EAAsC;;;;;;;;;;;;;;;;;;0DAGpF,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;gDACV,SAAS,IAAM,cAAc;;kEAE7B,6LAAC;wDAAI,WAAW,CAAC,4GAA4G,EAC3H,SAAS,YAAY,eAAe,qCACpC;;;;;;kEACF,6LAAC;wDAAK,WAAW,CAAC,6CAA6C,EAC7D,SAAS,YAAY,qBAAqB,gCAC1C;;4DAAE;4DAED,SAAS,2BAAa,6LAAC;gEAAK,WAAU;0EAAsC;;;;;;;;;;;;;;;;;;0DAGjF,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;gDACV,SAAS,IAAM,cAAc;;kEAG7B,6LAAC;wDAAI,WAAU;;0EAEb,6LAAC;gEAAI,WAAU;;;;;;0EACf,6LAAC;gEAAI,WAAU;;;;;;0EACf,6LAAC;gEAAI,WAAU;;;;;;0EACf,6LAAC;gEAAI,WAAU;gEAA0E,OAAO;oEAAC,gBAAgB;gEAAM;;;;;;0EACvH,6LAAC;gEAAI,WAAU;gEAA+E,OAAO;oEAAC,gBAAgB;gEAAM;;;;;;0EAC5H,6LAAC;gEAAI,WAAU;gEAAgF,OAAO;oEAAC,gBAAgB;gEAAM;;;;;;;;;;;;kEAI/H,6LAAC;wDAAI,WAAU;;;;;;kEAGf,6LAAC;wDAAI,WAAU;;;;;;kEAEf,6LAAC;wDAAK,WAAW,CAAC,+DAA+D,EAC/E,SAAS,UAAU,oBAAoB,+BACvC;;0EACA,6LAAC;gEAAE,WAAW,CAAC,iDAAiD,EAC9D,SAAS,UACL,gCACA,uEACJ;;;;;;4DAAM;4DAEP,SAAS,yBAAW,6LAAC;gEAAK,WAAU;0EAAqC;;;;;;;;;;;;;;;;;;0DAK9E,6LAAC;gDAAI,WAAU;;;;;;0DAGf,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;gDACV,SAAS,IAAM,cAAc;;kEAG7B,6LAAC;wDAAI,WAAU;;;;;;kEACf,6LAAC;wDAAI,WAAU;;;;;;kEAGf,6LAAC;wDAAI,WAAU;;;;;;kEAGf,6LAAC;wDAAK,WAAU;kEAA6G;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAW/I;GAhZM;;QAGa,qIAAA,CAAA,cAAW;;;KAHxB;uCAkZS"}}, {"offset": {"line": 1047, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1053, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/TGRS%20Website/tgrs-website/src/components/Footer.tsx"], "sourcesContent": ["import Link from \"next/link\";\nimport Image from \"next/image\";\n\nconst Footer = () => {\n  return (\n    <footer className=\"bg-black/50 pattern-dots-footer border-t border-neon-orange/30 pt-12 pb-6 relative\">\n      <div className=\"container mx-auto px-4 md:px-6\">\n        <div className=\"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-6 sm:gap-8 mb-6 sm:mb-8\">\n          {/* Logo and About */}\n          <div className=\"col-span-1 sm:col-span-2 md:col-span-1\">\n            <Link href=\"/\" className=\"flex items-center mb-4\">\n              <div className=\"relative h-8 w-8 sm:h-10 sm:w-10 mr-3\">\n                <Image\n                  src=\"/assets/tgrs-logo.png\"\n                  alt=\"TGRS Logo\"\n                  width={40}\n                  height={40}\n                  className=\"w-full h-full object-contain\"\n                />\n              </div>\n              <span className=\"text-white font-display font-bold text-lg sm:text-xl tracking-wider\">\n                TGRS\n              </span>\n            </Link>\n            <p className=\"text-gray-400 text-xs sm:text-sm mb-4\">\n              The premier Telugu community FiveM roleplay server with immersive experiences and unique features.\n            </p>\n            <div className=\"flex space-x-3 sm:space-x-4\">\n              <a href=\"#\" className=\"text-white hover:text-neon-orange transition-colors\">\n                <svg className=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\" aria-hidden=\"true\">\n                  <path fillRule=\"evenodd\" d=\"M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z\" clipRule=\"evenodd\"></path>\n                </svg>\n              </a>\n              <a href=\"#\" className=\"text-white hover:text-neon-orange transition-colors\">\n                <svg className=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\" aria-hidden=\"true\">\n                  <path fillRule=\"evenodd\" d=\"M12.315 2c2.43 0 2.784.013 3.808.06 1.064.049 1.791.218 2.427.465a4.902 4.902 0 011.772 1.153 4.902 4.902 0 011.153 1.772c.247.636.416 1.363.465 2.427.048 1.067.06 1.407.06 4.123v.08c0 2.643-.012 2.987-.06 4.043-.049 1.064-.218 1.791-.465 2.427a4.902 4.902 0 01-1.153 1.772 4.902 4.902 0 01-1.772 1.153c-.636.247-1.363.416-2.427.465-1.067.048-1.407.06-4.123.06h-.08c-2.643 0-2.987-.012-4.043-.06-1.064-.049-1.791-.218-2.427-.465a4.902 4.902 0 01-1.772-1.153 4.902 4.902 0 01-1.153-1.772c-.247-.636-.416-1.363-.465-2.427-.047-1.024-.06-1.379-.06-3.808v-.63c0-2.43.013-2.784.06-3.808.049-1.064.218-1.791.465-2.427a4.902 4.902 0 011.153-1.772A4.902 4.902 0 015.45 2.525c.636-.247 1.363-.416 2.427-.465C8.901 2.013 9.256 2 11.685 2h.63zm-.081 1.802h-.468c-2.456 0-2.784.011-3.807.058-.975.045-1.504.207-1.857.344-.467.182-.8.398-1.15.748-.35.35-.566.683-.748 1.15-.137.353-.3.882-.344 1.857-.047 1.023-.058 1.351-.058 3.807v.468c0 2.456.011 2.784.058 3.807.045.975.207 1.504.344 1.857.182.466.399.8.748 1.15.35.35.683.566 1.15.748.353.137.882.3 1.857.344 1.054.048 1.37.058 4.041.058h.08c2.597 0 2.917-.01 3.96-.058.976-.045 1.505-.207 1.858-.344.466-.182.8-.398 1.15-.748.35-.35.566-.683.748-1.15.137-.353.3-.882.344-1.857.048-1.055.058-1.37.058-4.041v-.08c0-2.597-.01-2.917-.058-3.96-.045-.976-.207-1.505-.344-1.858a3.097 3.097 0 00-.748-1.15 3.098 3.098 0 00-1.15-.748c-.353-.137-.882-.3-1.857-.344-1.023-.047-1.351-.058-3.807-.058zM12 6.865a5.135 5.135 0 110 10.27 5.135 5.135 0 010-10.27zm0 1.802a3.333 3.333 0 100 6.666 3.333 3.333 0 000-6.666zm5.338-3.205a1.2 1.2 0 110 2.4 1.2 1.2 0 010-2.4z\" clipRule=\"evenodd\"></path>\n                </svg>\n              </a>\n              <a href=\"#\" className=\"text-white hover:text-neon-orange transition-colors\">\n                <svg className=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\" aria-hidden=\"true\">\n                  <path d=\"M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84\"></path>\n                </svg>\n              </a>\n              <a href=\"#\" className=\"text-white hover:text-neon-orange transition-colors\">\n                <svg className=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\" aria-hidden=\"true\">\n                  <path fillRule=\"evenodd\" d=\"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10c5.51 0 10-4.48 10-10S17.51 2 12 2zm6.605 4.61a8.502 8.502 0 011.93 5.314c-.281-.054-3.101-.629-5.943-.271-.065-.141-.12-.293-.184-.445a25.416 25.416 0 00-.564-1.236c3.145-1.28 4.577-3.124 4.761-3.362zM12 3.475c2.17 0 4.154.813 5.662 2.148-.152.216-1.443 1.941-4.48 3.08-1.399-2.57-2.95-4.675-3.189-5A8.687 8.687 0 0112 3.475zm-3.633.803a53.896 53.896 0 013.167 4.935c-3.992 1.063-7.517 1.04-7.896 1.04a8.581 8.581 0 014.729-5.975zM3.453 12.01v-.26c.37.01 4.512.065 8.775-1.215.25.477.477.965.694 1.453-.109.033-.228.065-.336.098-4.404 1.42-6.747 5.303-6.942 5.629a8.522 8.522 0 01-2.19-5.705zM12 20.547a8.482 8.482 0 01-5.239-1.8c.152-.315 1.888-3.656 6.703-5.337.022-.01.033-.01.054-.022a35.318 35.318 0 011.823 6.475 8.4 8.4 0 01-3.341.684zm4.761-1.465c-.086-.52-.542-3.015-1.659-6.084 2.679-.423 5.022.271 5.314.369a8.468 8.468 0 01-3.655 5.715z\" clipRule=\"evenodd\"></path>\n                </svg>\n              </a>\n            </div>\n          </div>\n\n          {/* Quick Links */}\n          <div className=\"col-span-1\">\n            <h3 className=\"text-white font-display font-semibold text-base sm:text-lg mb-3 sm:mb-4\">Quick Links</h3>\n            <ul className=\"space-y-1 sm:space-y-2\">\n              <li>\n                <Link href=\"/\" className=\"text-gray-400 hover:text-neon-orange transition-colors text-xs sm:text-sm\">\n                  Home\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/about\" className=\"text-gray-400 hover:text-neon-orange transition-colors text-xs sm:text-sm\">\n                  About\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/features\" className=\"text-gray-400 hover:text-neon-orange transition-colors text-xs sm:text-sm\">\n                  Features\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/rules\" className=\"text-gray-400 hover:text-neon-orange transition-colors text-xs sm:text-sm\">\n                  Rules\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/contact\" className=\"text-gray-400 hover:text-neon-orange transition-colors text-xs sm:text-sm\">\n                  Contact\n                </Link>\n              </li>\n            </ul>\n          </div>\n\n          {/* Server Info */}\n          <div className=\"col-span-1\">\n            <h3 className=\"text-white font-display font-semibold text-base sm:text-lg mb-3 sm:mb-4\">Server Info</h3>\n            <ul className=\"space-y-1 sm:space-y-2\">\n              <li className=\"text-gray-400 text-xs sm:text-sm\">\n                <span className=\"text-neon-orange mr-2\">•</span> 24/7 Uptime\n              </li>\n              <li className=\"text-gray-400 text-xs sm:text-sm\">\n                <span className=\"text-neon-orange mr-2\">•</span> Active Staff\n              </li>\n              <li className=\"text-gray-400 text-xs sm:text-sm\">\n                <span className=\"text-neon-orange mr-2\">•</span> Custom Scripts\n              </li>\n              <li className=\"text-gray-400 text-xs sm:text-sm\">\n                <span className=\"text-neon-orange mr-2\">•</span> Regular Updates\n              </li>\n              <li className=\"text-gray-400 text-xs sm:text-sm\">\n                <span className=\"text-neon-orange mr-2\">•</span> Community Events\n              </li>\n            </ul>\n          </div>\n\n          {/* Discord */}\n          <div className=\"col-span-1 sm:col-span-2 md:col-span-1\">\n            <h3 className=\"text-white font-display font-semibold text-base sm:text-lg mb-3 sm:mb-4\">Join Our Discord</h3>\n            <p className=\"text-gray-400 text-xs sm:text-sm mb-3 sm:mb-4\">\n              Join our Discord community for server updates, events, and to connect with other players.\n            </p>\n            <a\n              href=\"https://discord.gg/GAMravHDnB\"\n              target=\"_blank\"\n              rel=\"noopener noreferrer\"\n              className=\"px-3 py-2 sm:px-4 sm:py-2 rounded-md bg-[#5865F2] hover:bg-[#4752C4] text-white font-medium transition-colors flex items-center justify-center space-x-2 w-full sm:w-auto text-xs sm:text-sm\"\n            >\n              <svg className=\"w-4 h-4 sm:w-5 sm:h-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path d=\"M20.317 4.3698a19.7913 19.7913 0 00-4.8851-1.5152.0741.0741 0 00-.0785.0371c-.211.3753-.4447.8648-.6083 1.2495-1.8447-.2762-3.68-.2762-5.4868 0-.1636-.3933-.4058-.8742-.6177-1.2495a.077.077 0 00-.0785-.037 19.7363 19.7363 0 00-4.8852 1.515.0699.0699 0 00-.0321.0277C.5334 9.0458-.319 13.5799.0992 18.0578a.0824.0824 0 00.0312.0561c2.0528 1.5076 4.0413 2.4228 5.9929 3.0294a.0777.0777 0 00.0842-.0276c.4616-.6304.8731-1.2952 1.226-1.9942a.076.076 0 00-.0416-.1057c-.6528-.2476-1.2743-.5495-1.8722-.8923a.077.077 0 01-.0076-.1277c.1258-.0943.2517-.1923.3718-.2914a.0743.0743 0 01.0776-.0105c3.9278 1.7933 8.18 1.7933 12.0614 0a.0739.0739 0 01.0785.0095c.1202.099.246.1981.3728.2924a.077.077 0 01-.0066.1276 12.2986 12.2986 0 01-1.873.8914.0766.0766 0 00-.0407.1067c.3604.698.7719 1.3628 1.225 1.9932a.076.076 0 00.0842.0286c1.961-.6067 3.9495-1.5219 6.0023-3.0294a.077.077 0 00.0313-.0552c.5004-5.177-.8382-9.6739-3.5485-13.6604a.061.061 0 00-.0312-.0286zM8.02 15.3312c-1.1825 0-2.1569-1.0857-2.1569-2.419 0-1.3332.9555-2.4189 2.157-2.4189 1.2108 0 2.1757 1.0952 2.1568 2.419 0 1.3332-.9555 2.4189-2.1569 2.4189zm7.9748 0c-1.1825 0-2.1569-1.0857-2.1569-2.419 0-1.3332.9554-2.4189 2.1569-2.4189 1.2108 0 2.1757 1.0952 2.1568 2.419 0 1.3332-.946 2.4189-2.1568 2.4189Z\"></path>\n              </svg>\n              <span>Join Discord</span>\n            </a>\n          </div>\n        </div>\n\n        {/* Copyright */}\n        <div className=\"border-t border-gray-800 pt-4 sm:pt-6 mt-4 sm:mt-6 text-center\">\n          <p className=\"text-gray-500 text-xs sm:text-sm\">\n            &copy; {new Date().getFullYear()} TGRS - Telugu Gaming Roleplay Server. All rights reserved.\n          </p>\n        </div>\n      </div>\n    </footer>\n  );\n};\n\nexport default Footer;\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAEA,MAAM,SAAS;IACb,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAI,WAAU;;sDACvB,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;gDACJ,KAAI;gDACJ,KAAI;gDACJ,OAAO;gDACP,QAAQ;gDACR,WAAU;;;;;;;;;;;sDAGd,6LAAC;4CAAK,WAAU;sDAAsE;;;;;;;;;;;;8CAIxF,6LAAC;oCAAE,WAAU;8CAAwC;;;;;;8CAGrD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,MAAK;4CAAI,WAAU;sDACpB,cAAA,6LAAC;gDAAI,WAAU;gDAAU,MAAK;gDAAe,SAAQ;gDAAY,eAAY;0DAC3E,cAAA,6LAAC;oDAAK,UAAS;oDAAU,GAAE;oDAAyQ,UAAS;;;;;;;;;;;;;;;;sDAGjT,6LAAC;4CAAE,MAAK;4CAAI,WAAU;sDACpB,cAAA,6LAAC;gDAAI,WAAU;gDAAU,MAAK;gDAAe,SAAQ;gDAAY,eAAY;0DAC3E,cAAA,6LAAC;oDAAK,UAAS;oDAAU,GAAE;oDAA8jD,UAAS;;;;;;;;;;;;;;;;sDAGtmD,6LAAC;4CAAE,MAAK;4CAAI,WAAU;sDACpB,cAAA,6LAAC;gDAAI,WAAU;gDAAU,MAAK;gDAAe,SAAQ;gDAAY,eAAY;0DAC3E,cAAA,6LAAC;oDAAK,GAAE;;;;;;;;;;;;;;;;sDAGZ,6LAAC;4CAAE,MAAK;4CAAI,WAAU;sDACpB,cAAA,6LAAC;gDAAI,WAAU;gDAAU,MAAK;gDAAe,SAAQ;gDAAY,eAAY;0DAC3E,cAAA,6LAAC;oDAAK,UAAS;oDAAU,GAAE;oDAA03B,UAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAOt6B,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAA0E;;;;;;8CACxF,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAI,WAAU;0DAA4E;;;;;;;;;;;sDAIvG,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAS,WAAU;0DAA4E;;;;;;;;;;;sDAI5G,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAY,WAAU;0DAA4E;;;;;;;;;;;sDAI/G,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAS,WAAU;0DAA4E;;;;;;;;;;;sDAI5G,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;0DAA4E;;;;;;;;;;;;;;;;;;;;;;;sCAQlH,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAA0E;;;;;;8CACxF,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;oDAAK,WAAU;8DAAwB;;;;;;gDAAQ;;;;;;;sDAElD,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;oDAAK,WAAU;8DAAwB;;;;;;gDAAQ;;;;;;;sDAElD,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;oDAAK,WAAU;8DAAwB;;;;;;gDAAQ;;;;;;;sDAElD,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;oDAAK,WAAU;8DAAwB;;;;;;gDAAQ;;;;;;;sDAElD,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;oDAAK,WAAU;8DAAwB;;;;;;gDAAQ;;;;;;;;;;;;;;;;;;;sCAMtD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAA0E;;;;;;8CACxF,6LAAC;oCAAE,WAAU;8CAAgD;;;;;;8CAG7D,6LAAC;oCACC,MAAK;oCACL,QAAO;oCACP,KAAI;oCACJ,WAAU;;sDAEV,6LAAC;4CAAI,WAAU;4CAAwB,MAAK;4CAAe,SAAQ;sDACjE,cAAA,6LAAC;gDAAK,GAAE;;;;;;;;;;;sDAEV,6LAAC;sDAAK;;;;;;;;;;;;;;;;;;;;;;;;8BAMZ,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAE,WAAU;;4BAAmC;4BACtC,IAAI,OAAO,WAAW;4BAAG;;;;;;;;;;;;;;;;;;;;;;;AAM7C;KAnIM;uCAqIS"}}, {"offset": {"line": 1560, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1566, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/TGRS%20Website/tgrs-website/src/app/rules/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useState } from \"react\";\nimport Navbar from \"@/components/Navbar\";\nimport Footer from \"@/components/Footer\";\n\nconst ruleCategories = [\n  {\n    id: \"server-rules\",\n    name: \"Server Rules\",\n    rules: [\n      {\n        id: \"inappropriate-language\",\n        title: \"Inappropriate Language\",\n        content: \"Don't use 18+ words in city situations & Server meetings.\"\n      },\n      {\n        id: \"exploits\",\n        title: \"Exploits and Glitches\",\n        content: \"Do not use glitches of the game to your advantage or use exploits. If you happen to find so, make sure you report it on the discord.\"\n      },\n      {\n        id: \"respect\",\n        title: \"Respect Others\",\n        content: \"Respect others at all times. Racial slurs, hate comments, rape references and sexual harassment are not allowed.\"\n      },\n      {\n        id: \"cultural-respect\",\n        title: \"Cultural Respect\",\n        content: \"Under no circumstances, you are not allowed to speak about others Religion, Culture or Country, you must respect each other.\"\n      },\n      {\n        id: \"discriminatory-rp\",\n        title: \"Discriminatory RP\",\n        content: \"You are not allowed to have role-play which can be deemed to have racist, slavery or homophobic acts.\"\n      },\n      {\n        id: \"rule-encouragement\",\n        title: \"Rule Encouragement\",\n        content: \"Encouraging or forcing other members of the community to break rules is strictly prohibited and will be punished with a Temp/permanent ban.\"\n      },\n      {\n        id: \"ban-evasion\",\n        title: \"Ban Evasion\",\n        content: \"After being banned, you may not play on other accounts to get around the ban or attempt anything that might trick staff into thinking you're a different person.\"\n      },\n      {\n        id: \"griefing\",\n        title: \"Griefing\",\n        content: \"Do not grief others. Griefing is the act of intentionally angering or irritating another player with malicious intent. Example: Consistently stalking a player to force RP or ruining their RP experiences.\"\n      },\n      {\n        id: \"ooc-targeting\",\n        title: \"OOC Targeting\",\n        content: \"Players must not target, harass, or take any adverse actions against another player out-of-character (OOC) due to in-character (IC) actions or roleplay scenarios. All interactions that arise within roleplay must remain strictly within the context of the game.\"\n      },\n      {\n        id: \"safe-zone-crimes\",\n        title: \"Safe Zone Crimes\",\n        content: \"You are not allowed to do any criminal activity such as Hotwiring/Illegal Dealings and many more in Safe Zones until and unless any situation is going on.\"\n      },\n      {\n        id: \"toxicity\",\n        title: \"Toxicity\",\n        content: \"Without any proper reason, Toxicity will not be tolerated at all in the server.\"\n      },\n      {\n        id: \"emergency-abuse\",\n        title: \"Emergency Service Abuse\",\n        content: \"Additionally, 311/911 should not be used for cursing the government employees. (No Actions will be taken if both the party agrees)\"\n      },\n      {\n        id: \"hacker-items\",\n        title: \"Hacker Items\",\n        content: \"Accepting any kind of items from hackers/doublers may it be in any means, should be reported to the support team as soon as possible. Taking any items from them will lead to ban.\"\n      },\n      {\n        id: \"playtime-requirement\",\n        title: \"Playtime Requirement\",\n        content: \"You must have a minimum of 12 hours playtime to initiate any kind of criminal activity.\"\n      },\n      {\n        id: \"pov-requirement\",\n        title: \"POV Requirement\",\n        content: \"You must have a POV (point of view) if you are initiating any type of situation & must have citizen ID's.\"\n      },\n      {\n        id: \"ems-hostage\",\n        title: \"EMS Hostage\",\n        content: \"You can't take EMS as a hostage at any cost when he/she is on-duty.\"\n      },\n      {\n        id: \"report-pov\",\n        title: \"Report POV\",\n        content: \"When submitting a player report it is mandatory to provide a POV that is raw and not edited/cut in middle. A valid judgement will be taken by the administration upon proper investigations of POV's from both parties.\"\n      }\n    ]\n  },\n  {\n    id: \"general\",\n    name: \"General Rules\",\n    rules: [\n      {\n        id: \"rdm\",\n        title: \"RDM\",\n        content: \"1. Killing people without giving them a specific reason. Always take the initiative and explain why you're doing what you're doing. They wouldn't always need to initiate before shooting, depending on the story building up to a fight between different tribes. RDM is killing someone when they are AFK.\"\n      },\n      {\n        id: \"combat-logging\",\n        title: \"Combat Logging\",\n        content: \"2. Logging out or Quitting during an active situation to avoid role-play it leads to ban.\"\n      },\n      {\n        id: \"vdm\",\n        title: \"VDM\",\n        content: \"3. Never kill anyone on purpose using a vehicle as a weapon. Also you cannot deliberately use a vehicle to run over someone unless there is direct threat of your own life.\"\n      },\n      {\n        id: \"cop-baiting\",\n        title: \"Cop Baiting\",\n        content: \"Cop baiting is prohibited, this is when your sole purpose is to attract the attention of LEO with no gain to the development of your character.\"\n      }\n    ]\n  },\n  {\n    id: \"meta-gaming\",\n    name: \"Meta Gaming\",\n    rules: [\n      {\n        id: \"ooc-information\",\n        title: \"Using Out-of-Character Information\",\n        content: \"1. Using out-of-character/external information for in-game situations. This includes stream sniping (OOC to IC).\"\n      },\n      {\n        id: \"content-permission\",\n        title: \"Verbal/Digital Content Permission\",\n        content: \"2. Verbal/Digital Content Permission.\"\n      },\n      {\n        id: \"copyrighted-music\",\n        title: \"Copyrighted Music\",\n        content: \"3. You cannot play copyrighted music unless you have everyone's permission, and you cannot play it at government places such as Pillbox/PD, etc.\"\n      },\n      {\n        id: \"emergency-impersonation\",\n        title: \"Impersonation of Emergency Services\",\n        content: \"4. Impersonation of Emergency Services - Impersonation of Police/EMS/Mechanics is forbidden. You may not use their character models.\"\n      },\n      {\n        id: \"character-development\",\n        title: \"Character Development\",\n        content: \"5. Character development is the observable changes the character makes as the narrative progresses. To track character development, the player should focus on the character traits and circumstances of the character. Examples of the character's circumstances are where they live, what they eat, their family structure, social life, appearance, and backstory, etc.\"\n      }\n    ]\n  },\n  {\n    id: \"police-hostage\",\n    name: \"Police as Hostage\",\n    rules: [\n      {\n        id: \"hostage-requirements\",\n        title: \"Police as Hostage Requirements\",\n        content: \"You can take on-duty police as a hostage only when there are more than 6 on-duty police officers which involves proper roleplay scenarios. You can't directly gunpoint any on-duty officer and take them hostage, and you must have a back-story to take police as a Hostage.\"\n      }\n    ]\n  },\n  {\n    id: \"pd-looting\",\n    name: \"PD Looting\",\n    rules: [\n      {\n        id: \"police-items\",\n        title: \"Police Items Restriction\",\n        content: \"1. You can't loot police items or guns (those which are not available publicly)\"\n      },\n      {\n        id: \"public-items\",\n        title: \"Public Items\",\n        content: \"2. You can loot ammo's and other items which are publicly available in city and the guns which are available in the city for civilians/gangs.\"\n      },\n      {\n        id: \"corrupt-cop\",\n        title: \"Corrupt Cop\",\n        content: \"3. Being a corrupt cop is not allowed, until staff/admin need to confirm.\"\n      },\n      {\n        id: \"corrupt-ems\",\n        title: \"Corrupt EMS/Mechanic\",\n        content: \"4. Being a corrupt EMS / Mechanic is not allowed.\"\n      }\n    ]\n  },\n  {\n    id: \"robbery-rules\",\n    name: \"Robbery Rules\",\n    rules: [\n      {\n        id: \"house-robbery\",\n        title: \"House Robbery\",\n        content: \"• Maximum 2-3 people allowed in house robbery\\n• You can take only 1 hostage in a house robbery. (Optional)\\n• You will get 2 demands from the police. (Note : If you have hostage)\\n• If you don't have hostage negotiate with police to chase or surrender and continue situation.\\n• Suspects can use 1 escape vehicles\\n• Code Red is strictly prohibited.\"\n      },\n      {\n        id: \"fleeca-bank-store\",\n        title: \"Fleeca Bank & Store Robbery\",\n        content: \"• Min 2-4people in one robbery situation are allowed.\\n• Only 1 escape vehicle is allowed.\\nDemands and Info:\\n• You can take only one person as a hostage.\\n• Friendly hostage is not allowed.\\n• Robbers can keep only 2 demands.\"\n      },\n      {\n        id: \"jewellery-robbery\",\n        title: \"Jewellery Robbery\",\n        content: \"• Maximum 4 people in one robbery situation are allowed.\\n• Only 1 escape vehicle is allowed.\\nDemands and Info:\\n• You can take only 1-2 person as a hostage.\\n• Friendly hostage is not allowed.\\n• Robbers can keep only 2 demands\"\n      },\n      {\n        id: \"pacific-bank\",\n        title: \"Pacific Bank Robbery\",\n        content: \"• Maximum 4-6 people in one robbery situation are allowed.\\n• Only 1 escape vehicle is allowed.\\nDemands and Info:\\n• You can take 1-2 persons as a hostage.\\n• Friendly hostage is not allowed.\\n• Robbers can keep only 2 demands. Negotiate with PD if you want extra 1 demand.(if you have 2 hostages you can have 3 demands)\"\n      }\n    ]\n  },\n  {\n    id: \"important-rules\",\n    name: \"Important Rules\",\n    rules: [\n      {\n        id: \"staff-respect\",\n        title: \"Disrespecting Staff/Support\",\n        content: \"Staff Members are only fulfilling their role here on TGRS when they make a decision. Do not make their life difficult by disrespecting them (Verbal Abuse / Threats), this is not permitted this will mute you for a Day on discord and 30 Community Service in game.\"\n      },\n      {\n        id: \"spamming\",\n        title: \"Spamming\",\n        content: \"Messaging Staff members through the use of our Discord Server multiple times will result in a delay in your request. Each member of Staff has their own reasons for not responding the first time so if you do not get a response, be patient and wait for your turn.\"\n      },\n      {\n        id: \"staff-abuse\",\n        title: \"Staff Abuse\",\n        content: \"If a Staff member is caught abusing his/her power with clear evidence and no clear reasoning, they will be dealt with depending on the severity of the issue.\"\n      },\n      {\n        id: \"staff-discretion\",\n        title: \"Staff Discretion\",\n        content: \"TGRS Staff reserve the right to restrict access to our services (game server and Discord) at their own discretion. This may include (but not limited to) players trying to cause issues, players not willing to resolve disputes, players who show a bad attitude towards rule violations etc.\"\n      }\n    ]\n  },\n  {\n    id: \"robbery-demands-vehicle\",\n    name: \"Robbery Demands and Vehicle Rules\",\n    rules: [\n      {\n        id: \"money-car-demands\",\n        title: \"Money and Car Demands\",\n        content: \"♦ You cannot demand Money for Hostage.\\n♦ You cannot demand Car for Hostage.\"\n      },\n      {\n        id: \"police-shooting\",\n        title: \"Police Shooting Rules\",\n        content: \"♦ If Police Fires First, then their Intention is to Burst the tires only, unless until Civilian Cod-Reds, Police Won't Shoot You.\"\n      },\n      {\n        id: \"vehicle-robbery\",\n        title: \"Vehicle Robbery Restrictions\",\n        content: \"♦ You may not rob the bank, store with a vehicle inside.\"\n      },\n      {\n        id: \"party-size\",\n        title: \"Party Size Announcement\",\n        content: \"♦ You must announce your party size at the beginning of a store/bank robbery to the police.\"\n      },\n      {\n        id: \"water-escape\",\n        title: \"Water Escape\",\n        content: \"♦ If you choose water to escape then there will be a code red situation. Police will do Code RED.\"\n      },\n      {\n        id: \"pd-guns-demand\",\n        title: \"PD Guns Demand\",\n        content: \"♦ You cannot demand PD Guns For Police hostage situations.\"\n      },\n      {\n        id: \"demands-acceptance\",\n        title: \"Demands Acceptance\",\n        content: \"♦ The number of demands and type of demands (of the robbers) will be accepted or declined according to the POLICE decision while a scripted robbery is in progress.\"\n      }\n    ]\n  },\n  {\n    id: \"police-chase\",\n    name: \"Police Chase Rules\",\n    rules: [\n      {\n        id: \"body-cam\",\n        title: \"Body Cam Requirement\",\n        content: \"• Always Body Cam ON when you are in duty/situation.\"\n      },\n      {\n        id: \"quick-repair\",\n        title: \"Quick Repair Restrictions\",\n        content: \"• Quick repair at a mechanic shop is great but when in police chase you are not allowed to do so as it is very unrealistic if your cars dying get out, leave it and find a new one.\"\n      },\n      {\n        id: \"interference\",\n        title: \"No Interference\",\n        content: \"• If you see someone who has been pulled over do not interfere with their scenario unless you are involved.\"\n      },\n      {\n        id: \"robbery-interference\",\n        title: \"Robbery Interference\",\n        content: \"• If you see a robbery taking place don't be a hero drive to a safe location and call the police.\"\n      },\n      {\n        id: \"chase-involvement\",\n        title: \"Chase Involvement\",\n        content: \"• If you see a police chase do not get involved you can give directions if asked by a police officer but do not try to ram the suspect, shoot and or join the pursuit if you want to.\"\n      },\n      {\n        id: \"pit-timing\",\n        title: \"PIT Timing\",\n        content: \"• You need to give 10-15min time to robbers on chase after the pit will start on higher officer command/call.\"\n      }\n    ]\n  },\n  {\n    id: \"emergency-services\",\n    name: \"Emergency Services\",\n    rules: [\n      {\n        id: \"city-restart\",\n        title: \"City Restart Warnings\",\n        content: \"• City restart warnings appear 30 minutes before they occur, This warning should signal a hold on Bank Robberies, Vangelico hits and convenience stores.\"\n      },\n      {\n        id: \"situation-activity\",\n        title: \"Situation Activity\",\n        content: \"• If you are in middle of the situation after situation everyone must be active in situation continue, if not it leads to ban.\"\n      }\n    ]\n  },\n  {\n    id: \"basic-rules\",\n    name: \"Basic Rules\",\n    rules: [\n      {\n        id: \"death-communication\",\n        title: \"Death Communication\",\n        content: \"People should not be allowed to speak after death.\"\n      },\n      {\n        id: \"cuffed-radio\",\n        title: \"Cuffed Radio\",\n        content: \"People should not be allowed to use the radio while cuffed.\"\n      },\n      {\n        id: \"cuffed-doors\",\n        title: \"Cuffed Door Access\",\n        content: \"People should not be allowed to open and close doors while cuffed.\"\n      },\n      {\n        id: \"respawn-return\",\n        title: \"Respawn Return\",\n        content: \"People should not be allowed to respawn after death and come back to the same situation.\"\n      },\n      {\n        id: \"combat-log\",\n        title: \"Combat Logging\",\n        content: \"People should not be allowed to 'quit' (Combat Log) after dying.\"\n      },\n      {\n        id: \"store-robbery\",\n        title: \"Store Robbery\",\n        content: \"Multiple people should not be allowed to be involved in a Store Robbery.\"\n      },\n      {\n        id: \"ems-transport\",\n        title: \"EMS Transport\",\n        content: \"People shouldn't be allowed to carry their friends to the Pillbox in the presence of EMS.\"\n      },\n      {\n        id: \"family-abuse\",\n        title: \"Family Abuse\",\n        content: \"People shouldn't be allowed to abuse someone's father/mother.\"\n      },\n      {\n        id: \"ooc-usage\",\n        title: \"OOC Usage\",\n        content: \"You cannot go OOC in situations unless or until for Bugs.\"\n      },\n      {\n        id: \"fail-rp\",\n        title: \"Fail RP\",\n        content: \"Don't break any kind of RP situation in between because it is considered as Fail RP.\"\n      },\n      {\n        id: \"new-life-rule\",\n        title: \"New Life Rule\",\n        content: \"New life rule should be followed after Respawn or Hospital treatment.\"\n      },\n      {\n        id: \"hostage-restrictions\",\n        title: \"Hostage Restrictions\",\n        content: \"You are not allowed to take a hostage from the following locations or situations: Local Job Areas (Taxi, Lumber, Garbage, Electrician, etc.), Safe Zone / Green Zone, New Players from job centers, If they are Unconscious or AFK.\"\n      },\n      {\n        id: \"support-contact\",\n        title: \"Support Contact\",\n        content: \"If you have any doubt regarding rules, then join support.\"\n      },\n      {\n        id: \"greenzones\",\n        title: \"Greenzones\",\n        content: \"All job locations, selling locations, shops, businesses, government establishments and its surroundings are considered as greenzones, you cannot commit any kind of crimes here.\"\n      },\n      {\n        id: \"robbery-requirements\",\n        title: \"Robbery Requirements\",\n        content: \"To rob a citizen you need 2 vehicles and 4 people minimum, you cannot rob in the city, you can only rob outside of city or in ghettos. Without proper roleplay you cannot rob anyone.\"\n      }\n    ]\n  },\n  {\n    id: \"prohibited-roleplay\",\n    name: \"Prohibited Role-play\",\n    rules: [\n      {\n        id: \"afk-farming\",\n        title: \"AFK Farming\",\n        content: \"1. AFK - AFK farming, abusing in-game bugs, etc. This will result in an immediate temp/permanent ban.\"\n      },\n      {\n        id: \"smuggling\",\n        title: \"Smuggling\",\n        content: \"2. Engaging in smuggling from PD, EMS, or other whitelist jobs and selling those items to the public can result in a bannable offense and character wipe.\"\n      },\n      {\n        id: \"new-life-rule-detail\",\n        title: \"New Life Rule\",\n        content: \"3. If you are downed and respawn at the hospital, your character forgets all events leading up to you being downed in the current scenario.\"\n      },\n      {\n        id: \"respawn-restrictions\",\n        title: \"Respawn Restrictions\",\n        content: \"4. You may not respawn if you have been advised that police or EMS is on the way to your scene.\"\n      },\n      {\n        id: \"active-situation\",\n        title: \"Active Situation\",\n        content: \"5. You may not respawn if you are in an active situation. (15-20mins)\"\n      },\n      {\n        id: \"ems-call\",\n        title: \"EMS Call\",\n        content: \"6. The only time you should be calling EMS/police while downed is when you're 100% alone and your injuries were not inflicted by someone else or any situation. Example: /911 LOCAL CALL\"\n      },\n      {\n        id: \"ban-appeal\",\n        title: \"Ban Appeal\",\n        content: \"7. BAN can be appealed and will be revived.\"\n      },\n      {\n        id: \"hunting-weapons\",\n        title: \"Hunting Weapons\",\n        content: \"8. Any weapons/tools that are bought from hunting job and found to be used anywhere else other than hunting will get you banned without hesitation.\"\n      }\n    ]\n  },\n  {\n    id: \"marketplace-policy\",\n    name: \"Marketplace Policy\",\n    rules: [\n      {\n        id: \"marketplace-zones\",\n        title: \"Marketplace Zones\",\n        content: \"At Legion Square, the marketplace is divided into two distinct zones.\"\n      },\n      {\n        id: \"legal-market-title\",\n        title: \"Legal Market\",\n        content: \"Legal Market:\"\n      },\n      {\n        id: \"legal-items\",\n        title: \"Legal Items Only\",\n        content: \"1. Only legal items are permitted for sale.\"\n      },\n      {\n        id: \"price-limit\",\n        title: \"Price Limit\",\n        content: \"2. The maximum allowed sale price is 30% above the item's base value.\"\n      },\n      {\n        id: \"price-penalties\",\n        title: \"Price Penalties\",\n        content: \"3. Any item exceeding this price limit will be subject to removal and penalties.\"\n      },\n      {\n        id: \"illegal-market-title\",\n        title: \"Illegal Market\",\n        content: \"Illegal Market:\"\n      },\n      {\n        id: \"illegal-items\",\n        title: \"Illegal Items Only\",\n        content: \"1. Only illegal items may be listed in this section.\"\n      },\n      {\n        id: \"dynamic-pricing\",\n        title: \"Dynamic Pricing\",\n        content: \"2. Item pricing is dynamic and may fluctuate based on demand and rarity.\"\n      },\n      {\n        id: \"responsible-selling\",\n        title: \"Responsible Selling\",\n        content: \"3. Sellers are expected to act responsibly and within the roleplay framework.\"\n      }\n    ]\n  }\n];\n\nexport default function Rules() {\n  const [selectedCategory, setSelectedCategory] = useState(\"server-rules\");\n  const [expandedRules, setExpandedRules] = useState<string[]>([]);\n  const [searchTerm, setSearchTerm] = useState(\"\");\n\n  const toggleRule = (ruleId: string) => {\n    setExpandedRules(prev =>\n      prev.includes(ruleId)\n        ? prev.filter(id => id !== ruleId)\n        : [...prev, ruleId]\n    );\n  };\n\n  const currentCategory = ruleCategories.find(cat => cat.id === selectedCategory);\n  const filteredRules = currentCategory?.rules.filter(rule =>\n    rule.title.toLowerCase().includes(searchTerm.toLowerCase()) ||\n    rule.content.toLowerCase().includes(searchTerm.toLowerCase())\n  ) || [];\n\n  return (\n    <>\n      <Navbar />\n\n      {/* Header */}\n      <div className=\"pt-24 pb-8 md:pt-32 md:pb-12 relative bg-black\">\n        <div className=\"absolute inset-0 pattern-circuit opacity-5\"></div>\n        <div className=\"container mx-auto px-4 md:px-6 relative z-10\">\n          <div className=\"mb-8 text-center\">\n            <h1 className=\"text-4xl md:text-5xl font-display font-bold text-white mb-6 wave-heading-bg\">\n              <span className=\"text-neon-orange\">SERVER</span> RULES\n            </h1>\n            <p className=\"text-gray-400 max-w-3xl mx-auto\">\n              These are the TGRS Community rules, essential for maintaining a fair and immersive roleplay experience.\n            </p>\n          </div>\n        </div>\n      </div>\n\n      {/* Section Divider */}\n      <div className=\"section-divider\"></div>\n\n      {/* Rules Section */}\n      <section className=\"py-20 relative bg-black\">\n        <div className=\"absolute inset-0 pattern-hexagon opacity-5\"></div>\n        <div className=\"container mx-auto px-4 md:px-6 relative z-10\">\n\n          {/* Quick Stats */}\n          <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4 mb-12\">\n            <div className=\"glass-enhanced rounded-xl p-4 text-center border border-neon-orange/30 hover:border-neon-orange/70 transition-all duration-300 group\">\n              <div className=\"text-2xl font-bold text-neon-orange mb-1 group-hover:text-white transition-colors\">\n                {ruleCategories.length}\n              </div>\n              <div className=\"text-xs text-gray-400 group-hover:text-gray-200 transition-colors\">Categories</div>\n            </div>\n            <div className=\"glass-enhanced rounded-xl p-4 text-center border border-neon-orange/30 hover:border-neon-orange/70 transition-all duration-300 group\">\n              <div className=\"text-2xl font-bold text-neon-orange mb-1 group-hover:text-white transition-colors\">\n                {ruleCategories.reduce((total, cat) => total + cat.rules.length, 0)}\n              </div>\n              <div className=\"text-xs text-gray-400 group-hover:text-gray-200 transition-colors\">Total Rules</div>\n            </div>\n            <div className=\"glass-enhanced rounded-xl p-4 text-center border border-neon-orange/30 hover:border-neon-orange/70 transition-all duration-300 group\">\n              <div className=\"text-2xl font-bold text-neon-orange mb-1 group-hover:text-white transition-colors\">24/7</div>\n              <div className=\"text-xs text-gray-400 group-hover:text-gray-200 transition-colors\">Enforcement</div>\n            </div>\n            <div className=\"glass-enhanced rounded-xl p-4 text-center border border-neon-orange/30 hover:border-neon-orange/70 transition-all duration-300 group\">\n              <div className=\"text-2xl font-bold text-neon-orange mb-1 group-hover:text-white transition-colors\">Fair</div>\n              <div className=\"text-xs text-gray-400 group-hover:text-gray-200 transition-colors\">Gameplay</div>\n            </div>\n          </div>\n\n          <div className=\"grid grid-cols-1 lg:grid-cols-4 gap-8\">\n            {/* Enhanced Sidebar */}\n            <div className=\"lg:col-span-1\">\n              {/* Search */}\n              <div className=\"mb-6\">\n                <div className=\"relative glass-enhanced rounded-xl border border-neon-orange/30 hover:border-neon-orange/70 transition-all duration-300 group\">\n                  <svg className=\"absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400 group-hover:text-neon-orange transition-colors\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\" />\n                  </svg>\n                  <input\n                    type=\"text\"\n                    placeholder=\"Search rules...\"\n                    value={searchTerm}\n                    onChange={(e) => setSearchTerm(e.target.value)}\n                    className=\"w-full bg-transparent border-0 rounded-xl pl-12 pr-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-neon-orange/50\"\n                  />\n                </div>\n              </div>\n\n              {/* Category Navigation */}\n              <div className=\"space-y-3\">\n                <h3 className=\"text-sm font-semibold text-neon-orange uppercase tracking-wider mb-6\">Rule Categories</h3>\n                {ruleCategories.map((category) => (\n                  <button\n                    key={category.id}\n                    onClick={() => {\n                      setSelectedCategory(category.id);\n                      setExpandedRules([]);\n                    }}\n                    className={`w-full text-left px-5 py-4 rounded-xl transition-all duration-300 group relative overflow-hidden transform hover:scale-[1.02] ${\n                      selectedCategory === category.id\n                        ? \"glass-enhanced border-2 border-neon-orange text-white font-bold bg-gradient-to-r from-neon-orange/15 to-neon-red/10 shadow-lg shadow-neon-orange/20\"\n                        : \"glass-enhanced border border-neon-orange/30 text-gray-300 hover:border-neon-orange/70 hover:text-white hover:bg-neon-orange/5\"\n                    }`}\n                  >\n                    <div className=\"flex items-center justify-between relative z-10\">\n                      <div className=\"flex items-center space-x-4\">\n                        <div className={`w-3 h-3 rounded-full transition-all duration-300 ${\n                          selectedCategory === category.id\n                            ? \"bg-neon-orange shadow-lg shadow-neon-orange/50 animate-pulse\"\n                            : \"bg-neon-orange/60 group-hover:bg-neon-orange group-hover:shadow-md group-hover:shadow-neon-orange/30\"\n                        }`}></div>\n                        <span className={`font-medium transition-all duration-300 ${\n                          selectedCategory === category.id ? \"text-base\" : \"text-sm group-hover:text-base\"\n                        }`}>\n                          {category.name}\n                        </span>\n                      </div>\n                      <span className={`text-xs px-3 py-1.5 rounded-full font-semibold transition-all duration-300 ${\n                        selectedCategory === category.id\n                          ? \"bg-neon-orange/30 text-neon-orange border-2 border-neon-orange/50 shadow-md\"\n                          : \"bg-neon-orange/15 text-neon-orange/80 border border-neon-orange/30 group-hover:bg-neon-orange/25 group-hover:text-neon-orange group-hover:border-neon-orange/50\"\n                      }`}>\n                        {category.rules.length}\n                      </span>\n                    </div>\n\n                    {/* Enhanced Background Effects */}\n                    {selectedCategory === category.id ? (\n                      <div className=\"absolute inset-0 bg-gradient-to-r from-neon-orange/10 via-neon-orange/5 to-neon-red/5 opacity-100\"></div>\n                    ) : (\n                      <div className=\"absolute inset-0 bg-gradient-to-r from-neon-orange/8 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"></div>\n                    )}\n\n                    {/* Active State Indicator */}\n                    {selectedCategory === category.id && (\n                      <div className=\"absolute left-0 top-1/2 transform -translate-y-1/2 w-1 h-8 bg-gradient-to-b from-neon-orange to-neon-red rounded-r-full\"></div>\n                    )}\n\n                    {/* Hover Glow Effect */}\n                    <div className={`absolute inset-0 rounded-xl transition-all duration-300 ${\n                      selectedCategory === category.id\n                        ? \"shadow-inner shadow-neon-orange/20\"\n                        : \"group-hover:shadow-lg group-hover:shadow-neon-orange/10\"\n                    }`}></div>\n                  </button>\n                ))}\n              </div>\n            </div>\n\n            {/* Enhanced Main Content */}\n            <div className=\"lg:col-span-3\">\n              <div className=\"glass-enhanced rounded-xl border border-neon-orange/30 overflow-hidden\">\n                {/* Category Header */}\n                <div className=\"relative p-6 bg-gradient-to-r from-neon-orange/10 to-neon-red/10 border-b border-neon-orange/30\">\n                  <div className=\"absolute inset-0 pattern-circuit opacity-10\"></div>\n                  <div className=\"relative z-10 flex items-center justify-between\">\n                    <div>\n                      <h2 className=\"text-2xl md:text-3xl font-display font-bold text-white mb-2\">\n                        {currentCategory?.name}\n                      </h2>\n                      <p className=\"text-neon-orange text-sm\">\n                        {filteredRules.length} rule{filteredRules.length !== 1 ? 's' : ''} in this category\n                      </p>\n                    </div>\n                    <div className=\"hidden md:flex items-center space-x-2\">\n                      <div className=\"w-3 h-3 bg-neon-orange rounded-full animate-pulse\"></div>\n                      <span className=\"text-xs text-gray-400 uppercase tracking-wider\">Active</span>\n                    </div>\n                  </div>\n                </div>\n\n                {/* Rules List */}\n                <div>\n                  {filteredRules.map((rule, index) => (\n                    <div key={rule.id} className=\"border-b border-neon-orange/10 last:border-b-0\">\n                      <button\n                        onClick={() => toggleRule(rule.id)}\n                        className=\"w-full px-6 py-5 text-left hover:bg-neon-orange/5 transition-all duration-300 flex items-center justify-between group relative overflow-hidden\"\n                      >\n                        <div className=\"flex items-center space-x-4 flex-1\">\n                          <div className=\"flex-shrink-0\">\n                            <div className=\"w-8 h-8 rounded-lg bg-gradient-to-br from-neon-orange/20 to-neon-red/20 border border-neon-orange/30 flex items-center justify-center group-hover:border-neon-orange/70 transition-colors\">\n                              <span className=\"text-xs font-bold text-neon-orange group-hover:text-white transition-colors\">\n                                {String(index + 1).padStart(2, '0')}\n                              </span>\n                            </div>\n                          </div>\n                          <div className=\"flex-1 min-w-0\">\n                            <h3 className=\"text-white font-semibold group-hover:text-neon-orange transition-colors text-left\">\n                              {rule.title}\n                            </h3>\n                            <p className=\"text-gray-400 text-sm mt-1 line-clamp-1\">\n                              {rule.content.substring(0, 80)}...\n                            </p>\n                          </div>\n                        </div>\n                        <div className=\"flex items-center space-x-3\">\n                          <div className={`px-3 py-1 rounded-full text-xs font-medium transition-colors ${\n                            expandedRules.includes(rule.id)\n                              ? \"bg-neon-orange/20 text-neon-orange border border-neon-orange/30\"\n                              : \"bg-gray-700/50 text-gray-400 border border-gray-600/30\"\n                          }`}>\n                            {expandedRules.includes(rule.id) ? \"Expanded\" : \"Collapsed\"}\n                          </div>\n                          <svg\n                            className={`w-5 h-5 text-gray-400 group-hover:text-neon-orange transition-all duration-300 ${\n                              expandedRules.includes(rule.id) ? \"rotate-180\" : \"\"\n                            }`}\n                            fill=\"none\"\n                            stroke=\"currentColor\"\n                            viewBox=\"0 0 24 24\"\n                          >\n                            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 9l-7 7-7-7\" />\n                          </svg>\n                        </div>\n                        <div className=\"absolute inset-0 bg-gradient-to-r from-neon-orange/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"></div>\n                      </button>\n\n                      {expandedRules.includes(rule.id) && (\n                        <div className=\"px-6 pb-6 bg-black/20\">\n                          <div className=\"ml-12 bg-gradient-to-r from-neon-orange/10 to-neon-red/5 rounded-xl p-6 border border-neon-orange/30 relative overflow-hidden\">\n                            <div className=\"absolute top-0 left-0 w-1 h-full bg-gradient-to-b from-neon-orange to-neon-red\"></div>\n                            <div className=\"relative z-10\">\n                              <div className=\"flex items-center space-x-2 mb-3\">\n                                <svg className=\"w-4 h-4 text-neon-orange\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                                  <path fillRule=\"evenodd\" d=\"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z\" clipRule=\"evenodd\" />\n                                </svg>\n                                <span className=\"text-neon-orange text-sm font-semibold uppercase tracking-wider\">Rule Details</span>\n                              </div>\n                              <p className=\"text-gray-200 leading-relaxed whitespace-pre-line\">\n                                {rule.content}\n                              </p>\n                            </div>\n                            <div className=\"absolute bottom-2 right-2 w-3 h-3 border-t-2 border-r-2 border-neon-orange/30\"></div>\n                          </div>\n                        </div>\n                      )}\n                    </div>\n                  ))}\n                </div>\n\n                {filteredRules.length === 0 && (\n                  <div className=\"p-12 text-center\">\n                    <div className=\"w-16 h-16 mx-auto mb-4 rounded-full bg-gradient-to-br from-neon-orange/20 to-neon-red/20 border border-neon-orange/30 flex items-center justify-center\">\n                      <svg className=\"w-8 h-8 text-neon-orange\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\" />\n                      </svg>\n                    </div>\n                    <h3 className=\"text-white font-semibold mb-2\">No Rules Found</h3>\n                    <p className=\"text-gray-400\">Try adjusting your search terms or select a different category.</p>\n                  </div>\n                )}\n              </div>\n\n              {/* Enhanced Footer Note */}\n              <div className=\"mt-8 glass-enhanced rounded-xl p-6 border border-neon-orange/30 relative overflow-hidden\">\n                <div className=\"absolute inset-0 pattern-dots opacity-5\"></div>\n                <div className=\"relative z-10\">\n                  <div className=\"flex items-start space-x-4\">\n                    <div className=\"flex-shrink-0\">\n                      <div className=\"w-12 h-12 rounded-xl bg-gradient-to-br from-neon-orange/20 to-neon-red/20 border border-neon-orange/30 flex items-center justify-center\">\n                        <svg className=\"w-6 h-6 text-neon-orange\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                          <path fillRule=\"evenodd\" d=\"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z\" clipRule=\"evenodd\" />\n                        </svg>\n                      </div>\n                    </div>\n                    <div className=\"flex-1\">\n                      <h3 className=\"text-white font-semibold mb-2 flex items-center\">\n                        <span>Important Notice</span>\n                        <div className=\"ml-2 w-2 h-2 bg-neon-orange rounded-full animate-pulse\"></div>\n                      </h3>\n                      <p className=\"text-gray-300 text-sm leading-relaxed mb-4\">\n                        By joining our server, you agree to follow these rules. The staff team reserves the right to modify these rules at any time and enforce them at their discretion.\n                      </p>\n                      <div className=\"flex flex-col sm:flex-row gap-3\">\n                        <a\n                          href=\"https://discord.gg/GAMravHDnB\"\n                          target=\"_blank\"\n                          rel=\"noopener noreferrer\"\n                          className=\"inline-flex items-center px-4 py-2 bg-gradient-to-r from-neon-orange to-neon-red text-black font-semibold rounded-lg hover:shadow-neon-strong transition-all duration-300 group\"\n                        >\n                          <svg className=\"w-4 h-4 mr-2 group-hover:scale-110 transition-transform\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                            <path d=\"M20.317 4.37a19.791 19.791 0 0 0-4.885-1.515.074.074 0 0 0-.079.037c-.21.375-.444.864-.608 1.25a18.27 18.27 0 0 0-5.487 0 12.64 12.64 0 0 0-.617-1.25.077.077 0 0 0-.079-.037A19.736 19.736 0 0 0 3.677 4.37a.07.07 0 0 0-.032.027C.533 9.046-.32 13.58.099 18.057a.082.082 0 0 0 .031.057 19.9 19.9 0 0 0 5.993 3.03.078.078 0 0 0 .084-.028c.462-.63.874-1.295 1.226-1.994a.076.076 0 0 0-.041-.106 13.107 13.107 0 0 1-1.872-.892.077.077 0 0 1-.008-.128 10.2 10.2 0 0 0 .372-.292.074.074 0 0 1 .077-.01c3.928 1.793 8.18 1.793 12.062 0a.074.074 0 0 1 .078.01c.12.098.246.198.373.292a.077.077 0 0 1-.006.127 12.299 12.299 0 0 1-1.873.892.077.077 0 0 0-.041.107c.36.698.772 1.362 1.225 1.993a.076.076 0 0 0 .084.028 19.839 19.839 0 0 0 6.002-3.03.077.077 0 0 0 .032-.054c.5-5.177-.838-9.674-3.549-13.66a.061.061 0 0 0-.031-.03z\"/>\n                          </svg>\n                          Join Discord Support\n                        </a>\n                        <div className=\"inline-flex items-center px-4 py-2 bg-black/30 border border-neon-orange/30 text-gray-300 rounded-lg\">\n                          <svg className=\"w-4 h-4 mr-2 text-neon-orange\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                          </svg>\n                          <span className=\"text-sm\">24/7 Enforcement</span>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n                <div className=\"absolute top-2 right-2 w-3 h-3 border-t-2 border-r-2 border-neon-orange/30\"></div>\n                <div className=\"absolute bottom-2 left-2 w-3 h-3 border-b-2 border-l-2 border-neon-orange/30\"></div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      <Footer />\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAMA,MAAM,iBAAiB;IACrB;QACE,IAAI;QACJ,MAAM;QACN,OAAO;YACL;gBACE,IAAI;gBACJ,OAAO;gBACP,SAAS;YACX;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,SAAS;YACX;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,SAAS;YACX;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,SAAS;YACX;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,SAAS;YACX;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,SAAS;YACX;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,SAAS;YACX;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,SAAS;YACX;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,SAAS;YACX;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,SAAS;YACX;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,SAAS;YACX;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,SAAS;YACX;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,SAAS;YACX;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,SAAS;YACX;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,SAAS;YACX;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,SAAS;YACX;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,SAAS;YACX;SACD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;YACL;gBACE,IAAI;gBACJ,OAAO;gBACP,SAAS;YACX;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,SAAS;YACX;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,SAAS;YACX;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,SAAS;YACX;SACD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;YACL;gBACE,IAAI;gBACJ,OAAO;gBACP,SAAS;YACX;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,SAAS;YACX;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,SAAS;YACX;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,SAAS;YACX;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,SAAS;YACX;SACD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;YACL;gBACE,IAAI;gBACJ,OAAO;gBACP,SAAS;YACX;SACD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;YACL;gBACE,IAAI;gBACJ,OAAO;gBACP,SAAS;YACX;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,SAAS;YACX;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,SAAS;YACX;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,SAAS;YACX;SACD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;YACL;gBACE,IAAI;gBACJ,OAAO;gBACP,SAAS;YACX;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,SAAS;YACX;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,SAAS;YACX;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,SAAS;YACX;SACD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;YACL;gBACE,IAAI;gBACJ,OAAO;gBACP,SAAS;YACX;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,SAAS;YACX;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,SAAS;YACX;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,SAAS;YACX;SACD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;YACL;gBACE,IAAI;gBACJ,OAAO;gBACP,SAAS;YACX;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,SAAS;YACX;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,SAAS;YACX;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,SAAS;YACX;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,SAAS;YACX;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,SAAS;YACX;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,SAAS;YACX;SACD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;YACL;gBACE,IAAI;gBACJ,OAAO;gBACP,SAAS;YACX;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,SAAS;YACX;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,SAAS;YACX;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,SAAS;YACX;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,SAAS;YACX;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,SAAS;YACX;SACD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;YACL;gBACE,IAAI;gBACJ,OAAO;gBACP,SAAS;YACX;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,SAAS;YACX;SACD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;YACL;gBACE,IAAI;gBACJ,OAAO;gBACP,SAAS;YACX;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,SAAS;YACX;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,SAAS;YACX;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,SAAS;YACX;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,SAAS;YACX;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,SAAS;YACX;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,SAAS;YACX;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,SAAS;YACX;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,SAAS;YACX;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,SAAS;YACX;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,SAAS;YACX;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,SAAS;YACX;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,SAAS;YACX;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,SAAS;YACX;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,SAAS;YACX;SACD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;YACL;gBACE,IAAI;gBACJ,OAAO;gBACP,SAAS;YACX;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,SAAS;YACX;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,SAAS;YACX;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,SAAS;YACX;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,SAAS;YACX;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,SAAS;YACX;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,SAAS;YACX;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,SAAS;YACX;SACD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;YACL;gBACE,IAAI;gBACJ,OAAO;gBACP,SAAS;YACX;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,SAAS;YACX;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,SAAS;YACX;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,SAAS;YACX;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,SAAS;YACX;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,SAAS;YACX;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,SAAS;YACX;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,SAAS;YACX;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,SAAS;YACX;SACD;IACH;CACD;AAEc,SAAS;;IACtB,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAC/D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,aAAa,CAAC;QAClB,iBAAiB,CAAA,OACf,KAAK,QAAQ,CAAC,UACV,KAAK,MAAM,CAAC,CAAA,KAAM,OAAO,UACzB;mBAAI;gBAAM;aAAO;IAEzB;IAEA,MAAM,kBAAkB,eAAe,IAAI,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK;IAC9D,MAAM,gBAAgB,iBAAiB,MAAM,OAAO,CAAA,OAClD,KAAK,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACxD,KAAK,OAAO,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,QACvD,EAAE;IAEP,qBACE;;0BACE,6LAAC,+HAAA,CAAA,UAAM;;;;;0BAGP,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC;4CAAK,WAAU;sDAAmB;;;;;;wCAAa;;;;;;;8CAElD,6LAAC;oCAAE,WAAU;8CAAkC;;;;;;;;;;;;;;;;;;;;;;;0BAQrD,6LAAC;gBAAI,WAAU;;;;;;0BAGf,6LAAC;gBAAQ,WAAU;;kCACjB,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;0CAGb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACZ,eAAe,MAAM;;;;;;0DAExB,6LAAC;gDAAI,WAAU;0DAAoE;;;;;;;;;;;;kDAErF,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACZ,eAAe,MAAM,CAAC,CAAC,OAAO,MAAQ,QAAQ,IAAI,KAAK,CAAC,MAAM,EAAE;;;;;;0DAEnE,6LAAC;gDAAI,WAAU;0DAAoE;;;;;;;;;;;;kDAErF,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DAAoF;;;;;;0DACnG,6LAAC;gDAAI,WAAU;0DAAoE;;;;;;;;;;;;kDAErF,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DAAoF;;;;;;0DACnG,6LAAC;gDAAI,WAAU;0DAAoE;;;;;;;;;;;;;;;;;;0CAIvF,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;wCAAI,WAAU;;0DAEb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;4DAA0H,MAAK;4DAAO,QAAO;4DAAe,SAAQ;sEACjL,cAAA,6LAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAa;gEAAG,GAAE;;;;;;;;;;;sEAEvE,6LAAC;4DACC,MAAK;4DACL,aAAY;4DACZ,OAAO;4DACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;4DAC7C,WAAU;;;;;;;;;;;;;;;;;0DAMhB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAAuE;;;;;;oDACpF,eAAe,GAAG,CAAC,CAAC,yBACnB,6LAAC;4DAEC,SAAS;gEACP,oBAAoB,SAAS,EAAE;gEAC/B,iBAAiB,EAAE;4DACrB;4DACA,WAAW,CAAC,8HAA8H,EACxI,qBAAqB,SAAS,EAAE,GAC5B,wJACA,iIACJ;;8EAEF,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAI,WAAU;;8FACb,6LAAC;oFAAI,WAAW,CAAC,iDAAiD,EAChE,qBAAqB,SAAS,EAAE,GAC5B,iEACA,wGACJ;;;;;;8FACF,6LAAC;oFAAK,WAAW,CAAC,wCAAwC,EACxD,qBAAqB,SAAS,EAAE,GAAG,cAAc,iCACjD;8FACC,SAAS,IAAI;;;;;;;;;;;;sFAGlB,6LAAC;4EAAK,WAAW,CAAC,2EAA2E,EAC3F,qBAAqB,SAAS,EAAE,GAC5B,gFACA,mKACJ;sFACC,SAAS,KAAK,CAAC,MAAM;;;;;;;;;;;;gEAKzB,qBAAqB,SAAS,EAAE,iBAC/B,6LAAC;oEAAI,WAAU;;;;;yFAEf,6LAAC;oEAAI,WAAU;;;;;;gEAIhB,qBAAqB,SAAS,EAAE,kBAC/B,6LAAC;oEAAI,WAAU;;;;;;8EAIjB,6LAAC;oEAAI,WAAW,CAAC,wDAAwD,EACvE,qBAAqB,SAAS,EAAE,GAC5B,uCACA,2DACJ;;;;;;;2DAlDG,SAAS,EAAE;;;;;;;;;;;;;;;;;kDAyDxB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEAEb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;;;;;0EACf,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;;0FACC,6LAAC;gFAAG,WAAU;0FACX,iBAAiB;;;;;;0FAEpB,6LAAC;gFAAE,WAAU;;oFACV,cAAc,MAAM;oFAAC;oFAAM,cAAc,MAAM,KAAK,IAAI,MAAM;oFAAG;;;;;;;;;;;;;kFAGtE,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAI,WAAU;;;;;;0FACf,6LAAC;gFAAK,WAAU;0FAAiD;;;;;;;;;;;;;;;;;;;;;;;;kEAMvE,6LAAC;kEACE,cAAc,GAAG,CAAC,CAAC,MAAM,sBACxB,6LAAC;gEAAkB,WAAU;;kFAC3B,6LAAC;wEACC,SAAS,IAAM,WAAW,KAAK,EAAE;wEACjC,WAAU;;0FAEV,6LAAC;gFAAI,WAAU;;kGACb,6LAAC;wFAAI,WAAU;kGACb,cAAA,6LAAC;4FAAI,WAAU;sGACb,cAAA,6LAAC;gGAAK,WAAU;0GACb,OAAO,QAAQ,GAAG,QAAQ,CAAC,GAAG;;;;;;;;;;;;;;;;kGAIrC,6LAAC;wFAAI,WAAU;;0GACb,6LAAC;gGAAG,WAAU;0GACX,KAAK,KAAK;;;;;;0GAEb,6LAAC;gGAAE,WAAU;;oGACV,KAAK,OAAO,CAAC,SAAS,CAAC,GAAG;oGAAI;;;;;;;;;;;;;;;;;;;0FAIrC,6LAAC;gFAAI,WAAU;;kGACb,6LAAC;wFAAI,WAAW,CAAC,6DAA6D,EAC5E,cAAc,QAAQ,CAAC,KAAK,EAAE,IAC1B,oEACA,0DACJ;kGACC,cAAc,QAAQ,CAAC,KAAK,EAAE,IAAI,aAAa;;;;;;kGAElD,6LAAC;wFACC,WAAW,CAAC,+EAA+E,EACzF,cAAc,QAAQ,CAAC,KAAK,EAAE,IAAI,eAAe,IACjD;wFACF,MAAK;wFACL,QAAO;wFACP,SAAQ;kGAER,cAAA,6LAAC;4FAAK,eAAc;4FAAQ,gBAAe;4FAAQ,aAAa;4FAAG,GAAE;;;;;;;;;;;;;;;;;0FAGzE,6LAAC;gFAAI,WAAU;;;;;;;;;;;;oEAGhB,cAAc,QAAQ,CAAC,KAAK,EAAE,mBAC7B,6LAAC;wEAAI,WAAU;kFACb,cAAA,6LAAC;4EAAI,WAAU;;8FACb,6LAAC;oFAAI,WAAU;;;;;;8FACf,6LAAC;oFAAI,WAAU;;sGACb,6LAAC;4FAAI,WAAU;;8GACb,6LAAC;oGAAI,WAAU;oGAA2B,MAAK;oGAAe,SAAQ;8GACpE,cAAA,6LAAC;wGAAK,UAAS;wGAAU,GAAE;wGAAmI,UAAS;;;;;;;;;;;8GAEzK,6LAAC;oGAAK,WAAU;8GAAkE;;;;;;;;;;;;sGAEpF,6LAAC;4FAAE,WAAU;sGACV,KAAK,OAAO;;;;;;;;;;;;8FAGjB,6LAAC;oFAAI,WAAU;;;;;;;;;;;;;;;;;;+DA3Db,KAAK,EAAE;;;;;;;;;;oDAmEpB,cAAc,MAAM,KAAK,mBACxB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC;oEAAI,WAAU;oEAA2B,MAAK;oEAAO,QAAO;oEAAe,SAAQ;8EAClF,cAAA,6LAAC;wEAAK,eAAc;wEAAQ,gBAAe;wEAAQ,aAAa;wEAAG,GAAE;;;;;;;;;;;;;;;;0EAGzE,6LAAC;gEAAG,WAAU;0EAAgC;;;;;;0EAC9C,6LAAC;gEAAE,WAAU;0EAAgB;;;;;;;;;;;;;;;;;;0DAMnC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;;;;;kEACf,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EACb,cAAA,6LAAC;wEAAI,WAAU;kFACb,cAAA,6LAAC;4EAAI,WAAU;4EAA2B,MAAK;4EAAe,SAAQ;sFACpE,cAAA,6LAAC;gFAAK,UAAS;gFAAU,GAAE;gFAAmI,UAAS;;;;;;;;;;;;;;;;;;;;;8EAI7K,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAG,WAAU;;8FACZ,6LAAC;8FAAK;;;;;;8FACN,6LAAC;oFAAI,WAAU;;;;;;;;;;;;sFAEjB,6LAAC;4EAAE,WAAU;sFAA6C;;;;;;sFAG1D,6LAAC;4EAAI,WAAU;;8FACb,6LAAC;oFACC,MAAK;oFACL,QAAO;oFACP,KAAI;oFACJ,WAAU;;sGAEV,6LAAC;4FAAI,WAAU;4FAA0D,MAAK;4FAAe,SAAQ;sGACnG,cAAA,6LAAC;gGAAK,GAAE;;;;;;;;;;;wFACJ;;;;;;;8FAGR,6LAAC;oFAAI,WAAU;;sGACb,6LAAC;4FAAI,WAAU;4FAAgC,MAAK;4FAAO,QAAO;4FAAe,SAAQ;sGACvF,cAAA,6LAAC;gGAAK,eAAc;gGAAQ,gBAAe;gGAAQ,aAAa;gGAAG,GAAE;;;;;;;;;;;sGAEvE,6LAAC;4FAAK,WAAU;sGAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kEAMpC,6LAAC;wDAAI,WAAU;;;;;;kEACf,6LAAC;wDAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOzB,6LAAC,+HAAA,CAAA,UAAM;;;;;;;AAGb;GApTwB;KAAA"}}, {"offset": {"line": 3037, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}