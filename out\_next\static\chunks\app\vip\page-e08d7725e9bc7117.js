(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[782],{5634:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,8173,23)),Promise.resolve().then(r.t.bind(r,7970,23)),Promise.resolve().then(r.bind(r,3083)),Promise.resolve().then(r.bind(r,7282)),Promise.resolve().then(r.bind(r,8136))},3083:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});var a=r(5155),o=r(2115);let s=()=>{let e=(0,o.useRef)(null),t=(0,o.useRef)(void 0),r=(0,o.useRef)([]),s=(0,o.useRef)({x:0,y:0});return(0,o.useEffect)(()=>{let a=e.current;if(!a)return;let o=a.getContext("2d");if(!o)return;let l=()=>{a.width=a.offsetWidth,a.height=a.offsetHeight};l(),window.addEventListener("resize",l);let n=e=>{let t=a.getBoundingClientRect();s.current={x:e.clientX-t.left,y:e.clientY-t.top}};a.addEventListener("mousemove",n);let d=["#10B981","#3B82F6","#F59E0B","#EF4444","#8B5CF6","#F97316","#06B6D4"],i=["dollar","euro","rupee","bitcoin"],c=()=>{let e,t,r,o;let s=Math.random();return s<.3?(e=-50,t=Math.random()*a.height,r=3*Math.random()+1,o=(Math.random()-.5)*2):s<.6?(e=Math.random()*a.width,t=-50,r=(Math.random()-.5)*2,o=3*Math.random()+1):(e=a.width+50,t=Math.random()*a.height,r=-(3*Math.random()+1),o=(Math.random()-.5)*2),{x:e,y:t,vx:r,vy:o,rotation:Math.random()*Math.PI*2,rotationSpeed:(Math.random()-.5)*.05,size:25*Math.random()+20,opacity:.9*Math.random()+.4,type:i[Math.floor(Math.random()*i.length)],color:d[Math.floor(Math.random()*d.length)],life:0,maxLife:400*Math.random()+300,swayOffset:Math.random()*Math.PI*2,swaySpeed:.02*Math.random()+.01}};(()=>{r.current=[];for(let e=0;e<50;e++)r.current.push(c())})();let h=(e,t)=>{e.save(),e.translate(t.x,t.y),e.rotate(t.rotation),e.globalAlpha=t.opacity*(1-t.life/t.maxLife);let r=1.8*t.size,a=.8*t.size,o=e.createLinearGradient(-r/2,-a/2,r/2,a/2);o.addColorStop(0,t.color),o.addColorStop(.5,"#ffffff40"),o.addColorStop(1,t.color),e.fillStyle=o,e.strokeStyle=t.color,e.lineWidth=2,e.beginPath(),e.roundRect(-r/2,-a/2,r,a,4),e.fill(),e.stroke(),e.fillStyle="#ffffff",e.font="bold ".concat(.6*t.size,"px Arial"),e.textAlign="center",e.textBaseline="middle";let s="$";switch(t.type){case"euro":s="€";break;case"rupee":s="₹";break;case"bitcoin":s="₿";break;default:s="$"}e.fillText(s,0,0);let l=e.createLinearGradient(-r/2,-a/2,r/2,a/2);l.addColorStop(0,"rgba(255,255,255,0)"),l.addColorStop(.5,"rgba(255,255,255,0.3)"),l.addColorStop(1,"rgba(255,255,255,0)"),e.fillStyle=l,e.beginPath(),e.roundRect(-r/2,-a/2,r,a,4),e.fill(),e.restore()},x=()=>{o.clearRect(0,0,a.width,a.height);let e=r.current;for(let t=e.length-1;t>=0;t--){let r=e[t],l=s.current.x-r.x,n=s.current.y-r.y,d=Math.sqrt(l*l+n*n);if(d<150){let e=(150-d)/150;r.vx+=l/d*e*.02,r.vy+=n/d*e*.02}r.swayOffset+=r.swaySpeed;let i=.5*Math.sin(r.swayOffset),c=.3*Math.cos(.7*r.swayOffset);r.x+=r.vx+i,r.y+=r.vy+c,r.rotation+=r.rotationSpeed,r.life++,r.vx*=.995,r.vy+=.01,r.vy*=.995;let x=Math.sqrt(r.vx*r.vx+r.vy*r.vy);x>4&&(r.vx=r.vx/x*4,r.vy=r.vy/x*4),r.x>a.width+100||r.y>a.height+100||r.x<-100||r.y<-100||r.life>r.maxLife?e.splice(t,1):h(o,r)}e.length<80&&.6>Math.random()&&e.push(c()),o.strokeStyle="rgba(16, 185, 129, 0.2)",o.lineWidth=1.5;for(let t=0;t<e.length;t++)for(let r=t+1;r<e.length;r++){let a=e[t].x-e[r].x,s=e[t].y-e[r].y,l=Math.sqrt(a*a+s*s);if(l<120){let a=(120-l)/120*.25;o.globalAlpha=a,o.beginPath(),o.moveTo(e[t].x,e[t].y),o.lineTo(e[r].x,e[r].y),o.stroke(),o.globalAlpha=1}}t.current=requestAnimationFrame(x)};return x(),()=>{window.removeEventListener("resize",l),a.removeEventListener("mousemove",n),t.current&&cancelAnimationFrame(t.current)}},[]),(0,a.jsx)("canvas",{ref:e,className:"absolute inset-0 w-full h-full pointer-events-none",style:{zIndex:1}})}},8136:(e,t,r)=>{"use strict";r.d(t,{default:()=>l});var a=r(5155),o=r(2115),s=r(5565);let l=e=>{let{tier:t,price:r,title:l,description:n,icon:d,mainFeatures:i,timeFeatures:c,clothing:h,peds:x,isPopular:g=!1,className:b="",currency:m="₹"}=e,[f,u]=(0,o.useState)("features"),[p,v]=(0,o.useState)(!1),y="border-orange-500",w="text-orange-400",j="bg-orange-500",N="from-orange-500/30 to-yellow-500/30",M="shadow-orange-500/30",k="fas fa-star",C=!1;return 1===t?(y="border-red-800",w="text-red-300",j="bg-red-800",N="from-red-800/30 to-red-700/30",M="shadow-red-800/30",k="fas fa-medal"):2===t?(y="border-gray-400",w="text-gray-300",j="bg-gray-400",N="from-gray-400/30 to-gray-300/30",M="shadow-gray-400/30",k="fas fa-award"):3===t?(y="border-amber-600",w="text-amber-400",j="bg-amber-600",N="from-amber-600/30 to-amber-500/30",M="shadow-amber-600/30",k="fas fa-trophy"):4===t?(y="border-violet-500",w="text-violet-400",j="bg-violet-500",N="from-violet-500/30 to-violet-400/30",M="shadow-violet-500/30",k="fas fa-gem",C=!0):5===t&&(y="border-yellow-400",w="text-yellow-300",j="bg-yellow-400",N="from-yellow-400/30 to-yellow-300/30",M="shadow-yellow-400/30",k="fas fa-crown",C=!0),(0,a.jsxs)("div",{className:"relative ".concat(b," group"),children:[g&&(0,a.jsx)("div",{className:"absolute -top-2 right-4 z-30",children:(0,a.jsx)("div",{className:"bg-gradient-to-r from-yellow-400 to-orange-500 px-3 py-1 rounded-md text-black text-xs font-bold shadow-lg",children:"POPULAR"})}),(0,a.jsx)("div",{className:"absolute inset-0 rounded-3xl bg-gradient-to-r ".concat(N," opacity-20 blur-xl pointer-events-none")}),C&&(0,a.jsx)("div",{className:"absolute inset-0 rounded-3xl bg-gradient-to-r ".concat(N," opacity-40 blur-2xl pointer-events-none animate-pulse")}),(0,a.jsxs)("div",{className:"bg-black/95 backdrop-blur-sm rounded-3xl border-2 ".concat(y," hover:border-opacity-100 transition-all duration-500 overflow-hidden relative h-full flex flex-col shadow-xl ").concat(M," group-hover:shadow-2xl group-hover:").concat(M.replace("/30","/50")),children:[(0,a.jsxs)("div",{className:"p-8 border-b border-gray-700/30 bg-gradient-to-r from-gray-900/50 to-black/50 relative overflow-hidden",children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent -skew-x-12 translate-x-[-100%] group-hover:translate-x-[200%] transition-transform duration-1000 ease-out"}),(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("div",{className:"w-16 h-16 rounded-2xl bg-gradient-to-br ".concat(N," border-2 ").concat(y," flex items-center justify-center ").concat(C?"animate-pulse shadow-lg shadow-current/50":"shadow-lg"),children:(0,a.jsx)("i",{className:"".concat(k," ").concat(w," text-2xl")})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-3xl font-bold text-white mb-1",children:l||"VIP ".concat(t)}),(0,a.jsx)("p",{className:"text-gray-300 text-lg font-medium",children:n})]})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsxs)("div",{className:"text-5xl font-bold text-white mb-1",children:[m,r.toLocaleString()]}),(0,a.jsx)("div",{className:"text-gray-400 text-base font-medium",children:"One-time purchase"})]})]})]}),(0,a.jsx)("div",{className:"px-8 py-4 border-b border-gray-700/30 bg-gray-900/30",children:(0,a.jsxs)("div",{className:"flex space-x-3",children:[(0,a.jsx)("button",{onClick:()=>u("features"),className:"flex-1 px-4 py-3 rounded-xl text-sm font-bold transition-all duration-300 text-center whitespace-nowrap ".concat("features"===f?"".concat(w," bg-gray-800/90 shadow-lg border-2 border-current/40"):"text-gray-400 hover:text-white hover:bg-gray-800/50 border-2 border-transparent"),children:"Features"}),x.length>0&&(0,a.jsxs)("button",{onClick:()=>u("peds"),className:"flex-1 px-4 py-3 rounded-xl text-sm font-bold transition-all duration-300 text-center whitespace-nowrap ".concat("peds"===f?"".concat(w," bg-gray-800/90 shadow-lg border-2 border-current/40"):"text-gray-400 hover:text-white hover:bg-gray-800/50 border-2 border-transparent"),children:["Peds (",x.length,")"]}),h.length>0&&(0,a.jsx)("button",{onClick:()=>u("clothing"),className:"flex-1 px-4 py-3 rounded-xl text-sm font-bold transition-all duration-300 text-center whitespace-nowrap ".concat("clothing"===f?"".concat(w," bg-gray-800/90 shadow-lg border-2 border-current/40"):"text-gray-400 hover:text-white hover:bg-gray-800/50 border-2 border-transparent"),children:"Clothing"})]})}),(0,a.jsxs)("div",{className:"p-8 flex-grow",children:["features"===f&&(0,a.jsxs)("div",{className:"space-y-5",children:[i.map((e,t)=>(0,a.jsxs)("div",{className:"flex items-start space-x-4 p-5 rounded-2xl bg-gray-800/40 border border-gray-700/40 hover:bg-gray-800/60 hover:border-gray-600/60 transition-all duration-300 shadow-lg",children:[(0,a.jsx)("div",{className:"w-3 h-3 rounded-full ".concat(j," mt-3 flex-shrink-0 shadow-lg")}),(0,a.jsxs)("div",{className:"flex-grow",children:[(0,a.jsx)("div",{className:"text-white font-bold text-lg mb-3",children:e.text}),e.details&&(0,a.jsx)("div",{className:"text-gray-300 text-base mb-4",children:e.details}),(0,a.jsxs)("div",{className:"flex flex-wrap gap-3",children:[e.highlight&&(0,a.jsx)("span",{className:"".concat(w," text-base font-bold bg-gray-800/80 px-4 py-2 rounded-xl border-2 ").concat(y," shadow-lg"),children:e.highlight}),e.example&&(0,a.jsx)("span",{className:"text-gray-200 text-base font-mono bg-gray-900/80 px-4 py-2 rounded-xl border border-gray-600 shadow-lg",children:e.example})]})]})]},t)),c.length>0&&(0,a.jsxs)("div",{className:"mt-8 p-6 bg-gray-800/30 rounded-2xl border border-gray-700/40 shadow-lg",children:[(0,a.jsx)("h5",{className:"text-white font-bold mb-5 text-xl",children:"Time-Limited Benefits"}),(0,a.jsx)("div",{className:"space-y-4",children:c.map((e,t)=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 bg-gray-800/50 rounded-xl border border-gray-700/50 hover:bg-gray-800/70 transition-all duration-300",children:[(0,a.jsx)("span",{className:"text-white text-lg font-semibold",children:e.text}),(0,a.jsx)("span",{className:"".concat(w," text-base font-bold px-4 py-2 rounded-xl bg-gray-800/80 border-2 ").concat(y," shadow-lg"),children:e.duration})]},t))})]})]}),"peds"===f&&x.length>0&&(0,a.jsxs)("div",{children:[(0,a.jsxs)("h5",{className:"text-white font-semibold mb-6 text-lg",children:["Character Models (",x.length,")"]}),(0,a.jsx)("div",{className:"grid grid-cols-3 gap-4",children:x.map((e,t)=>(0,a.jsx)("div",{className:"group relative",children:(0,a.jsxs)("div",{className:"aspect-square rounded-xl overflow-hidden bg-gray-800/50 border border-gray-700/50 group-hover:border-gray-600 transition-all duration-300 shadow-lg",children:[(0,a.jsx)(s.default,{src:e.image,alt:e.name,width:120,height:120,className:"w-full h-full object-cover group-hover:scale-110 transition-transform duration-300"}),(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-t from-black/80 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"}),(0,a.jsx)("div",{className:"absolute bottom-2 left-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300",children:(0,a.jsx)("div",{className:"text-white text-sm font-semibold truncate text-center bg-black/90 rounded-lg px-3 py-2",children:e.name})})]})},t))})]}),"clothing"===f&&h.length>0&&(0,a.jsxs)("div",{children:[(0,a.jsx)("h5",{className:"text-white font-semibold mb-4 text-base",children:"Exclusive Clothing"}),(0,a.jsx)("div",{className:"space-y-4",children:h.map((e,t)=>(0,a.jsxs)("div",{className:"flex items-center space-x-4 p-4 bg-gray-800/30 rounded-lg border border-gray-700/30 hover:bg-gray-800/50 transition-colors",children:[(0,a.jsx)("div",{className:"w-10 h-10 rounded-lg bg-orange-500/20 flex items-center justify-center",children:(0,a.jsx)("i",{className:"fas fa-tshirt text-orange-400 text-base"})}),(0,a.jsxs)("div",{className:"flex-grow",children:[(0,a.jsx)("h6",{className:"text-white font-semibold text-base mb-1",children:e.name}),(0,a.jsx)("p",{className:"text-gray-400 text-sm mb-2",children:e.description}),e.variants>0&&(0,a.jsxs)("span",{className:"text-orange-400 text-sm font-semibold bg-orange-500/10 px-3 py-1 rounded border border-orange-500/20",children:[e.variants," variants"]})]})]},t))})]})]}),(0,a.jsx)("div",{className:"p-6 border-t border-gray-700/30 bg-gray-900/20 flex justify-center",children:(0,a.jsxs)("button",{className:"relative overflow-hidden px-6 py-2 rounded-lg font-bold text-base transition-all duration-300 ".concat(C?"bg-gradient-to-r ".concat(N," ").concat(w," border-2 ").concat(y," hover:shadow-lg hover:shadow-current/40"):"bg-gray-800/80 text-white border-2 border-gray-600/50 hover:bg-gray-700/80 hover:border-gray-500/70"," hover:scale-110 flex items-center justify-center gap-2 group shadow-md"),onMouseEnter:()=>{(4===t||5===t)&&v(!0)},onMouseLeave:()=>{(4===t||5===t)&&v(!1)},onClick:()=>window.open("https://discord.gg/GAMravHDnB","_blank"),children:[(0,a.jsx)("span",{className:"text-base",children:"\uD83C\uDFAB"}),(0,a.jsx)("span",{children:"Grab Now"}),4===t&&p&&(0,a.jsx)("div",{className:"absolute inset-0 bg-violet-500/30 rounded-lg animate-pulse pointer-events-none"}),5===t&&p&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-yellow-400/30 rounded-lg animate-pulse pointer-events-none"}),(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-transparent via-yellow-300/50 to-transparent -skew-x-12 translate-x-[-100%] animate-shimmer pointer-events-none"})]})]})})]})]})}}},e=>{var t=t=>e(e.s=t);e.O(0,[992,282,441,517,358],()=>t(5634)),_N_E=e.O()}]);