(()=>{var e={};e.id=974,e.ids=[974],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},9551:e=>{"use strict";e.exports=require("url")},2022:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>m,routeModule:()=>c,tree:()=>d});var a=s(260),r=s(8203),l=s(5155),i=s.n(l),n=s(7292),o={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);s.d(t,o);let d=["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,319)),"C:\\Users\\<USER>\\Desktop\\TGRS Website\\tgrs-website\\src\\app\\page.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,1354)),"C:\\Users\\<USER>\\Desktop\\TGRS Website\\tgrs-website\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,9937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,9116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,1485,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],m=["C:\\Users\\<USER>\\Desktop\\TGRS Website\\tgrs-website\\src\\app\\page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},c=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},1437:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,9607,23)),Promise.resolve().then(s.t.bind(s,1066,23)),Promise.resolve().then(s.bind(s,1750)),Promise.resolve().then(s.bind(s,1443))},5405:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,8531,23)),Promise.resolve().then(s.t.bind(s,1902,23)),Promise.resolve().then(s.bind(s,1866)),Promise.resolve().then(s.bind(s,6519))},1866:(e,t,s)=>{"use strict";s.d(t,{default:()=>l});var a=s(5512),r=s(8009);let l=()=>{let e=(0,r.useRef)(null),t=(0,r.useRef)(void 0),s=(0,r.useRef)([]);return(0,r.useEffect)(()=>{let a=e.current;if(!a)return;let r=a.getContext("2d");if(!r)return;let l=()=>{a.width=a.offsetWidth,a.height=a.offsetHeight};l(),window.addEventListener("resize",l),(()=>{let e=[],t=["rgba(212, 175, 55, 0.6)","rgba(205, 133, 63, 0.5)","rgba(255, 215, 0, 0.4)","rgba(184, 134, 11, 0.5)","rgba(251, 191, 36, 0.4)","rgba(255, 193, 7, 0.5)","rgba(218, 165, 32, 0.4)","rgba(255, 140, 0, 0.3)","rgba(255, 165, 0, 0.4)"],r=["rgba(255, 215, 0, 0.8)","rgba(255, 193, 7, 0.8)","rgba(255, 140, 0, 0.7)"];["T","G","R","S","T","G","R","S"].forEach((t,s)=>{e.push({id:s,x:Math.random()*(a.width-100)+50,y:Math.random()*(a.height-100)+50,vx:(Math.random()-.5)*1.2,vy:(Math.random()-.5)*1.2,radius:22,color:r[Math.floor(Math.random()*r.length)],type:"letter",letter:t,rotation:Math.random()*Math.PI*2,rotationSpeed:(Math.random()-.5)*.02})});for(let s=8;s<16;s++){let r=["circle","triangle"][Math.floor(2*Math.random())];e.push({id:s,x:Math.random()*(a.width-60)+30,y:Math.random()*(a.height-60)+30,vx:(Math.random()-.5)*2,vy:(Math.random()-.5)*2,radius:10*Math.random()+5,color:t[Math.floor(Math.random()*t.length)],type:r,rotation:Math.random()*Math.PI*2,rotationSpeed:(Math.random()-.5)*.03})}s.current=e})();let i=(e,t)=>{let s=e.x-t.x,a=e.y-t.y;return Math.sqrt(s*s+a*a)<e.radius+t.radius},n=(e,t)=>{let s=e.x-t.x,a=e.y-t.y,r=Math.sqrt(s*s+a*a);if(0===r)return;let l=s/r,i=a/r,n=(e.vx-t.vx)*l+(e.vy-t.vy)*i;if(n>0)return;let o=2*n/2;e.vx-=o*l,e.vy-=o*i,t.vx+=o*l,t.vy+=o*i,e.rotationSpeed+=(Math.random()-.5)*.02,t.rotationSpeed+=(Math.random()-.5)*.02;let d=e.radius+t.radius-r,m=d/2*l,u=d/2*i;e.x+=m,e.y+=u,t.x-=m,t.y-=u},o=(e,t)=>{switch(e.fillStyle=t.color,e.strokeStyle=t.color.replace(/0\.\d+/,"0.8"),e.lineWidth=1,t.type){case"circle":e.beginPath(),e.arc(t.x,t.y,t.radius,0,2*Math.PI),e.fill(),e.stroke();break;case"triangle":e.save(),e.translate(t.x,t.y),e.rotate(t.rotation),e.beginPath(),e.moveTo(0,-t.radius),e.lineTo(-(.866*t.radius),.5*t.radius),e.lineTo(.866*t.radius,.5*t.radius),e.closePath(),e.fill(),e.stroke(),e.restore();break;case"letter":e.save(),e.translate(t.x,t.y),e.rotate(t.rotation),e.shadowColor=t.color,e.shadowBlur=8,e.shadowOffsetX=0,e.shadowOffsetY=0,e.font=`bold ${1.3*t.radius}px Arial`,e.textAlign="center",e.textBaseline="middle",e.fillStyle=t.color,e.strokeStyle="rgba(255, 255, 255, 0.3)",e.lineWidth=1,e.fillText(t.letter||"",0,0),e.strokeText(t.letter||"",0,0),e.shadowBlur=0,e.restore()}},d=()=>{r.clearRect(0,0,a.width,a.height);let e=s.current;e.forEach(e=>{e.x+=e.vx,e.y+=e.vy,e.rotation+=e.rotationSpeed,(e.x-e.radius<=0||e.x+e.radius>=a.width)&&(e.vx*=-.98,e.x=Math.max(e.radius,Math.min(a.width-e.radius,e.x)),e.rotationSpeed+=(Math.random()-.5)*.015),(e.y-e.radius<=0||e.y+e.radius>=a.height)&&(e.vy*=-.98,e.y=Math.max(e.radius,Math.min(a.height-e.radius,e.y)),e.rotationSpeed+=(Math.random()-.5)*.015),e.vx*=.9995,e.vy*=.9995;let t=Math.sqrt(e.vx*e.vx+e.vy*e.vy);if(t>3)e.vx=e.vx/t*3,e.vy=e.vy/t*3;else if(t<.5&&t>0){let s=.5/t;e.vx*=s,e.vy*=s}else 0===t&&(e.vx=(Math.random()-.5)*2,e.vy=(Math.random()-.5)*2)});for(let t=0;t<e.length;t++)for(let s=t+1;s<e.length;s++)i(e[t],e[s])&&n(e[t],e[s]);e.forEach(e=>o(r,e)),t.current=requestAnimationFrame(d)};return d(),()=>{window.removeEventListener("resize",l),t.current&&cancelAnimationFrame(t.current)}},[]),(0,a.jsx)("canvas",{ref:e,className:"absolute inset-0 w-full h-full pointer-events-none",style:{zIndex:1}})}},319:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>x,metadata:()=>c});var a=s(2740),r=s(1443),l=s(3852),i=s(3145);s(6301);let n=({title:e,description:t,icon:s,className:r="",tiltDirection:l="none",pattern:i="circuit"})=>(0,a.jsxs)("div",{className:`glass-enhanced rounded-lg p-3 sm:p-4 md:p-5 border border-neon-orange/30 hover:border-neon-orange/70 transition-all duration-500 hover:shadow-neon-strong group relative h-full flex flex-col ${(()=>{switch(l){case"left":return"card-tilt-left";case"right":return"card-tilt-right";default:return""}})()} ${(()=>{switch(i){case"dots":return"pattern-dots";case"grid":return"pattern-grid";case"hexagon":return"pattern-hexagon";case"circuit":return"pattern-circuit";default:return""}})()} ${r}`,children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-black/20 rounded-xl"}),(0,a.jsxs)("div",{className:"relative z-10 flex flex-col h-full",children:[s&&(0,a.jsx)("div",{className:"mb-6 sm:mb-4 md:mb-3 text-neon-orange group-hover:text-white transition-all duration-300 relative icon-container",children:(0,a.jsxs)("div",{className:"w-6 h-6 sm:w-7 sm:h-7 md:w-8 md:h-8 relative transform group-hover:scale-105 transition-transform duration-300 origin-center",children:[(0,a.jsxs)("div",{className:"absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-500 pointer-events-none",children:[(0,a.jsx)("div",{className:"absolute w-1 h-1 bg-neon-orange rounded-full animate-ping",style:{top:"-4px",left:"20%",animationDelay:"0s"}}),(0,a.jsx)("div",{className:"absolute w-0.5 h-0.5 bg-yellow-400 rounded-full animate-pulse",style:{top:"-2px",right:"25%",animationDelay:"0.4s"}}),(0,a.jsx)("div",{className:"absolute w-1 h-1 bg-orange-400 rounded-full animate-ping",style:{right:"-4px",top:"30%",animationDelay:"0.2s"}}),(0,a.jsx)("div",{className:"absolute w-0.5 h-0.5 bg-yellow-300 rounded-full animate-pulse",style:{right:"-2px",bottom:"35%",animationDelay:"0.7s"}}),(0,a.jsx)("div",{className:"absolute w-1 h-1 bg-neon-orange rounded-full animate-ping",style:{bottom:"-4px",right:"20%",animationDelay:"0.5s"}}),(0,a.jsx)("div",{className:"absolute w-0.5 h-0.5 bg-orange-500 rounded-full animate-pulse",style:{bottom:"-2px",left:"30%",animationDelay:"0.9s"}}),(0,a.jsx)("div",{className:"absolute w-1 h-1 bg-yellow-400 rounded-full animate-ping",style:{left:"-4px",bottom:"25%",animationDelay:"0.3s"}}),(0,a.jsx)("div",{className:"absolute w-0.5 h-0.5 bg-neon-orange rounded-full animate-pulse",style:{left:"-2px",top:"40%",animationDelay:"0.6s"}})]}),(0,a.jsx)("div",{className:"w-full h-full relative z-10",children:s})]})}),(0,a.jsx)("h3",{className:"text-sm sm:text-base md:text-lg font-display font-bold text-white mb-1 sm:mb-2 group-hover:text-neon-orange transition-colors duration-300",children:e}),(0,a.jsx)("p",{className:"text-xs sm:text-sm md:text-base text-gray-400 group-hover:text-gray-200 transition-colors duration-300 leading-relaxed flex-grow",children:t})]}),(0,a.jsx)("div",{className:"absolute top-2 right-2 w-3 h-3 border-t-2 border-r-2 border-neon-orange/30 group-hover:border-neon-orange transition-colors duration-300 z-20"}),(0,a.jsx)("div",{className:"absolute bottom-2 left-2 w-3 h-3 border-b-2 border-l-2 border-neon-orange/30 group-hover:border-neon-orange transition-colors duration-300 z-20"})]});var o=s(9607),d=s.n(o);let m=({children:e,href:t,variant:s="primary",size:r="md",className:l="",onClick:i})=>{let n=`inline-flex items-center justify-center font-medium transition-all duration-300 rounded-full ${{primary:"bg-gradient-to-r from-neon-orange to-neon-red text-white hover:shadow-neon",secondary:"bg-neon-orange text-white hover:bg-neon-orange/90 hover:shadow-neon-orange",outline:"border border-neon-orange text-neon-orange hover:bg-neon-orange/10",ghost:"text-white hover:text-neon-orange hover:bg-white/5"}[s]} ${{sm:"text-xs px-3 py-1.5",md:"text-sm px-4 py-2",lg:"text-base px-6 py-3"}[r]} ${l}`;return t?t.startsWith("http")||t.startsWith("https")?(0,a.jsx)("a",{href:t,className:n,target:"_blank",rel:"noopener noreferrer",children:e}):(0,a.jsx)(d(),{href:t,className:n,children:e}):(0,a.jsx)("button",{className:n,onClick:i,children:e})};var u=s(1750);let c={title:"TGRS - #1 Telugu FiveM Roleplay Server | Best Telugu Gaming Community",description:"Join TGRS - The premier Telugu FiveM roleplay server! Experience immersive GTA 5 roleplay with Telugu community, custom scripts, realistic economy, and 24/7 uptime. Best Telugu gaming server for FiveM enthusiasts.",keywords:["Telugu FiveM server","FiveM Telugu","Telugu gaming","GTA 5 roleplay Telugu","Telugu gaming community","FiveM roleplay server","Telugu gamers","TGRS","Telugu online gaming","FiveM RP Telugu","GTA roleplay Telugu","Telugu gaming server","GTA 5 Telugu","Telugu GTA 5","GTA Telugu","Telugu GTA","GTA 5 Telugu server","Telugu GTA 5 server","GTA V Telugu","Telugu GTA V","GTA roleplay Telugu","SAMP Telugu","Telugu SAMP","SAMP Telugu server","Telugu SAMP server","San Andreas Telugu","Telugu San Andreas","SAMP roleplay Telugu","Telugu server","server Telugu","Telugu gaming server","Telugu roleplay server","Telugu multiplayer server","Telugu community server","Telugu game server","Indian FiveM server","South India gaming","Hyderabad gaming","Telugu esports","Telangana gaming","Andhra Pradesh gaming","Hyderabad FiveM","Telangana FiveM","Andhra Pradesh FiveM","Vijayawada gaming","Visakhapatnam gaming","Warangal gaming","Telugu online gaming","Telugu multiplayer gaming","Telugu gaming community","Telugu gaming discord","Telugu gaming YouTube","Telugu gaming clan","Telugu gaming group","Telugu gaming family","Telugu gaming friends","Telugu culture gaming","Telugu tradition gaming","Telugu language gaming","Telugu speaking gamers","Telugu gaming culture","Telugu gaming pride","తెలుగు గేమింగ్","తెలుగు గేమర్స్","తెలుగు సర్వర్","Telugu gaming 2024","best Telugu gaming","top Telugu gaming","popular Telugu gaming"],openGraph:{title:"TGRS - #1 Telugu FiveM Roleplay Server | Best Telugu Gaming Community",description:"Join TGRS - The premier Telugu FiveM roleplay server! Experience immersive GTA 5 roleplay with Telugu community, custom scripts, realistic economy, and 24/7 uptime.",images:["/assets/tgrs-og-image.jpg"]},alternates:{canonical:"/"}};function x(){return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(r.default,{}),(0,a.jsx)(i.A,{title:"WELCOME TO TGRS",subtitle:"Experience immersive roleplay in a unique Telugu community with custom features and scripts",primaryButtonText:"Join Discord",primaryButtonHref:"https://discord.gg/GAMravHDnB",secondaryButtonText:"Learn More",secondaryButtonHref:"#features"}),(0,a.jsx)("div",{className:"section-divider"}),(0,a.jsxs)("section",{id:"about",className:"py-8 md:py-20 relative overflow-hidden min-h-screen",children:[(0,a.jsxs)("div",{className:"absolute inset-0 w-full h-full",children:[(0,a.jsx)("iframe",{src:"https://www.youtube.com/embed/pZbzoJAj4TE?autoplay=1&mute=1&loop=1&playlist=pZbzoJAj4TE&controls=0&showinfo=0&rel=0&iv_load_policy=3&modestbranding=1&disablekb=1&fs=0&cc_load_policy=0&start=33",className:"w-full h-full hidden md:block",style:{position:"absolute",top:"50%",left:"50%",width:"100vw",height:"56.25vw",minHeight:"100vh",minWidth:"177.77vh",transform:"translate(-50%, -50%)",pointerEvents:"none",border:"none"},allow:"autoplay; encrypted-media",allowFullScreen:!1}),(0,a.jsx)("div",{className:"block md:hidden absolute inset-0 w-full h-full",children:(0,a.jsx)("iframe",{src:"https://www.youtube.com/embed/waN7QSRC-YU?autoplay=1&mute=1&loop=1&playlist=waN7QSRC-YU&controls=0&showinfo=0&rel=0&iv_load_policy=3&modestbranding=1&disablekb=1&fs=0&cc_load_policy=0&start=0&end=45&playsinline=1&enablejsapi=0",className:"absolute",style:{top:"-50%",left:"-50%",width:"200%",height:"200%",minHeight:"200%",minWidth:"200%",pointerEvents:"none",border:"none",objectFit:"cover"},allow:"autoplay; encrypted-media",allowFullScreen:!1})}),(0,a.jsx)("div",{className:"absolute inset-0 bg-black/60"})]}),(0,a.jsxs)("div",{className:"container mx-auto px-4 md:px-6 relative z-10",children:[(0,a.jsxs)("div",{className:"text-center mb-6 sm:mb-8 md:mb-12 lg:mb-16",children:[(0,a.jsxs)("h2",{className:"text-xl sm:text-2xl md:text-3xl lg:text-4xl font-display font-bold text-white mb-3 sm:mb-4 wave-heading-bg",children:["Create Your ",(0,a.jsx)("span",{className:"text-neon-orange",children:"Unique Story"})]}),(0,a.jsx)("p",{className:"text-xs sm:text-sm md:text-base lg:text-lg text-gray-400 max-w-3xl mx-auto px-2",children:"Step into Los Santos and become anyone you want to be. Your story, your rules."})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4 md:gap-6 lg:gap-8 mb-6 sm:mb-8 md:mb-12 lg:mb-16 px-2 sm:px-0",children:[(0,a.jsxs)("div",{className:"glass-enhanced rounded-lg sm:rounded-xl p-3 sm:p-4 md:p-5 lg:p-6 border border-red-500/30 hover:border-red-500/70 transition-all duration-500 hover:shadow-neon-strong group relative h-full flex flex-col pattern-circuit",children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-black/20 rounded-xl"}),(0,a.jsxs)("div",{className:"relative z-10 flex flex-col h-full",children:[(0,a.jsx)("div",{className:"mb-3 text-red-500 group-hover:text-white transition-all duration-300 relative icon-container",children:(0,a.jsxs)("div",{className:"w-8 h-8 md:w-10 md:h-10 relative transform group-hover:scale-105 transition-transform duration-300 origin-center",children:[(0,a.jsxs)("div",{className:"absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-500 pointer-events-none",children:[(0,a.jsx)("div",{className:"absolute w-1 h-1 bg-red-500 rounded-full animate-ping",style:{top:"-4px",left:"20%",animationDelay:"0s"}}),(0,a.jsx)("div",{className:"absolute w-0.5 h-0.5 bg-red-400 rounded-full animate-pulse",style:{top:"-2px",right:"25%",animationDelay:"0.4s"}}),(0,a.jsx)("div",{className:"absolute w-1 h-1 bg-red-600 rounded-full animate-ping",style:{right:"-4px",top:"30%",animationDelay:"0.2s"}}),(0,a.jsx)("div",{className:"absolute w-0.5 h-0.5 bg-red-300 rounded-full animate-pulse",style:{right:"-2px",bottom:"35%",animationDelay:"0.7s"}}),(0,a.jsx)("div",{className:"absolute w-1 h-1 bg-red-500 rounded-full animate-ping",style:{bottom:"-4px",right:"20%",animationDelay:"0.5s"}}),(0,a.jsx)("div",{className:"absolute w-0.5 h-0.5 bg-red-700 rounded-full animate-pulse",style:{bottom:"-2px",left:"30%",animationDelay:"0.9s"}}),(0,a.jsx)("div",{className:"absolute w-1 h-1 bg-red-400 rounded-full animate-ping",style:{left:"-4px",bottom:"25%",animationDelay:"0.3s"}}),(0,a.jsx)("div",{className:"absolute w-0.5 h-0.5 bg-red-500 rounded-full animate-pulse",style:{left:"-2px",top:"40%",animationDelay:"0.6s"}})]}),(0,a.jsx)("svg",{className:"w-full h-full relative z-10",fill:"currentColor",viewBox:"0 0 20 20",children:(0,a.jsx)("path",{d:"M10 2L3 7v11h4v-6h6v6h4V7l-7-5z"})})]})}),(0,a.jsx)("h3",{className:"text-lg sm:text-xl md:text-2xl font-display font-bold text-white mb-2 group-hover:text-red-400 transition-colors duration-300",children:"Criminal"}),(0,a.jsx)("p",{className:"text-sm sm:text-base text-gray-400 group-hover:text-gray-200 transition-colors duration-300 leading-relaxed flex-grow",children:"Build your empire from the streets. Rob banks, run illegal businesses, and climb the criminal hierarchy."})]}),(0,a.jsx)("div",{className:"absolute top-2 right-2 w-3 h-3 border-t-2 border-r-2 border-red-500/30 group-hover:border-red-500 transition-colors duration-300 z-20"}),(0,a.jsx)("div",{className:"absolute bottom-2 left-2 w-3 h-3 border-b-2 border-l-2 border-red-500/30 group-hover:border-red-500 transition-colors duration-300 z-20"})]}),(0,a.jsxs)("div",{className:"glass-enhanced rounded-xl p-4 sm:p-5 md:p-6 border border-blue-500/30 hover:border-blue-500/70 transition-all duration-500 hover:shadow-neon-strong group relative h-full flex flex-col pattern-hexagon",children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-black/20 rounded-xl"}),(0,a.jsxs)("div",{className:"relative z-10 flex flex-col h-full",children:[(0,a.jsx)("div",{className:"mb-3 text-blue-500 group-hover:text-white transition-all duration-300 relative icon-container",children:(0,a.jsxs)("div",{className:"w-8 h-8 md:w-10 md:h-10 relative transform group-hover:scale-105 transition-transform duration-300 origin-center",children:[(0,a.jsxs)("div",{className:"absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-500 pointer-events-none",children:[(0,a.jsx)("div",{className:"absolute w-1 h-1 bg-blue-500 rounded-full animate-ping",style:{top:"-4px",left:"20%",animationDelay:"0s"}}),(0,a.jsx)("div",{className:"absolute w-0.5 h-0.5 bg-blue-400 rounded-full animate-pulse",style:{top:"-2px",right:"25%",animationDelay:"0.4s"}}),(0,a.jsx)("div",{className:"absolute w-1 h-1 bg-blue-600 rounded-full animate-ping",style:{right:"-4px",top:"30%",animationDelay:"0.2s"}}),(0,a.jsx)("div",{className:"absolute w-0.5 h-0.5 bg-blue-300 rounded-full animate-pulse",style:{right:"-2px",bottom:"35%",animationDelay:"0.7s"}}),(0,a.jsx)("div",{className:"absolute w-1 h-1 bg-blue-500 rounded-full animate-ping",style:{bottom:"-4px",right:"20%",animationDelay:"0.5s"}}),(0,a.jsx)("div",{className:"absolute w-0.5 h-0.5 bg-blue-700 rounded-full animate-pulse",style:{bottom:"-2px",left:"30%",animationDelay:"0.9s"}}),(0,a.jsx)("div",{className:"absolute w-1 h-1 bg-blue-400 rounded-full animate-ping",style:{left:"-4px",bottom:"25%",animationDelay:"0.3s"}}),(0,a.jsx)("div",{className:"absolute w-0.5 h-0.5 bg-blue-500 rounded-full animate-pulse",style:{left:"-2px",top:"40%",animationDelay:"0.6s"}})]}),(0,a.jsx)("svg",{className:"w-full h-full relative z-10",fill:"currentColor",viewBox:"0 0 20 20",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M9.383 3.076A1 1 0 0110 4v12a1 1 0 01-1.707.707L4.586 13H2a1 1 0 01-1-1V8a1 1 0 011-1h2.586l3.707-3.707a1 1 0 011.09-.217z",clipRule:"evenodd"})})]})}),(0,a.jsx)("h3",{className:"text-lg sm:text-xl md:text-2xl font-display font-bold text-white mb-2 group-hover:text-blue-400 transition-colors duration-300",children:"Legal"}),(0,a.jsx)("p",{className:"text-sm sm:text-base text-gray-400 group-hover:text-gray-200 transition-colors duration-300 leading-relaxed flex-grow",children:"Enforce the law? Revive people? Repair vehicles? Many more than you can imagine, we shelter everyone.."})]}),(0,a.jsx)("div",{className:"absolute top-2 right-2 w-3 h-3 border-t-2 border-r-2 border-blue-500/30 group-hover:border-blue-500 transition-colors duration-300 z-20"}),(0,a.jsx)("div",{className:"absolute bottom-2 left-2 w-3 h-3 border-b-2 border-l-2 border-blue-500/30 group-hover:border-blue-500 transition-colors duration-300 z-20"})]}),(0,a.jsxs)("div",{className:"glass-enhanced rounded-xl p-4 sm:p-5 md:p-6 border border-green-500/30 hover:border-green-500/70 transition-all duration-500 hover:shadow-neon-strong group relative h-full flex flex-col pattern-dots",children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-black/20 rounded-xl"}),(0,a.jsxs)("div",{className:"relative z-10 flex flex-col h-full",children:[(0,a.jsx)("div",{className:"mb-3 text-green-500 group-hover:text-white transition-all duration-300 relative icon-container",children:(0,a.jsxs)("div",{className:"w-8 h-8 md:w-10 md:h-10 relative transform group-hover:scale-105 transition-transform duration-300 origin-center",children:[(0,a.jsxs)("div",{className:"absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-500 pointer-events-none",children:[(0,a.jsx)("div",{className:"absolute w-1 h-1 bg-green-500 rounded-full animate-ping",style:{top:"-4px",left:"20%",animationDelay:"0s"}}),(0,a.jsx)("div",{className:"absolute w-0.5 h-0.5 bg-green-400 rounded-full animate-pulse",style:{top:"-2px",right:"25%",animationDelay:"0.4s"}}),(0,a.jsx)("div",{className:"absolute w-1 h-1 bg-green-600 rounded-full animate-ping",style:{right:"-4px",top:"30%",animationDelay:"0.2s"}}),(0,a.jsx)("div",{className:"absolute w-0.5 h-0.5 bg-green-300 rounded-full animate-pulse",style:{right:"-2px",bottom:"35%",animationDelay:"0.7s"}}),(0,a.jsx)("div",{className:"absolute w-1 h-1 bg-green-500 rounded-full animate-ping",style:{bottom:"-4px",right:"20%",animationDelay:"0.5s"}}),(0,a.jsx)("div",{className:"absolute w-0.5 h-0.5 bg-green-700 rounded-full animate-pulse",style:{bottom:"-2px",left:"30%",animationDelay:"0.9s"}}),(0,a.jsx)("div",{className:"absolute w-1 h-1 bg-green-400 rounded-full animate-ping",style:{left:"-4px",bottom:"25%",animationDelay:"0.3s"}}),(0,a.jsx)("div",{className:"absolute w-0.5 h-0.5 bg-green-500 rounded-full animate-pulse",style:{left:"-2px",top:"40%",animationDelay:"0.6s"}})]}),(0,a.jsx)("svg",{className:"w-full h-full relative z-10",fill:"currentColor",viewBox:"0 0 20 20",children:(0,a.jsx)("path",{d:"M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"})})]})}),(0,a.jsx)("h3",{className:"text-lg sm:text-xl md:text-2xl font-display font-bold text-white mb-2 group-hover:text-green-400 transition-colors duration-300",children:"Civilian"}),(0,a.jsx)("p",{className:"text-sm sm:text-base text-gray-400 group-hover:text-gray-200 transition-colors duration-300 leading-relaxed flex-grow",children:"Live a normal life. Start businesses, buy properties, and build relationships in the community."})]}),(0,a.jsx)("div",{className:"absolute top-2 right-2 w-3 h-3 border-t-2 border-r-2 border-green-500/30 group-hover:border-green-500 transition-colors duration-300 z-20"}),(0,a.jsx)("div",{className:"absolute bottom-2 left-2 w-3 h-3 border-b-2 border-l-2 border-green-500/30 group-hover:border-green-500 transition-colors duration-300 z-20"})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4 sm:gap-6 md:gap-8 px-2 sm:px-0",children:[(0,a.jsxs)("div",{className:"text-center p-3 sm:p-4",children:[(0,a.jsx)("div",{className:"text-2xl sm:text-3xl md:text-4xl font-display font-bold text-neon-orange mb-2",children:"80+"}),(0,a.jsx)("div",{className:"text-xs sm:text-sm md:text-base text-gray-400",children:"Active Players"})]}),(0,a.jsxs)("div",{className:"text-center p-3 sm:p-4",children:[(0,a.jsx)("div",{className:"text-2xl sm:text-3xl md:text-4xl font-display font-bold text-neon-orange mb-2",children:"24/7"}),(0,a.jsx)("div",{className:"text-xs sm:text-sm md:text-base text-gray-400",children:"Server Uptime"})]}),(0,a.jsxs)("div",{className:"text-center p-3 sm:p-4",children:[(0,a.jsx)("div",{className:"text-2xl sm:text-3xl md:text-4xl font-display font-bold text-neon-orange mb-2",children:"10+"}),(0,a.jsx)("div",{className:"text-xs sm:text-sm md:text-base text-gray-400",children:"Custom Scripts"})]}),(0,a.jsxs)("div",{className:"text-center p-3 sm:p-4",children:[(0,a.jsx)("div",{className:"text-2xl sm:text-3xl md:text-4xl font-display font-bold text-neon-orange mb-2",children:"100%"}),(0,a.jsx)("div",{className:"text-xs sm:text-sm md:text-base text-gray-400",children:"Telugu Community"})]})]})]})]}),(0,a.jsx)("div",{className:"section-divider"}),(0,a.jsx)("section",{id:"features",className:"py-12 md:py-20 relative bg-black/50",children:(0,a.jsxs)("div",{className:"container mx-auto px-6 sm:px-12 md:px-16 lg:px-20 xl:px-24",children:[(0,a.jsxs)("div",{className:"text-center mb-8 md:mb-12",children:[(0,a.jsxs)("h2",{className:"text-2xl sm:text-3xl md:text-4xl font-display font-bold text-white mb-4 wave-heading-bg",children:[(0,a.jsx)("span",{className:"text-neon-orange",children:"UNIQUE"})," FEATURES"]}),(0,a.jsx)("p",{className:"text-sm sm:text-base md:text-lg text-gray-400 max-w-3xl mx-auto px-2",children:"Discover what makes our server special with these exclusive features"})]}),(0,a.jsx)("div",{className:"max-w-6xl mx-auto",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-5 md:gap-6 items-stretch",children:[(0,a.jsx)(n,{title:"24/7 Server Uptime",description:"Our server is online 24/7, ensuring you can play whenever you want without interruptions.",tiltDirection:"left",pattern:"circuit",className:"animate-slide-in-left",icon:(0,a.jsx)("svg",{className:"w-10 h-10",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})})}),(0,a.jsx)(n,{title:"Realistic Economy",description:"Experience a balanced and realistic economy system with multiple jobs and business opportunities.",tiltDirection:"right",pattern:"hexagon",className:"animate-slide-in-right",icon:(0,a.jsx)("svg",{className:"w-10 h-10",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})})}),(0,a.jsx)(n,{title:"Immersive Roleplay",description:"Dive into a rich roleplay experience with custom scripts and scenarios designed for the Telugu community.",tiltDirection:"left",pattern:"dots",className:"animate-slide-in-left",icon:(0,a.jsx)("svg",{className:"w-10 h-10",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M7 4v16M17 4v16M3 8h4m10 0h4M3 12h18M3 16h4m10 0h4M4 20h16a1 1 0 001-1V5a1 1 0 00-1-1H4a1 1 0 00-1 1v14a1 1 0 001 1z"})})}),(0,a.jsx)(n,{title:"Friendly Community",description:"Join a welcoming Telugu community that values respect, creativity, and fun roleplay experiences.",tiltDirection:"right",pattern:"grid",className:"animate-slide-in-right",icon:(0,a.jsx)("svg",{className:"w-10 h-10",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"})})}),(0,a.jsx)(n,{title:"Supportive Staff",description:"Our dedicated staff team is always ready to help, ensuring a smooth and enjoyable experience for all players.",tiltDirection:"left",pattern:"circuit",className:"animate-slide-in-left",icon:(0,a.jsx)("svg",{className:"w-10 h-10",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192l-3.536 3.536M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-5 0a4 4 0 11-8 0 4 4 0 018 0z"})})}),(0,a.jsx)(n,{title:"Active Development",description:"Regular updates and new features keep the server fresh and exciting with continuous improvements.",tiltDirection:"right",pattern:"hexagon",className:"animate-slide-in-right",icon:(0,a.jsx)("svg",{className:"w-10 h-10",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"})})})]})})]})}),(0,a.jsx)("div",{className:"section-divider"}),(0,a.jsxs)("section",{id:"join",className:"py-8 sm:py-12 md:py-20 relative bg-black/50",children:[(0,a.jsx)(u.default,{}),(0,a.jsx)("div",{className:"container mx-auto px-4 md:px-6 relative z-10",children:(0,a.jsxs)("div",{className:"glass rounded-lg p-4 sm:p-6 md:p-8 lg:p-12 border border-neon-orange/30 max-w-4xl mx-auto",children:[(0,a.jsxs)("div",{className:"text-center mb-4 sm:mb-6 md:mb-8",children:[(0,a.jsxs)("h2",{className:"text-xl sm:text-2xl md:text-3xl lg:text-4xl font-display font-bold text-white mb-3 sm:mb-4 wave-heading-bg",children:[(0,a.jsx)("span",{className:"text-neon-orange",children:"JOIN"})," OUR SERVER"]}),(0,a.jsx)("p",{className:"text-xs sm:text-sm md:text-base text-gray-400 px-2",children:"Ready to start your journey in the TGRS community? Follow these steps to join our server."})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-3 sm:gap-4 md:gap-6 mb-4 sm:mb-6 md:mb-8",children:[(0,a.jsxs)("div",{className:"text-center p-2 sm:p-3 md:p-4",children:[(0,a.jsx)("div",{className:"w-10 h-10 sm:w-12 sm:h-12 md:w-16 md:h-16 rounded-full bg-neon-orange/20 flex items-center justify-center mx-auto mb-2 sm:mb-3",children:(0,a.jsx)("span",{className:"text-neon-orange font-display font-bold text-lg sm:text-xl md:text-2xl",children:"1"})}),(0,a.jsx)("h3",{className:"text-white font-semibold mb-1 sm:mb-2 text-xs sm:text-sm md:text-base",children:"Join Discord"}),(0,a.jsx)("p",{className:"text-gray-400 text-xs sm:text-sm",children:"Connect with our community on Discord for server updates and support."})]}),(0,a.jsxs)("div",{className:"text-center p-2 sm:p-3 md:p-4",children:[(0,a.jsx)("div",{className:"w-10 h-10 sm:w-12 sm:h-12 md:w-16 md:h-16 rounded-full bg-neon-orange/20 flex items-center justify-center mx-auto mb-2 sm:mb-3",children:(0,a.jsx)("span",{className:"text-neon-orange font-display font-bold text-lg sm:text-xl md:text-2xl",children:"2"})}),(0,a.jsx)("h3",{className:"text-white font-semibold mb-1 sm:mb-2 text-xs sm:text-sm md:text-base",children:"Read Rules"}),(0,a.jsx)("p",{className:"text-gray-400 text-xs sm:text-sm",children:"Familiarize yourself with our server rules to ensure a positive experience."})]}),(0,a.jsxs)("div",{className:"text-center p-2 sm:p-3 md:p-4 sm:col-span-2 md:col-span-1",children:[(0,a.jsx)("div",{className:"w-10 h-10 sm:w-12 sm:h-12 md:w-16 md:h-16 rounded-full bg-neon-orange/20 flex items-center justify-center mx-auto mb-2 sm:mb-3",children:(0,a.jsx)("span",{className:"text-neon-orange font-display font-bold text-lg sm:text-xl md:text-2xl",children:"3"})}),(0,a.jsx)("h3",{className:"text-white font-semibold mb-1 sm:mb-2 text-xs sm:text-sm md:text-base",children:"Connect & Play"}),(0,a.jsx)("p",{className:"text-gray-400 text-xs sm:text-sm",children:"Use FiveM to connect to our server and start your roleplay journey."})]})]}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-2 sm:gap-3 md:gap-4 justify-center px-2 sm:px-4 md:px-0",children:[(0,a.jsx)(m,{href:"https://discord.gg/GAMravHDnB",variant:"primary",size:"md",className:"w-full sm:w-auto text-xs sm:text-sm md:text-base py-2 sm:py-3",children:"Join Discord"}),(0,a.jsx)(m,{href:"/rules",variant:"outline",size:"md",className:"w-full sm:w-auto text-xs sm:text-sm md:text-base py-2 sm:py-3",children:"View Rules"})]})]})})]}),(0,a.jsx)("div",{style:{display:"none"},children:(0,a.jsxs)("article",{children:[(0,a.jsx)("h1",{children:"Telugu FiveM Server - Best GTA 5 Roleplay Experience"}),(0,a.jsx)("p",{children:"TGRS (Telugu Gaming Roleplay Server) is the premier Telugu FiveM server offering authentic GTA 5 roleplay experience for Telugu gamers. Join thousands of players including Hyper King, Macpie, and Satwik King from Hyderabad, Telangana, Andhra Pradesh, and across India in the best Telugu gaming community."}),(0,a.jsx)("h2",{children:"Telugu GTA 5 Roleplay Server Features"}),(0,a.jsx)("p",{children:"Experience the ultimate Telugu FiveM server with custom scripts designed specifically for Telugu players. Our GTA 5 roleplay server features 24/7 uptime, professional staff, and regular updates to ensure the best gaming experience."}),(0,a.jsx)("h3",{children:"Why Choose TGRS Telugu FiveM Server?"}),(0,a.jsxs)("ul",{children:[(0,a.jsx)("li",{children:"Best Telugu FiveM roleplay server in India"}),(0,a.jsx)("li",{children:"500+ active Telugu gamers online daily"}),(0,a.jsx)("li",{children:"Custom Telugu scripts and features"}),(0,a.jsx)("li",{children:"24/7 server uptime with professional support"}),(0,a.jsx)("li",{children:"Active Discord community for Telugu gamers"}),(0,a.jsx)("li",{children:"Regular events and competitions"}),(0,a.jsx)("li",{children:"VIP membership with exclusive benefits"}),(0,a.jsx)("li",{children:"Professional staff and moderation"})]}),(0,a.jsx)("h2",{children:"Telugu Gaming Community"}),(0,a.jsx)("p",{children:"Connect with fellow Telugu gamers including popular streamers like Hyper King, Macpie, and Satwik King from Hyderabad, Telangana, Andhra Pradesh, and across India. Our active Discord community provides a platform for Telugu gamers to interact, share experiences, and participate in community events."}),(0,a.jsx)("h3",{children:"GTA 5 Telugu Roleplay Experience"}),(0,a.jsx)("p",{children:"Immerse yourself in authentic Telugu roleplay scenarios with custom scripts that resonate with Telugu culture and language. Whether you're into competitive gaming, casual roleplay, or esports, TGRS offers the perfect environment for Telugu gaming enthusiasts."}),(0,a.jsx)("h2",{children:"Join Telugu FiveM Server Today"}),(0,a.jsx)("p",{children:"Ready to experience the best Telugu GTA 5 roleplay server? Join TGRS today and become part of the largest Telugu gaming community. Connect with players from Hyderabad, Telangana, Andhra Pradesh, and across India."}),(0,a.jsx)("h3",{children:"Telugu FiveM Server Keywords"}),(0,a.jsx)("p",{children:"Telugu FiveM, Telugu GTA 5, GTA 5 Telugu, Telugu roleplay server, FiveM Telugu server, Telugu gaming community, Hyderabad FiveM, Telangana gaming, Andhra Pradesh gamers, Telugu SAMP, GTA 5 roleplay Telugu, FiveM India Telugu, Telugu gaming server, GTA V Telugu, Telugu multiplayer gaming, Hyper King Telugu gaming, Macpie FiveM, Satwik King roleplay, Telugu streamers gaming."}),(0,a.jsx)("h2",{children:"Server Information"}),(0,a.jsx)("p",{children:"Server IP: cfx.re/join/o57lj7"}),(0,a.jsx)("p",{children:"Discord: https://discord.gg/GAMravHDnB"}),(0,a.jsx)("p",{children:"Location: India"}),(0,a.jsx)("p",{children:"Language: Telugu, English"}),(0,a.jsx)("p",{children:"Max Players: 48"}),(0,a.jsx)("p",{children:"Server Type: FiveM Roleplay"}),(0,a.jsx)("h3",{children:"Telugu Gaming Blog Posts"}),(0,a.jsxs)("article",{children:[(0,a.jsx)("h4",{children:"Best Telugu FiveM Servers in 2024"}),(0,a.jsx)("p",{children:"Discover the top Telugu FiveM servers offering the best GTA 5 roleplay experience for Telugu gamers. TGRS leads the way with innovative features and active community."})]}),(0,a.jsxs)("article",{children:[(0,a.jsx)("h4",{children:"How to Join Telugu FiveM Server"}),(0,a.jsx)("p",{children:"Step-by-step guide to joining TGRS Telugu FiveM server. Learn how to connect, create your character, and start your roleplay journey in the Telugu gaming community."})]}),(0,a.jsxs)("article",{children:[(0,a.jsx)("h4",{children:"Telugu Gaming Community Growth"}),(0,a.jsx)("p",{children:"The Telugu gaming community has grown exponentially with servers like TGRS providing authentic Telugu gaming experiences. Popular streamers like Hyper King, Macpie, and Satwik King have contributed to this growth. Join the movement and connect with fellow Telugu gamers."})]}),(0,a.jsxs)("article",{children:[(0,a.jsx)("h4",{children:"FiveM Telugu Scripts and Features"}),(0,a.jsx)("p",{children:"Explore custom Telugu scripts and features available on TGRS server. From Telugu language support to cultural roleplay scenarios, experience gaming like never before."})]}),(0,a.jsxs)("article",{children:[(0,a.jsx)("h4",{children:"Telugu Roleplay Server Events"}),(0,a.jsx)("p",{children:"Participate in exciting events and competitions on TGRS Telugu roleplay server. From racing tournaments to roleplay competitions, there's always something happening."})]})]})}),(0,a.jsx)(l.A,{})]})}},1750:(e,t,s)=>{"use strict";s.d(t,{default:()=>a});let a=(0,s(6760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\TGRS Website\\\\tgrs-website\\\\src\\\\components\\\\BouncingShapes.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\TGRS Website\\tgrs-website\\src\\components\\BouncingShapes.tsx","default")},3145:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});var a=s(2740);s(6301);let r=({title:e,subtitle:t,primaryButtonText:s,primaryButtonHref:r,secondaryButtonText:l,secondaryButtonHref:i,backgroundImage:n="/assets/image-3.jpg"})=>(0,a.jsxs)("div",{className:"relative min-h-screen flex items-center justify-center overflow-hidden",children:[(0,a.jsx)("div",{className:"absolute inset-0 z-0 bg-cover bg-center bg-no-repeat",style:{backgroundImage:`url('${n}')`}}),(0,a.jsxs)("div",{className:"absolute inset-0 z-10",children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-black opacity-60"}),(0,a.jsx)("div",{className:"absolute top-0 left-0 w-full h-full bg-gradient-to-b from-black/20 via-transparent to-black/80"}),(0,a.jsx)("div",{className:"absolute inset-0 flex flex-col opacity-5",children:Array.from({length:15}).map((e,t)=>(0,a.jsx)("div",{className:"h-px bg-neon-orange w-full animate-pulse-slow",style:{marginTop:`${7*t}vh`,animationDelay:`${.2*t}s`}},`h-${t}`))}),(0,a.jsx)("div",{className:"absolute inset-0 flex flex-row opacity-5",children:Array.from({length:15}).map((e,t)=>(0,a.jsx)("div",{className:"w-px bg-neon-orange h-full animate-pulse-slow",style:{marginLeft:`${7*t}vw`,animationDelay:`${.2*t}s`}},`v-${t}`))}),(0,a.jsx)("div",{className:"absolute top-1/4 left-1/4 w-96 h-96 rounded-full bg-neon-orange/10 blur-3xl animate-float"}),(0,a.jsx)("div",{className:"absolute bottom-1/4 right-1/4 w-64 h-64 rounded-full bg-neon-red/10 blur-3xl animate-float",style:{animationDelay:"3s"}})]}),(0,a.jsx)("div",{className:"container mx-auto px-4 md:px-6 relative z-20 text-center flex flex-col justify-center min-h-[75vh] pt-20 md:pt-36",children:(0,a.jsxs)("div",{className:"space-y-4 md:space-y-6",children:[(0,a.jsx)("h1",{className:"text-3xl sm:text-4xl md:text-6xl lg:text-7xl font-display font-bold tracking-wider text-white leading-tight",children:(0,a.jsx)("span",{className:"inline-block",children:e})}),(0,a.jsx)("p",{className:"text-lg sm:text-xl md:text-2xl text-gray-300 max-w-3xl mx-auto leading-relaxed px-2",children:t})]})}),(0,a.jsxs)("div",{className:"absolute bottom-6 md:bottom-10 left-1/2 transform -translate-x-1/2 flex flex-col items-center z-20",children:[(0,a.jsxs)("div",{className:"block md:hidden",children:[(0,a.jsx)("span",{className:"text-white text-xs mb-2",children:"Swipe Up"}),(0,a.jsx)("div",{className:"flex items-center justify-center",children:(0,a.jsx)("div",{className:"w-8 h-8 flex items-center justify-center",children:(0,a.jsxs)("svg",{className:"w-6 h-6 text-white animate-bounce",fill:"currentColor",viewBox:"0 0 24 24",children:[(0,a.jsx)("path",{d:"M12 2c-1.1 0-2 .9-2 2v8c0 .55-.45 1-1 1s-1-.45-1-1V8c0-1.1-.9-2-2-2s-2 .9-2 2v4c0 .55-.45 1-1 1s-1-.45-1-1V10c0-1.1-.9-2-2-2s-2 .9-2 2v8c0 3.31 2.69 6 6 6h6c3.31 0 6-2.69 6-6V4c0-1.1-.9-2-2-2s-2 .9-2 2v8c0 .55-.45 1-1 1s-1-.45-1-1V4c0-1.1-.9-2-2-2z"}),(0,a.jsx)("path",{d:"M12 8l-3 3h2v4h2v-4h2l-3-3z",fill:"white",opacity:"0.8"})]})})})]}),(0,a.jsxs)("div",{className:"hidden md:flex md:flex-col md:items-center",children:[(0,a.jsx)("span",{className:"text-white text-sm mb-2",children:"Scroll Down"}),(0,a.jsx)("div",{className:"w-6 h-10 border-2 border-white rounded-full flex justify-center items-start pt-2",children:(0,a.jsx)("div",{className:"w-1 h-2 bg-white rounded-full animate-bounce"})})]})]})]})}};var t=require("../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[638,413,666,685,540,662,328],()=>s(2022));module.exports=a})();