{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:file((?!\\.well-known(?:/.*)?)(?:[^/]+/)*[^/]+\\.\\w+)/", "destination": "/:file", "internal": true, "missing": [{"type": "header", "key": "x-nextjs-data"}], "statusCode": 308, "regex": "^(?:/((?!\\.well-known(?:/.*)?)(?:[^/]+/)*[^/]+\\.\\w+))/$"}, {"source": "/:notfile((?!\\.well-known(?:/.*)?)(?:[^/]+/)*[^/\\.]+)", "destination": "/:notfile/", "internal": true, "statusCode": 308, "regex": "^(?:/((?!\\.well-known(?:/.*)?)(?:[^/]+/)*[^/\\.]+))$"}], "headers": [], "dynamicRoutes": [], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}, {"page": "/_not-found", "regex": "^/_not\\-found(?:/)?$", "routeKeys": {}, "namedRegex": "^/_not\\-found(?:/)?$"}, {"page": "/about", "regex": "^/about(?:/)?$", "routeKeys": {}, "namedRegex": "^/about(?:/)?$"}, {"page": "/contact", "regex": "^/contact(?:/)?$", "routeKeys": {}, "namedRegex": "^/contact(?:/)?$"}, {"page": "/favicon.ico", "regex": "^/favicon\\.ico(?:/)?$", "routeKeys": {}, "namedRegex": "^/favicon\\.ico(?:/)?$"}, {"page": "/features", "regex": "^/features(?:/)?$", "routeKeys": {}, "namedRegex": "^/features(?:/)?$"}, {"page": "/fivem-roleplay-server", "regex": "^/fivem\\-roleplay\\-server(?:/)?$", "routeKeys": {}, "namedRegex": "^/fivem\\-roleplay\\-server(?:/)?$"}, {"page": "/robots.txt", "regex": "^/robots\\.txt(?:/)?$", "routeKeys": {}, "namedRegex": "^/robots\\.txt(?:/)?$"}, {"page": "/rules", "regex": "^/rules(?:/)?$", "routeKeys": {}, "namedRegex": "^/rules(?:/)?$"}, {"page": "/sitemap.xml", "regex": "^/sitemap\\.xml(?:/)?$", "routeKeys": {}, "namedRegex": "^/sitemap\\.xml(?:/)?$"}, {"page": "/telugu-fivem-server", "regex": "^/telugu\\-fivem\\-server(?:/)?$", "routeKeys": {}, "namedRegex": "^/telugu\\-fivem\\-server(?:/)?$"}, {"page": "/telugu-gaming-guide", "regex": "^/telugu\\-gaming\\-guide(?:/)?$", "routeKeys": {}, "namedRegex": "^/telugu\\-gaming\\-guide(?:/)?$"}, {"page": "/telugu-samp-server", "regex": "^/telugu\\-samp\\-server(?:/)?$", "routeKeys": {}, "namedRegex": "^/telugu\\-samp\\-server(?:/)?$"}, {"page": "/vip", "regex": "^/vip(?:/)?$", "routeKeys": {}, "namedRegex": "^/vip(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch, Next-Router-Segment-Prefetch", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc"}, "rewrites": []}