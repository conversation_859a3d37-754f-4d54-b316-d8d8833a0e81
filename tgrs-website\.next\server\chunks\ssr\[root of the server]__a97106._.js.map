{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/TGRS%20Website/tgrs-website/src/components/Analytics.tsx"], "sourcesContent": ["'use client';\n\nimport Script from 'next/script';\n\nexport default function Analytics() {\n  return (\n    <>\n      {/* Google Analytics */}\n      <Script\n        src=\"https://www.googletagmanager.com/gtag/js?id=GA_MEASUREMENT_ID\"\n        strategy=\"afterInteractive\"\n      />\n      <Script id=\"google-analytics\" strategy=\"afterInteractive\">\n        {`\n          window.dataLayer = window.dataLayer || [];\n          function gtag(){dataLayer.push(arguments);}\n          gtag('js', new Date());\n          gtag('config', 'GA_MEASUREMENT_ID', {\n            page_title: document.title,\n            page_location: window.location.href,\n            custom_map: {\n              'custom_parameter_1': 'server_type',\n              'custom_parameter_2': 'user_language'\n            }\n          });\n          \n          // Track Telugu gaming events\n          gtag('event', 'page_view', {\n            'custom_parameter_1': 'telugu_fivem',\n            'custom_parameter_2': 'telugu'\n          });\n        `}\n      </Script>\n\n      {/* Microsoft Clarity */}\n      <Script id=\"microsoft-clarity\" strategy=\"afterInteractive\">\n        {`\n          (function(c,l,a,r,i,t,y){\n            c[a]=c[a]||function(){(c[a].q=c[a].q||[]).push(arguments)};\n            t=l.createElement(r);t.async=1;t.src=\"https://www.clarity.ms/tag/\"+i;\n            y=l.getElementsByTagName(r)[0];y.parentNode.insertBefore(t,y);\n          })(window, document, \"clarity\", \"script\", \"CLARITY_PROJECT_ID\");\n        `}\n      </Script>\n\n      {/* Facebook Pixel */}\n      <Script id=\"facebook-pixel\" strategy=\"afterInteractive\">\n        {`\n          !function(f,b,e,v,n,t,s)\n          {if(f.fbq)return;n=f.fbq=function(){n.callMethod?\n          n.callMethod.apply(n,arguments):n.queue.push(arguments)};\n          if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';\n          n.queue=[];t=b.createElement(e);t.async=!0;\n          t.src=v;s=b.getElementsByTagName(e)[0];\n          s.parentNode.insertBefore(t,s)}(window, document,'script',\n          'https://connect.facebook.net/en_US/fbevents.js');\n          fbq('init', 'FACEBOOK_PIXEL_ID');\n          fbq('track', 'PageView');\n          \n          // Track Telugu gaming interest\n          fbq('trackCustom', 'TeluguGamingInterest', {\n            server_type: 'fivem',\n            language: 'telugu',\n            community: 'tgrs'\n          });\n        `}\n      </Script>\n\n      {/* Hotjar */}\n      <Script id=\"hotjar\" strategy=\"afterInteractive\">\n        {`\n          (function(h,o,t,j,a,r){\n            h.hj=h.hj||function(){(h.hj.q=h.hj.q||[]).push(arguments)};\n            h._hjSettings={hjid:HOTJAR_ID,hjsv:6};\n            a=o.getElementsByTagName('head')[0];\n            r=o.createElement('script');r.async=1;\n            r.src=t+h._hjSettings.hjid+j+h._hjSettings.hjsv;\n            a.appendChild(r);\n          })(window,document,'https://static.hotjar.com/c/hotjar-','.js?sv=');\n        `}\n      </Script>\n\n      {/* Custom Telugu Gaming Tracking */}\n      <Script id=\"telugu-gaming-tracking\" strategy=\"afterInteractive\">\n        {`\n          // Track Telugu gaming specific events\n          function trackTeluguGamingEvent(eventName, properties = {}) {\n            // Google Analytics\n            if (typeof gtag !== 'undefined') {\n              gtag('event', eventName, {\n                event_category: 'Telugu Gaming',\n                event_label: 'TGRS',\n                ...properties\n              });\n            }\n            \n            // Facebook Pixel\n            if (typeof fbq !== 'undefined') {\n              fbq('trackCustom', eventName, {\n                server: 'tgrs',\n                language: 'telugu',\n                ...properties\n              });\n            }\n            \n            // Console log for debugging\n            console.log('Telugu Gaming Event:', eventName, properties);\n          }\n          \n          // Track page interactions\n          document.addEventListener('DOMContentLoaded', function() {\n            // Track Discord link clicks\n            document.querySelectorAll('a[href*=\"discord.gg\"]').forEach(link => {\n              link.addEventListener('click', () => {\n                trackTeluguGamingEvent('discord_click', {\n                  source: 'website',\n                  page: window.location.pathname\n                });\n              });\n            });\n            \n            // Track VIP interest\n            document.querySelectorAll('a[href*=\"/vip\"]').forEach(link => {\n              link.addEventListener('click', () => {\n                trackTeluguGamingEvent('vip_interest', {\n                  source: 'navigation',\n                  page: window.location.pathname\n                });\n              });\n            });\n            \n            // Track server join attempts\n            document.querySelectorAll('a[href*=\"cfx.re\"]').forEach(link => {\n              link.addEventListener('click', () => {\n                trackTeluguGamingEvent('server_join_attempt', {\n                  source: 'website',\n                  page: window.location.pathname\n                });\n              });\n            });\n          });\n          \n          // Track scroll depth for engagement\n          let maxScroll = 0;\n          window.addEventListener('scroll', () => {\n            const scrollPercent = Math.round((window.scrollY / (document.body.scrollHeight - window.innerHeight)) * 100);\n            if (scrollPercent > maxScroll && scrollPercent % 25 === 0) {\n              maxScroll = scrollPercent;\n              trackTeluguGamingEvent('scroll_depth', {\n                depth: scrollPercent,\n                page: window.location.pathname\n              });\n            }\n          });\n        `}\n      </Script>\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIe,SAAS;IACtB,qBACE;;0BAEE,8OAAC,8HAAA,CAAA,UAAM;gBACL,KAAI;gBACJ,UAAS;;;;;;0BAEX,8OAAC,8HAAA,CAAA,UAAM;gBAAC,IAAG;gBAAmB,UAAS;0BACpC,CAAC;;;;;;;;;;;;;;;;;;QAkBF,CAAC;;;;;;0BAIH,8OAAC,8HAAA,CAAA,UAAM;gBAAC,IAAG;gBAAoB,UAAS;0BACrC,CAAC;;;;;;QAMF,CAAC;;;;;;0BAIH,8OAAC,8HAAA,CAAA,UAAM;gBAAC,IAAG;gBAAiB,UAAS;0BAClC,CAAC;;;;;;;;;;;;;;;;;;QAkBF,CAAC;;;;;;0BAIH,8OAAC,8HAAA,CAAA,UAAM;gBAAC,IAAG;gBAAS,UAAS;0BAC1B,CAAC;;;;;;;;;QASF,CAAC;;;;;;0BAIH,8OAAC,8HAAA,CAAA,UAAM;gBAAC,IAAG;gBAAyB,UAAS;0BAC1C,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;QAsEF,CAAC;;;;;;;;AAIT"}}, {"offset": {"line": 203, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 209, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/TGRS%20Website/tgrs-website/src/components/MobileLiveCount.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\n\ninterface ServerData {\n  clients: number;\n  sv_maxclients: number;\n  svMaxclients: number;\n  hostname: string;\n}\n\ninterface ApiResponse {\n  Data: ServerData;\n}\n\nexport default function MobileLiveCount() {\n  const [playerCount, setPlayerCount] = useState<number | null>(null);\n  const [maxPlayers, setMaxPlayers] = useState<number>(48);\n  const [isLoading, setIsLoading] = useState(true);\n  const [error, setError] = useState(false);\n  const [isExpanded, setIsExpanded] = useState(false);\n  const [isVisible, setIsVisible] = useState(true);\n\n  const fetchPlayerCount = async () => {\n    try {\n      const response = await fetch('https://servers-frontend.fivem.net/api/servers/single/o57lj7');\n\n      if (!response.ok) {\n        throw new Error('Failed to fetch server data');\n      }\n\n      const data: ApiResponse = await response.json();\n\n      if (data.Data) {\n        setPlayerCount(data.Data.clients);\n        setMaxPlayers(data.Data.svMaxclients || data.Data.sv_maxclients);\n        setError(false);\n      } else {\n        throw new Error('Invalid server data');\n      }\n    } catch (err) {\n      console.error('Error fetching player count:', err);\n      setError(true);\n      setPlayerCount(null);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  useEffect(() => {\n    fetchPlayerCount();\n    const interval = setInterval(fetchPlayerCount, 30000);\n    return () => clearInterval(interval);\n  }, []);\n\n  if (isLoading) {\n    return (\n      <div className=\"md:hidden fixed top-20 left-4 right-4 z-40\">\n        <div className=\"bg-black/95 backdrop-blur-xl rounded-xl px-4 py-3 border border-neon-orange/40 shadow-lg shadow-neon-orange/20\">\n          <div className=\"flex items-center justify-center space-x-2\">\n            <div className=\"w-2 h-2 bg-gray-400 rounded-full animate-pulse\"></div>\n            <span className=\"text-white text-sm font-medium\">Loading Server Status...</span>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  if (error || playerCount === null) {\n    return (\n      <div className=\"md:hidden fixed top-20 left-4 right-4 z-40\">\n        <div className=\"bg-black/95 backdrop-blur-xl rounded-xl px-4 py-3 border border-red-500/40 shadow-lg shadow-red-500/20\">\n          <div className=\"flex items-center justify-center space-x-2\">\n            <div className=\"w-2 h-2 bg-red-500 rounded-full\"></div>\n            <span className=\"text-red-400 text-sm font-medium\">Server Offline</span>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  const isServerFull = playerCount >= maxPlayers;\n  const isHighPopulation = playerCount >= maxPlayers * 0.8;\n  const fillPercentage = (playerCount / maxPlayers) * 100;\n\n  return (\n    <div className=\"md:hidden fixed top-20 left-4 right-4 z-40\">\n      {/* Permanent Live Count Widget */}\n      <div\n        className={`relative transition-all duration-500 ease-out ${\n          isExpanded ? 'transform scale-105' : ''\n        }`}\n        onClick={() => setIsExpanded(!isExpanded)}\n      >\n        {/* Glow Effect */}\n        <div className={`absolute inset-0 rounded-2xl blur-lg transition-all duration-500 ${\n          isServerFull\n            ? 'bg-red-500/30'\n            : isHighPopulation\n              ? 'bg-yellow-500/30'\n              : 'bg-green-500/30'\n        } ${isExpanded ? 'scale-110 opacity-100' : 'scale-100 opacity-60'}`}></div>\n\n        {/* Main Widget */}\n        <div className={`relative bg-black/95 backdrop-blur-xl rounded-xl border transition-all duration-500 ${\n          isServerFull\n            ? 'border-red-500/60 shadow-lg shadow-red-500/20'\n            : isHighPopulation\n              ? 'border-yellow-500/60 shadow-lg shadow-yellow-500/20'\n              : 'border-green-500/60 shadow-lg shadow-green-500/20'\n        } ${isExpanded ? 'px-5 py-4' : 'px-4 py-3'}`}>\n\n          {/* Particle Effects */}\n          <div className=\"absolute inset-0 overflow-hidden rounded-2xl pointer-events-none\">\n            <div className={`absolute top-2 left-3 w-1 h-1 rounded-full animate-ping ${\n              isServerFull ? 'bg-red-400' : isHighPopulation ? 'bg-yellow-400' : 'bg-green-400'\n            }`}></div>\n            <div className={`absolute top-4 right-4 w-0.5 h-0.5 rounded-full animate-pulse ${\n              isServerFull ? 'bg-red-300' : isHighPopulation ? 'bg-yellow-300' : 'bg-green-300'\n            }`} style={{animationDelay: '0.5s'}}></div>\n            <div className={`absolute bottom-3 left-2 w-0.5 h-0.5 rounded-full animate-bounce ${\n              isServerFull ? 'bg-red-500' : isHighPopulation ? 'bg-yellow-500' : 'bg-green-500'\n            }`} style={{animationDelay: '0.3s'}}></div>\n          </div>\n\n          {/* Content */}\n          <div className=\"relative z-10\">\n            {!isExpanded ? (\n              /* Compact View */\n              <div className=\"flex items-center justify-between\">\n                <div className=\"flex items-center space-x-3\">\n                  {/* Status Indicator */}\n                  <div className={`w-3 h-3 rounded-full transition-all duration-300 ${\n                    isServerFull\n                      ? 'bg-red-500 animate-pulse'\n                      : isHighPopulation\n                        ? 'bg-yellow-500 animate-pulse'\n                        : 'bg-green-500 animate-pulse'\n                  }`}></div>\n\n                  {/* Server Info */}\n                  <div>\n                    <div className=\"flex items-center space-x-2\">\n                      <span className=\"text-white text-sm font-semibold\">TGRS Server</span>\n                      <span className={`text-xs px-2 py-0.5 rounded-full font-medium ${\n                        isServerFull\n                          ? 'bg-red-500/20 text-red-400 border border-red-500/30'\n                          : isHighPopulation\n                            ? 'bg-yellow-500/20 text-yellow-400 border border-yellow-500/30'\n                            : 'bg-green-500/20 text-green-400 border border-green-500/30'\n                      }`}>\n                        LIVE\n                      </span>\n                    </div>\n                  </div>\n                </div>\n\n                {/* Player Count */}\n                <div className=\"flex items-center space-x-2\">\n                  <div className=\"text-right\">\n                    <div className=\"flex items-baseline space-x-1\">\n                      <span className={`text-lg font-bold transition-colors duration-300 ${\n                        isServerFull\n                          ? 'text-red-400'\n                          : isHighPopulation\n                            ? 'text-yellow-400'\n                            : 'text-green-400'\n                      }`}>\n                        {playerCount}\n                      </span>\n                      <span className=\"text-gray-400 text-sm\">/{maxPlayers}</span>\n                    </div>\n                    <div className=\"flex items-center justify-end space-x-1\">\n                      <i className=\"fas fa-users text-gray-400 text-xs\"></i>\n                      <span className=\"text-gray-400 text-xs\">online</span>\n                    </div>\n                  </div>\n\n                  {/* Expand Indicator */}\n                  <div className=\"text-gray-400\">\n                    <i className=\"fas fa-chevron-down text-xs\"></i>\n                  </div>\n                </div>\n              </div>\n            ) : (\n              /* Expanded View */\n              <div className=\"space-y-3\">\n                {/* Header */}\n                <div className=\"flex items-center justify-between\">\n                  <div className=\"flex items-center space-x-2\">\n                    <div className={`w-3 h-3 rounded-full ${\n                      isServerFull\n                        ? 'bg-red-500 animate-pulse'\n                        : isHighPopulation\n                          ? 'bg-yellow-500 animate-pulse'\n                          : 'bg-green-500 animate-pulse'\n                    }`}></div>\n                    <span className=\"text-white text-sm font-semibold\">LIVE SERVER</span>\n                  </div>\n                  <i className=\"fas fa-chevron-up text-gray-400 text-xs\"></i>\n                </div>\n\n                {/* Player Count Large */}\n                <div className=\"text-center\">\n                  <div className=\"flex items-baseline justify-center space-x-1\">\n                    <span className={`text-2xl font-bold transition-colors duration-300 ${\n                      isServerFull\n                        ? 'text-red-400'\n                        : isHighPopulation\n                          ? 'text-yellow-400'\n                          : 'text-green-400'\n                    }`}>\n                      {playerCount}\n                    </span>\n                    <span className=\"text-gray-400 text-lg\">/{maxPlayers}</span>\n                  </div>\n                  <p className=\"text-gray-300 text-xs mt-1\">Players Online</p>\n                </div>\n\n                {/* Progress Bar */}\n                <div className=\"space-y-2\">\n                  <div className=\"w-full bg-gray-700 rounded-full h-2 overflow-hidden\">\n                    <div\n                      className={`h-full transition-all duration-1000 ease-out ${\n                        isServerFull\n                          ? 'bg-gradient-to-r from-red-500 to-red-400'\n                          : isHighPopulation\n                            ? 'bg-gradient-to-r from-yellow-500 to-yellow-400'\n                            : 'bg-gradient-to-r from-green-500 to-green-400'\n                      }`}\n                      style={{ width: `${fillPercentage}%` }}\n                    ></div>\n                  </div>\n                  <div className=\"flex justify-between text-xs text-gray-400\">\n                    <span>0</span>\n                    <span className={`font-medium ${\n                      isServerFull\n                        ? 'text-red-400'\n                        : isHighPopulation\n                          ? 'text-yellow-400'\n                          : 'text-green-400'\n                    }`}>\n                      {Math.round(fillPercentage)}% Full\n                    </span>\n                    <span>{maxPlayers}</span>\n                  </div>\n                </div>\n\n                {/* Status Text */}\n                <div className=\"text-center\">\n                  <span className={`text-xs font-medium ${\n                    isServerFull\n                      ? 'text-red-400'\n                      : isHighPopulation\n                        ? 'text-yellow-400'\n                        : 'text-green-400'\n                  }`}>\n                    {isServerFull\n                      ? '🔥 SERVER FULL'\n                      : isHighPopulation\n                        ? '⚡ HIGH ACTIVITY'\n                        : '✨ JOIN NOW'}\n                  </span>\n                </div>\n              </div>\n            )}\n          </div>\n        </div>\n\n\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAee,SAAS;IACtB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAC9D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IACrD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,mBAAmB;QACvB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAE7B,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,OAAoB,MAAM,SAAS,IAAI;YAE7C,IAAI,KAAK,IAAI,EAAE;gBACb,eAAe,KAAK,IAAI,CAAC,OAAO;gBAChC,cAAc,KAAK,IAAI,CAAC,YAAY,IAAI,KAAK,IAAI,CAAC,aAAa;gBAC/D,SAAS;YACX,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;QACF,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,SAAS;YACT,eAAe;QACjB,SAAU;YACR,aAAa;QACf;IACF;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;QACA,MAAM,WAAW,YAAY,kBAAkB;QAC/C,OAAO,IAAM,cAAc;IAC7B,GAAG,EAAE;IAEL,IAAI,WAAW;QACb,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAK,WAAU;sCAAiC;;;;;;;;;;;;;;;;;;;;;;IAK3D;IAEA,IAAI,SAAS,gBAAgB,MAAM;QACjC,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAK,WAAU;sCAAmC;;;;;;;;;;;;;;;;;;;;;;IAK7D;IAEA,MAAM,eAAe,eAAe;IACpC,MAAM,mBAAmB,eAAe,aAAa;IACrD,MAAM,iBAAiB,AAAC,cAAc,aAAc;IAEpD,qBACE,8OAAC;QAAI,WAAU;kBAEb,cAAA,8OAAC;YACC,WAAW,CAAC,8CAA8C,EACxD,aAAa,wBAAwB,IACrC;YACF,SAAS,IAAM,cAAc,CAAC;;8BAG9B,8OAAC;oBAAI,WAAW,CAAC,iEAAiE,EAChF,eACI,kBACA,mBACE,qBACA,kBACP,CAAC,EAAE,aAAa,0BAA0B,wBAAwB;;;;;;8BAGnE,8OAAC;oBAAI,WAAW,CAAC,oFAAoF,EACnG,eACI,kDACA,mBACE,wDACA,oDACP,CAAC,EAAE,aAAa,cAAc,aAAa;;sCAG1C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAW,CAAC,wDAAwD,EACvE,eAAe,eAAe,mBAAmB,kBAAkB,gBACnE;;;;;;8CACF,8OAAC;oCAAI,WAAW,CAAC,8DAA8D,EAC7E,eAAe,eAAe,mBAAmB,kBAAkB,gBACnE;oCAAE,OAAO;wCAAC,gBAAgB;oCAAM;;;;;;8CAClC,8OAAC;oCAAI,WAAW,CAAC,iEAAiE,EAChF,eAAe,eAAe,mBAAmB,kBAAkB,gBACnE;oCAAE,OAAO;wCAAC,gBAAgB;oCAAM;;;;;;;;;;;;sCAIpC,8OAAC;4BAAI,WAAU;sCACZ,CAAC,aACA,gBAAgB,iBAChB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DAEb,8OAAC;gDAAI,WAAW,CAAC,iDAAiD,EAChE,eACI,6BACA,mBACE,gCACA,8BACN;;;;;;0DAGF,8OAAC;0DACC,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAmC;;;;;;sEACnD,8OAAC;4DAAK,WAAW,CAAC,6CAA6C,EAC7D,eACI,wDACA,mBACE,iEACA,6DACN;sEAAE;;;;;;;;;;;;;;;;;;;;;;;kDAQV,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAW,CAAC,iDAAiD,EACjE,eACI,iBACA,mBACE,oBACA,kBACN;0EACC;;;;;;0EAEH,8OAAC;gEAAK,WAAU;;oEAAwB;oEAAE;;;;;;;;;;;;;kEAE5C,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAE,WAAU;;;;;;0EACb,8OAAC;gEAAK,WAAU;0EAAwB;;;;;;;;;;;;;;;;;;0DAK5C,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAE,WAAU;;;;;;;;;;;;;;;;;;;;;;uCAKnB,iBAAiB,iBACjB,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAW,CAAC,qBAAqB,EACpC,eACI,6BACA,mBACE,gCACA,8BACN;;;;;;kEACF,8OAAC;wDAAK,WAAU;kEAAmC;;;;;;;;;;;;0DAErD,8OAAC;gDAAE,WAAU;;;;;;;;;;;;kDAIf,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAW,CAAC,kDAAkD,EAClE,eACI,iBACA,mBACE,oBACA,kBACN;kEACC;;;;;;kEAEH,8OAAC;wDAAK,WAAU;;4DAAwB;4DAAE;;;;;;;;;;;;;0DAE5C,8OAAC;gDAAE,WAAU;0DAA6B;;;;;;;;;;;;kDAI5C,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDACC,WAAW,CAAC,6CAA6C,EACvD,eACI,6CACA,mBACE,mDACA,gDACN;oDACF,OAAO;wDAAE,OAAO,GAAG,eAAe,CAAC,CAAC;oDAAC;;;;;;;;;;;0DAGzC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;kEAAK;;;;;;kEACN,8OAAC;wDAAK,WAAW,CAAC,YAAY,EAC5B,eACI,iBACA,mBACE,oBACA,kBACN;;4DACC,KAAK,KAAK,CAAC;4DAAgB;;;;;;;kEAE9B,8OAAC;kEAAM;;;;;;;;;;;;;;;;;;kDAKX,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAK,WAAW,CAAC,oBAAoB,EACpC,eACI,iBACA,mBACE,oBACA,kBACN;sDACC,eACG,mBACA,mBACE,oBACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAY1B"}}, {"offset": {"line": 719, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 729, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/TGRS%20Website/tgrs-website/node_modules/next/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n    } else if (process.env.TURBOPACK) {\n      module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n    } else {\n      module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n    } else if (process.env.TURBOPACK) {\n      module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n    } else {\n      module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": ";AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAQzC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1CJ,OAAOC,OAAO,GAAGC,QAAQ;QAC3B,OAAO,IAAIL,QAAQC,GAAG,CAACO,SAAS,EAAE;;QAIlC;IACF;AACF", "ignoreList": [0]}}, {"offset": {"line": 743, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 748, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/TGRS%20Website/tgrs-website/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-ssr'\n].ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,0HAAyBC,QAAQ,CACxD,YACD,CAACC,kBAAkB", "ignoreList": [0]}}, {"offset": {"line": 750, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 755, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/TGRS%20Website/tgrs-website/node_modules/%40swc/helpers/cjs/_interop_require_default.cjs"], "sourcesContent": ["\"use strict\";\n\nfunction _interop_require_default(obj) {\n    return obj && obj.__esModule ? obj : { default: obj };\n}\nexports._ = _interop_require_default;\n"], "names": [], "mappings": "AAAA;AAEA,SAAS,yBAAyB,GAAG;IACjC,OAAO,OAAO,IAAI,UAAU,GAAG,MAAM;QAAE,SAAS;IAAI;AACxD;AACA,QAAQ,CAAC,GAAG", "ignoreList": [0]}}, {"offset": {"line": 762, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 767, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/TGRS%20Website/tgrs-website/node_modules/%40swc/helpers/cjs/_interop_require_wildcard.cjs"], "sourcesContent": ["\"use strict\";\n\nfunction _getRequireWildcardCache(nodeInterop) {\n    if (typeof WeakMap !== \"function\") return null;\n\n    var cacheBabelInterop = new WeakMap();\n    var cacheNodeInterop = new WeakMap();\n\n    return (_getRequireWildcardCache = function(nodeInterop) {\n        return nodeInterop ? cacheNodeInterop : cacheBabelInterop;\n    })(nodeInterop);\n}\nfunction _interop_require_wildcard(obj, nodeInterop) {\n    if (!nodeInterop && obj && obj.__esModule) return obj;\n    if (obj === null || typeof obj !== \"object\" && typeof obj !== \"function\") return { default: obj };\n\n    var cache = _getRequireWildcardCache(nodeInterop);\n\n    if (cache && cache.has(obj)) return cache.get(obj);\n\n    var newObj = { __proto__: null };\n    var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor;\n\n    for (var key in obj) {\n        if (key !== \"default\" && Object.prototype.hasOwnProperty.call(obj, key)) {\n            var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null;\n            if (desc && (desc.get || desc.set)) Object.defineProperty(newObj, key, desc);\n            else newObj[key] = obj[key];\n        }\n    }\n\n    newObj.default = obj;\n\n    if (cache) cache.set(obj, newObj);\n\n    return newObj;\n}\nexports._ = _interop_require_wildcard;\n"], "names": [], "mappings": "AAAA;AAEA,SAAS,yBAAyB,WAAW;IACzC,IAAI,OAAO,YAAY,YAAY,OAAO;IAE1C,IAAI,oBAAoB,IAAI;IAC5B,IAAI,mBAAmB,IAAI;IAE3B,OAAO,CAAC,2BAA2B,SAAS,WAAW;QACnD,OAAO,cAAc,mBAAmB;IAC5C,CAAC,EAAE;AACP;AACA,SAAS,0BAA0B,GAAG,EAAE,WAAW;IAC/C,IAAI,CAAC,eAAe,OAAO,IAAI,UAAU,EAAE,OAAO;IAClD,IAAI,QAAQ,QAAQ,OAAO,QAAQ,YAAY,OAAO,QAAQ,YAAY,OAAO;QAAE,SAAS;IAAI;IAEhG,IAAI,QAAQ,yBAAyB;IAErC,IAAI,SAAS,MAAM,GAAG,CAAC,MAAM,OAAO,MAAM,GAAG,CAAC;IAE9C,IAAI,SAAS;QAAE,WAAW;IAAK;IAC/B,IAAI,wBAAwB,OAAO,cAAc,IAAI,OAAO,wBAAwB;IAEpF,IAAK,IAAI,OAAO,IAAK;QACjB,IAAI,QAAQ,aAAa,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,MAAM;YACrE,IAAI,OAAO,wBAAwB,OAAO,wBAAwB,CAAC,KAAK,OAAO;YAC/E,IAAI,QAAQ,CAAC,KAAK,GAAG,IAAI,KAAK,GAAG,GAAG,OAAO,cAAc,CAAC,QAAQ,KAAK;iBAClE,MAAM,CAAC,IAAI,GAAG,GAAG,CAAC,IAAI;QAC/B;IACJ;IAEA,OAAO,OAAO,GAAG;IAEjB,IAAI,OAAO,MAAM,GAAG,CAAC,KAAK;IAE1B,OAAO;AACX;AACA,QAAQ,CAAC,GAAG", "ignoreList": [0]}}, {"offset": {"line": 799, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 804, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/TGRS%20Website/tgrs-website/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-ssr'\n].ReactJsxRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxRuntime"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,0HAAyBC,QAAQ,CACxD,YACD,CAACC,eAAe", "ignoreList": [0]}}, {"offset": {"line": 806, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 811, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/TGRS%20Website/tgrs-website/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-dom.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored['react-ssr'].ReactDOM\n"], "names": ["module", "exports", "require", "vendored", "ReactDOM"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,0HAAyBC,QAAQ,CAAC,YAAY,CAACC,QAAQ", "ignoreList": [0]}}, {"offset": {"line": 813, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 818, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/TGRS%20Website/tgrs-website/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored['react-ssr'].React\n"], "names": ["module", "exports", "require", "vendored", "React"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,0HAAyBC,QAAQ,CAAC,YAAY,CAACC,KAAK", "ignoreList": [0]}}, {"offset": {"line": 820, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 825, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/TGRS%20Website/tgrs-website/node_modules/next/src/server/route-modules/app-page/vendored/contexts/head-manager-context.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'contexts'\n].HeadManagerContext\n"], "names": ["module", "exports", "require", "vendored", "HeadManagerContext"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,0HAAyBC,QAAQ,CACxD,WACD,CAACC,kBAAkB", "ignoreList": [0]}}, {"offset": {"line": 827, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 832, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/TGRS%20Website/tgrs-website/node_modules/next/src/client/set-attributes-from-props.ts"], "sourcesContent": ["const DOMAttributeNames: Record<string, string> = {\n  acceptCharset: 'accept-charset',\n  className: 'class',\n  htmlFor: 'for',\n  httpEquiv: 'http-equiv',\n  noModule: 'noModule',\n}\n\nconst ignoreProps = [\n  'onLoad',\n  'onReady',\n  'dangerouslySetInnerHTML',\n  'children',\n  'onError',\n  'strategy',\n  'stylesheets',\n]\n\nfunction isBooleanScriptAttribute(\n  attr: string\n): attr is 'async' | 'defer' | 'noModule' {\n  return ['async', 'defer', 'noModule'].includes(attr)\n}\n\nexport function setAttributesFromProps(el: HTMLElement, props: object) {\n  for (const [p, value] of Object.entries(props)) {\n    if (!props.hasOwnProperty(p)) continue\n    if (ignoreProps.includes(p)) continue\n\n    // we don't render undefined props to the DOM\n    if (value === undefined) {\n      continue\n    }\n\n    const attr = DOMAttributeNames[p] || p.toLowerCase()\n\n    if (el.tagName === 'SCRIPT' && isBooleanScriptAttribute(attr)) {\n      // Correctly assign boolean script attributes\n      // https://github.com/vercel/next.js/pull/20748\n      ;(el as HTMLScriptElement)[attr] = !!value\n    } else {\n      el.setAttribute(attr, String(value))\n    }\n\n    // Remove falsy non-zero boolean attributes so they are correctly interpreted\n    // (e.g. if we set them to false, this coerces to the string \"false\", which the browser interprets as true)\n    if (\n      value === false ||\n      (el.tagName === 'SCRIPT' &&\n        isBooleanScriptAttribute(attr) &&\n        (!value || value === 'false'))\n    ) {\n      // Call setAttribute before, as we need to set and unset the attribute to override force async:\n      // https://html.spec.whatwg.org/multipage/scripting.html#script-force-async\n      el.setAttribute(attr, '')\n      el.removeAttribute(attr)\n    }\n  }\n}\n"], "names": ["setAttributesFromProps", "DOMAttributeNames", "acceptCharset", "className", "htmlFor", "httpEquiv", "noModule", "ignoreProps", "isBooleanScriptAttribute", "attr", "includes", "el", "props", "p", "value", "Object", "entries", "hasOwnProperty", "undefined", "toLowerCase", "tagName", "setAttribute", "String", "removeAttribute"], "mappings": ";;;;+BAwBgBA,0BAAAA;;;eAAAA;;;AAxBhB,MAAMC,oBAA4C;IAChDC,eAAe;IACfC,WAAW;IACXC,SAAS;IACTC,WAAW;IACXC,UAAU;AACZ;AAEA,MAAMC,cAAc;IAClB;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED,SAASC,yBACPC,IAAY;IAEZ,OAAO;QAAC;QAAS;QAAS;KAAW,CAACC,QAAQ,CAACD;AACjD;AAEO,SAAST,uBAAuBW,EAAe,EAAEC,KAAa;IACnE,KAAK,MAAM,CAACC,GAAGC,MAAM,IAAIC,OAAOC,OAAO,CAACJ,OAAQ;QAC9C,IAAI,CAACA,MAAMK,cAAc,CAACJ,IAAI;QAC9B,IAAIN,YAAYG,QAAQ,CAACG,IAAI;QAE7B,6CAA6C;QAC7C,IAAIC,UAAUI,WAAW;YACvB;QACF;QAEA,MAAMT,OAAOR,iBAAiB,CAACY,EAAE,IAAIA,EAAEM,WAAW;QAElD,IAAIR,GAAGS,OAAO,KAAK,YAAYZ,yBAAyBC,OAAO;YAC7D,6CAA6C;YAC7C,+CAA+C;;YAC7CE,EAAwB,CAACF,KAAK,GAAG,CAAC,CAACK;QACvC,OAAO;YACLH,GAAGU,YAAY,CAACZ,MAAMa,OAAOR;QAC/B;QAEA,6EAA6E;QAC7E,2GAA2G;QAC3G,IACEA,UAAU,SACTH,GAAGS,OAAO,KAAK,YACdZ,yBAAyBC,SACxB,CAAA,CAACK,SAASA,UAAU,OAAM,GAC7B;YACA,+FAA+F;YAC/F,2EAA2E;YAC3EH,GAAGU,YAAY,CAACZ,MAAM;YACtBE,GAAGY,eAAe,CAACd;QACrB;IACF;AACF", "ignoreList": [0]}}, {"offset": {"line": 899, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 904, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/TGRS%20Website/tgrs-website/node_modules/next/src/client/request-idle-callback.ts"], "sourcesContent": ["export const requestIdleCallback =\n  (typeof self !== 'undefined' &&\n    self.requestIdleCallback &&\n    self.requestIdleCallback.bind(window)) ||\n  function (cb: IdleRequestCallback): number {\n    let start = Date.now()\n    return self.setTimeout(function () {\n      cb({\n        didTimeout: false,\n        timeRemaining: function () {\n          return Math.max(0, 50 - (Date.now() - start))\n        },\n      })\n    }, 1)\n  }\n\nexport const cancelIdleCallback =\n  (typeof self !== 'undefined' &&\n    self.cancelIdleCallback &&\n    self.cancelIdleCallback.bind(window)) ||\n  function (id: number) {\n    return clearTimeout(id)\n  }\n"], "names": ["cancelIdleCallback", "requestIdleCallback", "self", "bind", "window", "cb", "start", "Date", "now", "setTimeout", "didTimeout", "timeRemaining", "Math", "max", "id", "clearTimeout"], "mappings": ";;;;;;;;;;;;;;;IAgBaA,kBAAkB,EAAA;eAAlBA;;IAhBAC,mBAAmB,EAAA;eAAnBA;;;AAAN,MAAMA,sBACV,OAAOC,SAAS,eACfA,KAAKD,mBAAmB,IACxBC,KAAKD,mBAAmB,CAACE,IAAI,CAACC,WAChC,SAAUC,EAAuB;IAC/B,IAAIC,QAAQC,KAAKC,GAAG;IACpB,OAAON,KAAKO,UAAU,CAAC;QACrBJ,GAAG;YACDK,YAAY;YACZC,eAAe;gBACb,OAAOC,KAAKC,GAAG,CAAC,GAAG,KAAMN,CAAAA,KAAKC,GAAG,KAAKF,KAAI;YAC5C;QACF;IACF,GAAG;AACL;AAEK,MAAMN,qBACV,OAAOE,SAAS,eACfA,KAAKF,kBAAkB,IACvBE,KAAKF,kBAAkB,CAACG,IAAI,CAACC,WAC/B,SAAUU,EAAU;IAClB,OAAOC,aAAaD;AACtB", "ignoreList": [0]}}, {"offset": {"line": 947, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 952, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/TGRS%20Website/tgrs-website/node_modules/next/src/client/script.tsx"], "sourcesContent": ["'use client'\n\nimport ReactDOM from 'react-dom'\nimport React, { useEffect, useContext, useRef, type JSX } from 'react'\nimport type { ScriptHTMLAttributes } from 'react'\nimport { HeadManagerContext } from '../shared/lib/head-manager-context.shared-runtime'\nimport { setAttributesFromProps } from './set-attributes-from-props'\nimport { requestIdleCallback } from './request-idle-callback'\n\nconst ScriptCache = new Map()\nconst LoadCache = new Set()\n\nexport interface ScriptProps extends ScriptHTMLAttributes<HTMLScriptElement> {\n  strategy?: 'afterInteractive' | 'lazyOnload' | 'beforeInteractive' | 'worker'\n  id?: string\n  onLoad?: (e: any) => void\n  onReady?: () => void | null\n  onError?: (e: any) => void\n  children?: React.ReactNode\n  stylesheets?: string[]\n}\n\n/**\n * @deprecated Use `ScriptProps` instead.\n */\nexport type Props = ScriptProps\n\nconst insertStylesheets = (stylesheets: string[]) => {\n  // Case 1: Styles for afterInteractive/lazyOnload with appDir injected via handleClientScriptLoad\n  //\n  // Using ReactDOM.preinit to feature detect appDir and inject styles\n  // Stylesheets might have already been loaded if initialized with Script component\n  // Re-inject styles here to handle scripts loaded via handleClientScriptLoad\n  // ReactDOM.preinit handles dedup and ensures the styles are loaded only once\n  if (ReactDOM.preinit) {\n    stylesheets.forEach((stylesheet: string) => {\n      ReactDOM.preinit(stylesheet, { as: 'style' })\n    })\n\n    return\n  }\n\n  // Case 2: Styles for afterInteractive/lazyOnload with pages injected via handleClientScriptLoad\n  //\n  // We use this function to load styles when appdir is not detected\n  // TODO: Use React float APIs to load styles once available for pages dir\n  if (typeof window !== 'undefined') {\n    let head = document.head\n    stylesheets.forEach((stylesheet: string) => {\n      let link = document.createElement('link')\n\n      link.type = 'text/css'\n      link.rel = 'stylesheet'\n      link.href = stylesheet\n\n      head.appendChild(link)\n    })\n  }\n}\n\nconst loadScript = (props: ScriptProps): void => {\n  const {\n    src,\n    id,\n    onLoad = () => {},\n    onReady = null,\n    dangerouslySetInnerHTML,\n    children = '',\n    strategy = 'afterInteractive',\n    onError,\n    stylesheets,\n  } = props\n\n  const cacheKey = id || src\n\n  // Script has already loaded\n  if (cacheKey && LoadCache.has(cacheKey)) {\n    return\n  }\n\n  // Contents of this script are already loading/loaded\n  if (ScriptCache.has(src)) {\n    LoadCache.add(cacheKey)\n    // It is possible that multiple `next/script` components all have same \"src\", but has different \"onLoad\"\n    // This is to make sure the same remote script will only load once, but \"onLoad\" are executed in order\n    ScriptCache.get(src).then(onLoad, onError)\n    return\n  }\n\n  /** Execute after the script first loaded */\n  const afterLoad = () => {\n    // Run onReady for the first time after load event\n    if (onReady) {\n      onReady()\n    }\n    // add cacheKey to LoadCache when load successfully\n    LoadCache.add(cacheKey)\n  }\n\n  const el = document.createElement('script')\n\n  const loadPromise = new Promise<void>((resolve, reject) => {\n    el.addEventListener('load', function (e) {\n      resolve()\n      if (onLoad) {\n        onLoad.call(this, e)\n      }\n      afterLoad()\n    })\n    el.addEventListener('error', function (e) {\n      reject(e)\n    })\n  }).catch(function (e) {\n    if (onError) {\n      onError(e)\n    }\n  })\n\n  if (dangerouslySetInnerHTML) {\n    // Casting since lib.dom.d.ts doesn't have TrustedHTML yet.\n    el.innerHTML = (dangerouslySetInnerHTML.__html as string) || ''\n\n    afterLoad()\n  } else if (children) {\n    el.textContent =\n      typeof children === 'string'\n        ? children\n        : Array.isArray(children)\n          ? children.join('')\n          : ''\n\n    afterLoad()\n  } else if (src) {\n    el.src = src\n    // do not add cacheKey into LoadCache for remote script here\n    // cacheKey will be added to LoadCache when it is actually loaded (see loadPromise above)\n\n    ScriptCache.set(src, loadPromise)\n  }\n\n  setAttributesFromProps(el, props)\n\n  if (strategy === 'worker') {\n    el.setAttribute('type', 'text/partytown')\n  }\n\n  el.setAttribute('data-nscript', strategy)\n\n  // Load styles associated with this script\n  if (stylesheets) {\n    insertStylesheets(stylesheets)\n  }\n\n  document.body.appendChild(el)\n}\n\nexport function handleClientScriptLoad(props: ScriptProps) {\n  const { strategy = 'afterInteractive' } = props\n  if (strategy === 'lazyOnload') {\n    window.addEventListener('load', () => {\n      requestIdleCallback(() => loadScript(props))\n    })\n  } else {\n    loadScript(props)\n  }\n}\n\nfunction loadLazyScript(props: ScriptProps) {\n  if (document.readyState === 'complete') {\n    requestIdleCallback(() => loadScript(props))\n  } else {\n    window.addEventListener('load', () => {\n      requestIdleCallback(() => loadScript(props))\n    })\n  }\n}\n\nfunction addBeforeInteractiveToCache() {\n  const scripts = [\n    ...document.querySelectorAll('[data-nscript=\"beforeInteractive\"]'),\n    ...document.querySelectorAll('[data-nscript=\"beforePageRender\"]'),\n  ]\n  scripts.forEach((script) => {\n    const cacheKey = script.id || script.getAttribute('src')\n    LoadCache.add(cacheKey)\n  })\n}\n\nexport function initScriptLoader(scriptLoaderItems: ScriptProps[]) {\n  scriptLoaderItems.forEach(handleClientScriptLoad)\n  addBeforeInteractiveToCache()\n}\n\n/**\n * Load a third-party scripts in an optimized way.\n *\n * Read more: [Next.js Docs: `next/script`](https://nextjs.org/docs/app/api-reference/components/script)\n */\nfunction Script(props: ScriptProps): JSX.Element | null {\n  const {\n    id,\n    src = '',\n    onLoad = () => {},\n    onReady = null,\n    strategy = 'afterInteractive',\n    onError,\n    stylesheets,\n    ...restProps\n  } = props\n\n  // Context is available only during SSR\n  const { updateScripts, scripts, getIsSsr, appDir, nonce } =\n    useContext(HeadManagerContext)\n\n  /**\n   * - First mount:\n   *   1. The useEffect for onReady executes\n   *   2. hasOnReadyEffectCalled.current is false, but the script hasn't loaded yet (not in LoadCache)\n   *      onReady is skipped, set hasOnReadyEffectCalled.current to true\n   *   3. The useEffect for loadScript executes\n   *   4. hasLoadScriptEffectCalled.current is false, loadScript executes\n   *      Once the script is loaded, the onLoad and onReady will be called by then\n   *   [If strict mode is enabled / is wrapped in <OffScreen /> component]\n   *   5. The useEffect for onReady executes again\n   *   6. hasOnReadyEffectCalled.current is true, so entire effect is skipped\n   *   7. The useEffect for loadScript executes again\n   *   8. hasLoadScriptEffectCalled.current is true, so entire effect is skipped\n   *\n   * - Second mount:\n   *   1. The useEffect for onReady executes\n   *   2. hasOnReadyEffectCalled.current is false, but the script has already loaded (found in LoadCache)\n   *      onReady is called, set hasOnReadyEffectCalled.current to true\n   *   3. The useEffect for loadScript executes\n   *   4. The script is already loaded, loadScript bails out\n   *   [If strict mode is enabled / is wrapped in <OffScreen /> component]\n   *   5. The useEffect for onReady executes again\n   *   6. hasOnReadyEffectCalled.current is true, so entire effect is skipped\n   *   7. The useEffect for loadScript executes again\n   *   8. hasLoadScriptEffectCalled.current is true, so entire effect is skipped\n   */\n  const hasOnReadyEffectCalled = useRef(false)\n\n  useEffect(() => {\n    const cacheKey = id || src\n    if (!hasOnReadyEffectCalled.current) {\n      // Run onReady if script has loaded before but component is re-mounted\n      if (onReady && cacheKey && LoadCache.has(cacheKey)) {\n        onReady()\n      }\n\n      hasOnReadyEffectCalled.current = true\n    }\n  }, [onReady, id, src])\n\n  const hasLoadScriptEffectCalled = useRef(false)\n\n  useEffect(() => {\n    if (!hasLoadScriptEffectCalled.current) {\n      if (strategy === 'afterInteractive') {\n        loadScript(props)\n      } else if (strategy === 'lazyOnload') {\n        loadLazyScript(props)\n      }\n\n      hasLoadScriptEffectCalled.current = true\n    }\n  }, [props, strategy])\n\n  if (strategy === 'beforeInteractive' || strategy === 'worker') {\n    if (updateScripts) {\n      scripts[strategy] = (scripts[strategy] || []).concat([\n        {\n          id,\n          src,\n          onLoad,\n          onReady,\n          onError,\n          ...restProps,\n        },\n      ])\n      updateScripts(scripts)\n    } else if (getIsSsr && getIsSsr()) {\n      // Script has already loaded during SSR\n      LoadCache.add(id || src)\n    } else if (getIsSsr && !getIsSsr()) {\n      loadScript(props)\n    }\n  }\n\n  // For the app directory, we need React Float to preload these scripts.\n  if (appDir) {\n    // Injecting stylesheets here handles beforeInteractive and worker scripts correctly\n    // For other strategies injecting here ensures correct stylesheet order\n    // ReactDOM.preinit handles loading the styles in the correct order,\n    // also ensures the stylesheet is loaded only once and in a consistent manner\n    //\n    // Case 1: Styles for beforeInteractive/worker with appDir - handled here\n    // Case 2: Styles for beforeInteractive/worker with pages dir - Not handled yet\n    // Case 3: Styles for afterInteractive/lazyOnload with appDir - handled here\n    // Case 4: Styles for afterInteractive/lazyOnload with pages dir - handled in insertStylesheets function\n    if (stylesheets) {\n      stylesheets.forEach((styleSrc) => {\n        ReactDOM.preinit(styleSrc, { as: 'style' })\n      })\n    }\n\n    // Before interactive scripts need to be loaded by Next.js' runtime instead\n    // of native <script> tags, because they no longer have `defer`.\n    if (strategy === 'beforeInteractive') {\n      if (!src) {\n        // For inlined scripts, we put the content in `children`.\n        if (restProps.dangerouslySetInnerHTML) {\n          // Casting since lib.dom.d.ts doesn't have TrustedHTML yet.\n          restProps.children = restProps.dangerouslySetInnerHTML\n            .__html as string\n          delete restProps.dangerouslySetInnerHTML\n        }\n\n        return (\n          <script\n            nonce={nonce}\n            dangerouslySetInnerHTML={{\n              __html: `(self.__next_s=self.__next_s||[]).push(${JSON.stringify([\n                0,\n                { ...restProps, id },\n              ])})`,\n            }}\n          />\n        )\n      } else {\n        // @ts-ignore\n        ReactDOM.preload(\n          src,\n          restProps.integrity\n            ? {\n                as: 'script',\n                integrity: restProps.integrity,\n                nonce,\n                crossOrigin: restProps.crossOrigin,\n              }\n            : { as: 'script', nonce, crossOrigin: restProps.crossOrigin }\n        )\n        return (\n          <script\n            nonce={nonce}\n            dangerouslySetInnerHTML={{\n              __html: `(self.__next_s=self.__next_s||[]).push(${JSON.stringify([\n                src,\n                { ...restProps, id },\n              ])})`,\n            }}\n          />\n        )\n      }\n    } else if (strategy === 'afterInteractive') {\n      if (src) {\n        // @ts-ignore\n        ReactDOM.preload(\n          src,\n          restProps.integrity\n            ? {\n                as: 'script',\n                integrity: restProps.integrity,\n                nonce,\n                crossOrigin: restProps.crossOrigin,\n              }\n            : { as: 'script', nonce, crossOrigin: restProps.crossOrigin }\n        )\n      }\n    }\n  }\n\n  return null\n}\n\nObject.defineProperty(Script, '__nextScript', { value: true })\n\nexport default Script\n"], "names": ["handleClientScriptLoad", "initScriptLoader", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Map", "Load<PERSON>ache", "Set", "insertStylesheets", "stylesheets", "ReactDOM", "preinit", "for<PERSON>ach", "stylesheet", "as", "window", "head", "document", "link", "createElement", "type", "rel", "href", "append<PERSON><PERSON><PERSON>", "loadScript", "props", "src", "id", "onLoad", "onReady", "dangerouslySetInnerHTML", "children", "strategy", "onError", "cache<PERSON>ey", "has", "add", "get", "then", "afterLoad", "el", "loadPromise", "Promise", "resolve", "reject", "addEventListener", "e", "call", "catch", "innerHTML", "__html", "textContent", "Array", "isArray", "join", "set", "setAttributesFromProps", "setAttribute", "body", "requestIdleCallback", "loadLazyScript", "readyState", "addBeforeInteractiveToCache", "scripts", "querySelectorAll", "script", "getAttribute", "scriptLoaderItems", "<PERSON><PERSON><PERSON>", "restProps", "updateScripts", "getIsSsr", "appDir", "nonce", "useContext", "HeadManagerContext", "hasOnReadyEffectCalled", "useRef", "useEffect", "current", "hasLoadScriptEffectCalled", "concat", "styleSrc", "JSON", "stringify", "preload", "integrity", "crossOrigin", "Object", "defineProperty", "value"], "mappings": "AAAA;;;;;;;;;;;;;;;;;IAyXA,OAAqB,EAAA;eAArB;;IA7NgBA,sBAAsB,EAAA;eAAtBA;;IAgCAC,gBAAgB,EAAA;eAAhBA;;;;;;mEA1LK;iEAC0C;iDAE5B;wCACI;qCACH;AAEpC,MAAMC,cAAc,IAAIC;AACxB,MAAMC,YAAY,IAAIC;AAiBtB,MAAMC,oBAAoB,CAACC;IACzB,iGAAiG;IACjG,EAAE;IACF,oEAAoE;IACpE,kFAAkF;IAClF,4EAA4E;IAC5E,6EAA6E;IAC7E,IAAIC,UAAAA,OAAQ,CAACC,OAAO,EAAE;QACpBF,YAAYG,OAAO,CAAC,CAACC;YACnBH,UAAAA,OAAQ,CAACC,OAAO,CAACE,YAAY;gBAAEC,IAAI;YAAQ;QAC7C;QAEA;IACF;IAEA,gGAAgG;IAChG,EAAE;IACF,kEAAkE;IAClE,yEAAyE;IACzE,IAAI,OAAOC,WAAW,aAAa;QACjC,IAAIC,OAAOC,SAASD,IAAI;QACxBP,YAAYG,OAAO,CAAC,CAACC;YACnB,IAAIK,OAAOD,SAASE,aAAa,CAAC;YAElCD,KAAKE,IAAI,GAAG;YACZF,KAAKG,GAAG,GAAG;YACXH,KAAKI,IAAI,GAAGT;YAEZG,KAAKO,WAAW,CAACL;QACnB;IACF;AACF;AAEA,MAAMM,aAAa,CAACC;IAClB,MAAM,EACJC,GAAG,EACHC,EAAE,EACFC,SAAS,KAAO,CAAC,EACjBC,UAAU,IAAI,EACdC,uBAAuB,EACvBC,WAAW,EAAE,EACbC,WAAW,kBAAkB,EAC7BC,OAAO,EACPxB,WAAW,EACZ,GAAGgB;IAEJ,MAAMS,WAAWP,MAAMD;IAEvB,4BAA4B;IAC5B,IAAIQ,YAAY5B,UAAU6B,GAAG,CAACD,WAAW;QACvC;IACF;IAEA,qDAAqD;IACrD,IAAI9B,YAAY+B,GAAG,CAACT,MAAM;QACxBpB,UAAU8B,GAAG,CAACF;QACd,wGAAwG;QACxG,sGAAsG;QACtG9B,YAAYiC,GAAG,CAACX,KAAKY,IAAI,CAACV,QAAQK;QAClC;IACF;IAEA,0CAA0C,GAC1C,MAAMM,YAAY;QAChB,kDAAkD;QAClD,IAAIV,SAAS;YACXA;QACF;QACA,mDAAmD;QACnDvB,UAAU8B,GAAG,CAACF;IAChB;IAEA,MAAMM,KAAKvB,SAASE,aAAa,CAAC;IAElC,MAAMsB,cAAc,IAAIC,QAAc,CAACC,SAASC;QAC9CJ,GAAGK,gBAAgB,CAAC,QAAQ,SAAUC,CAAC;YACrCH;YACA,IAAIf,QAAQ;gBACVA,OAAOmB,IAAI,CAAC,IAAI,EAAED;YACpB;YACAP;QACF;QACAC,GAAGK,gBAAgB,CAAC,SAAS,SAAUC,CAAC;YACtCF,OAAOE;QACT;IACF,GAAGE,KAAK,CAAC,SAAUF,CAAC;QAClB,IAAIb,SAAS;YACXA,QAAQa;QACV;IACF;IAEA,IAAIhB,yBAAyB;QAC3B,2DAA2D;QAC3DU,GAAGS,SAAS,GAAInB,wBAAwBoB,MAAM,IAAe;QAE7DX;IACF,OAAO,IAAIR,UAAU;QACnBS,GAAGW,WAAW,GACZ,OAAOpB,aAAa,WAChBA,WACAqB,MAAMC,OAAO,CAACtB,YACZA,SAASuB,IAAI,CAAC,MACd;QAERf;IACF,OAAO,IAAIb,KAAK;QACdc,GAAGd,GAAG,GAAGA;QACT,4DAA4D;QAC5D,yFAAyF;QAEzFtB,YAAYmD,GAAG,CAAC7B,KAAKe;IACvB;IAEAe,CAAAA,GAAAA,wBAAAA,sBAAsB,EAAChB,IAAIf;IAE3B,IAAIO,aAAa,UAAU;QACzBQ,GAAGiB,YAAY,CAAC,QAAQ;IAC1B;IAEAjB,GAAGiB,YAAY,CAAC,gBAAgBzB;IAEhC,0CAA0C;IAC1C,IAAIvB,aAAa;QACfD,kBAAkBC;IACpB;IAEAQ,SAASyC,IAAI,CAACnC,WAAW,CAACiB;AAC5B;AAEO,SAAStC,uBAAuBuB,KAAkB;IACvD,MAAM,EAAEO,WAAW,kBAAkB,EAAE,GAAGP;IAC1C,IAAIO,aAAa,cAAc;QAC7BjB,OAAO8B,gBAAgB,CAAC,QAAQ;YAC9Bc,CAAAA,GAAAA,qBAAAA,mBAAmB,EAAC,IAAMnC,WAAWC;QACvC;IACF,OAAO;QACLD,WAAWC;IACb;AACF;AAEA,SAASmC,eAAenC,KAAkB;IACxC,IAAIR,SAAS4C,UAAU,KAAK,YAAY;QACtCF,CAAAA,GAAAA,qBAAAA,mBAAmB,EAAC,IAAMnC,WAAWC;IACvC,OAAO;QACLV,OAAO8B,gBAAgB,CAAC,QAAQ;YAC9Bc,CAAAA,GAAAA,qBAAAA,mBAAmB,EAAC,IAAMnC,WAAWC;QACvC;IACF;AACF;AAEA,SAASqC;IACP,MAAMC,UAAU;WACX9C,SAAS+C,gBAAgB,CAAC;WAC1B/C,SAAS+C,gBAAgB,CAAC;KAC9B;IACDD,QAAQnD,OAAO,CAAC,CAACqD;QACf,MAAM/B,WAAW+B,OAAOtC,EAAE,IAAIsC,OAAOC,YAAY,CAAC;QAClD5D,UAAU8B,GAAG,CAACF;IAChB;AACF;AAEO,SAAS/B,iBAAiBgE,iBAAgC;IAC/DA,kBAAkBvD,OAAO,CAACV;IAC1B4D;AACF;AAEA;;;;CAIC,GACD,SAASM,OAAO3C,KAAkB;IAChC,MAAM,EACJE,EAAE,EACFD,MAAM,EAAE,EACRE,SAAS,KAAO,CAAC,EACjBC,UAAU,IAAI,EACdG,WAAW,kBAAkB,EAC7BC,OAAO,EACPxB,WAAW,EACX,GAAG4D,WACJ,GAAG5C;IAEJ,uCAAuC;IACvC,MAAM,EAAE6C,aAAa,EAAEP,OAAO,EAAEQ,QAAQ,EAAEC,MAAM,EAAEC,KAAK,EAAE,GACvDC,CAAAA,GAAAA,OAAAA,UAAU,EAACC,iCAAAA,kBAAkB;IAE/B;;;;;;;;;;;;;;;;;;;;;;;;;GAyBC,GACD,MAAMC,yBAAyBC,CAAAA,GAAAA,OAAAA,MAAM,EAAC;IAEtCC,CAAAA,GAAAA,OAAAA,SAAS,EAAC;QACR,MAAM5C,WAAWP,MAAMD;QACvB,IAAI,CAACkD,uBAAuBG,OAAO,EAAE;YACnC,sEAAsE;YACtE,IAAIlD,WAAWK,YAAY5B,UAAU6B,GAAG,CAACD,WAAW;gBAClDL;YACF;YAEA+C,uBAAuBG,OAAO,GAAG;QACnC;IACF,GAAG;QAAClD;QAASF;QAAID;KAAI;IAErB,MAAMsD,4BAA4BH,CAAAA,GAAAA,OAAAA,MAAM,EAAC;IAEzCC,CAAAA,GAAAA,OAAAA,SAAS,EAAC;QACR,IAAI,CAACE,0BAA0BD,OAAO,EAAE;YACtC,IAAI/C,aAAa,oBAAoB;gBACnCR,WAAWC;YACb,OAAO,IAAIO,aAAa,cAAc;gBACpC4B,eAAenC;YACjB;YAEAuD,0BAA0BD,OAAO,GAAG;QACtC;IACF,GAAG;QAACtD;QAAOO;KAAS;IAEpB,IAAIA,aAAa,uBAAuBA,aAAa,UAAU;QAC7D,IAAIsC,eAAe;YACjBP,OAAO,CAAC/B,SAAS,GAAI+B,CAAAA,OAAO,CAAC/B,SAAS,IAAI,EAAC,EAAGiD,MAAM,CAAC;gBACnD;oBACEtD;oBACAD;oBACAE;oBACAC;oBACAI;oBACA,GAAGoC,SAAS;gBACd;aACD;YACDC,cAAcP;QAChB,OAAO,IAAIQ,YAAYA,YAAY;YACjC,uCAAuC;YACvCjE,UAAU8B,GAAG,CAACT,MAAMD;QACtB,OAAO,IAAI6C,YAAY,CAACA,YAAY;YAClC/C,WAAWC;QACb;IACF;IAEA,uEAAuE;IACvE,IAAI+C,QAAQ;QACV,oFAAoF;QACpF,uEAAuE;QACvE,oEAAoE;QACpE,6EAA6E;QAC7E,EAAE;QACF,yEAAyE;QACzE,+EAA+E;QAC/E,4EAA4E;QAC5E,wGAAwG;QACxG,IAAI/D,aAAa;YACfA,YAAYG,OAAO,CAAC,CAACsE;gBACnBxE,UAAAA,OAAQ,CAACC,OAAO,CAACuE,UAAU;oBAAEpE,IAAI;gBAAQ;YAC3C;QACF;QAEA,2EAA2E;QAC3E,gEAAgE;QAChE,IAAIkB,aAAa,qBAAqB;YACpC,IAAI,CAACN,KAAK;gBACR,yDAAyD;gBACzD,IAAI2C,UAAUvC,uBAAuB,EAAE;oBACrC,2DAA2D;oBAC3DuC,UAAUtC,QAAQ,GAAGsC,UAAUvC,uBAAuB,CACnDoB,MAAM;oBACT,OAAOmB,UAAUvC,uBAAuB;gBAC1C;gBAEA,OAAA,WAAA,GACE,CAAA,GAAA,YAAA,GAAA,EAACmC,UAAAA;oBACCQ,OAAOA;oBACP3C,yBAAyB;wBACvBoB,QAAS,4CAAyCiC,KAAKC,SAAS,CAAC;4BAC/D;4BACA;gCAAE,GAAGf,SAAS;gCAAE1C;4BAAG;yBACpB,IAAE;oBACL;;YAGN,OAAO;gBACL,aAAa;gBACbjB,UAAAA,OAAQ,CAAC2E,OAAO,CACd3D,KACA2C,UAAUiB,SAAS,GACf;oBACExE,IAAI;oBACJwE,WAAWjB,UAAUiB,SAAS;oBAC9Bb;oBACAc,aAAalB,UAAUkB,WAAW;gBACpC,IACA;oBAAEzE,IAAI;oBAAU2D;oBAAOc,aAAalB,UAAUkB,WAAW;gBAAC;gBAEhE,OAAA,WAAA,GACE,CAAA,GAAA,YAAA,GAAA,EAACtB,UAAAA;oBACCQ,OAAOA;oBACP3C,yBAAyB;wBACvBoB,QAAS,4CAAyCiC,KAAKC,SAAS,CAAC;4BAC/D1D;4BACA;gCAAE,GAAG2C,SAAS;gCAAE1C;4BAAG;yBACpB,IAAE;oBACL;;YAGN;QACF,OAAO,IAAIK,aAAa,oBAAoB;YAC1C,IAAIN,KAAK;gBACP,aAAa;gBACbhB,UAAAA,OAAQ,CAAC2E,OAAO,CACd3D,KACA2C,UAAUiB,SAAS,GACf;oBACExE,IAAI;oBACJwE,WAAWjB,UAAUiB,SAAS;oBAC9Bb;oBACAc,aAAalB,UAAUkB,WAAW;gBACpC,IACA;oBAAEzE,IAAI;oBAAU2D;oBAAOc,aAAalB,UAAUkB,WAAW;gBAAC;YAElE;QACF;IACF;IAEA,OAAO;AACT;AAEAC,OAAOC,cAAc,CAACrB,QAAQ,gBAAgB;IAAEsB,OAAO;AAAK;MAE5D,WAAetB", "ignoreList": [0]}}, {"offset": {"line": 1292, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1297, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/TGRS%20Website/tgrs-website/node_modules/next/script.js"], "sourcesContent": ["module.exports = require('./dist/client/script')\n"], "names": [], "mappings": "AAAA,OAAO,OAAO", "ignoreList": [0]}}, {"offset": {"line": 1298, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}