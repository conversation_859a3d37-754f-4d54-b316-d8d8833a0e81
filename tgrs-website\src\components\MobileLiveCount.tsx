'use client';

import { useState, useEffect } from 'react';

interface ServerData {
  clients: number;
  sv_maxclients: number;
  svMaxclients: number;
  hostname: string;
}

interface ApiResponse {
  Data: ServerData;
}

export default function MobileLiveCount() {
  const [playerCount, setPlayerCount] = useState<number | null>(null);
  const [maxPlayers, setMaxPlayers] = useState<number>(48);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(false);
  const [isExpanded, setIsExpanded] = useState(false);
  const [isVisible, setIsVisible] = useState(true);

  const fetchPlayerCount = async () => {
    try {
      const response = await fetch('https://servers-frontend.fivem.net/api/servers/single/o57lj7');

      if (!response.ok) {
        throw new Error('Failed to fetch server data');
      }

      const data: ApiResponse = await response.json();

      if (data.Data) {
        setPlayerCount(data.Data.clients);
        setMaxPlayers(data.Data.svMaxclients || data.Data.sv_maxclients);
        setError(false);
      } else {
        throw new Error('Invalid server data');
      }
    } catch (err) {
      console.error('Error fetching player count:', err);
      setError(true);
      setPlayerCount(null);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchPlayerCount();
    const interval = setInterval(fetchPlayerCount, 30000);
    return () => clearInterval(interval);
  }, []);

  if (isLoading) {
    return (
      <div className="md:hidden fixed top-20 left-4 right-4 z-40">
        <div className="bg-black/95 backdrop-blur-xl rounded-xl px-4 py-3 border border-neon-orange/40 shadow-lg shadow-neon-orange/20">
          <div className="flex items-center justify-center space-x-2">
            <div className="w-2 h-2 bg-gray-400 rounded-full animate-pulse"></div>
            <span className="text-white text-sm font-medium">Loading Server Status...</span>
          </div>
        </div>
      </div>
    );
  }

  if (error || playerCount === null) {
    return (
      <div className="md:hidden fixed top-20 left-4 right-4 z-40">
        <div className="bg-black/95 backdrop-blur-xl rounded-xl px-4 py-3 border border-red-500/40 shadow-lg shadow-red-500/20">
          <div className="flex items-center justify-center space-x-2">
            <div className="w-2 h-2 bg-red-500 rounded-full"></div>
            <span className="text-red-400 text-sm font-medium">Server Offline</span>
          </div>
        </div>
      </div>
    );
  }

  const isServerFull = playerCount >= maxPlayers;
  const isHighPopulation = playerCount >= maxPlayers * 0.8;
  const fillPercentage = (playerCount / maxPlayers) * 100;

  return (
    <div className="md:hidden fixed top-20 left-4 right-4 z-40">
      {/* Permanent Live Count Widget */}
      <div
        className={`relative transition-all duration-500 ease-out ${
          isExpanded ? 'transform scale-105' : ''
        }`}
        onClick={() => setIsExpanded(!isExpanded)}
      >
        {/* Glow Effect */}
        <div className={`absolute inset-0 rounded-2xl blur-lg transition-all duration-500 ${
          isServerFull
            ? 'bg-red-500/30'
            : isHighPopulation
              ? 'bg-yellow-500/30'
              : 'bg-green-500/30'
        } ${isExpanded ? 'scale-110 opacity-100' : 'scale-100 opacity-60'}`}></div>

        {/* Main Widget */}
        <div className={`relative bg-black/95 backdrop-blur-xl rounded-xl border transition-all duration-500 ${
          isServerFull
            ? 'border-red-500/60 shadow-lg shadow-red-500/20'
            : isHighPopulation
              ? 'border-yellow-500/60 shadow-lg shadow-yellow-500/20'
              : 'border-green-500/60 shadow-lg shadow-green-500/20'
        } ${isExpanded ? 'px-5 py-4' : 'px-4 py-3'}`}>

          {/* Particle Effects */}
          <div className="absolute inset-0 overflow-hidden rounded-2xl pointer-events-none">
            <div className={`absolute top-2 left-3 w-1 h-1 rounded-full animate-ping ${
              isServerFull ? 'bg-red-400' : isHighPopulation ? 'bg-yellow-400' : 'bg-green-400'
            }`}></div>
            <div className={`absolute top-4 right-4 w-0.5 h-0.5 rounded-full animate-pulse ${
              isServerFull ? 'bg-red-300' : isHighPopulation ? 'bg-yellow-300' : 'bg-green-300'
            }`} style={{animationDelay: '0.5s'}}></div>
            <div className={`absolute bottom-3 left-2 w-0.5 h-0.5 rounded-full animate-bounce ${
              isServerFull ? 'bg-red-500' : isHighPopulation ? 'bg-yellow-500' : 'bg-green-500'
            }`} style={{animationDelay: '0.3s'}}></div>
          </div>

          {/* Content */}
          <div className="relative z-10">
            {!isExpanded ? (
              /* Compact View */
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  {/* Status Indicator */}
                  <div className={`w-3 h-3 rounded-full transition-all duration-300 ${
                    isServerFull
                      ? 'bg-red-500 animate-pulse'
                      : isHighPopulation
                        ? 'bg-yellow-500 animate-pulse'
                        : 'bg-green-500 animate-pulse'
                  }`}></div>

                  {/* Server Info */}
                  <div>
                    <div className="flex items-center space-x-2">
                      <span className="text-white text-sm font-semibold">TGRS Server</span>
                      <span className={`text-xs px-2 py-0.5 rounded-full font-medium ${
                        isServerFull
                          ? 'bg-red-500/20 text-red-400 border border-red-500/30'
                          : isHighPopulation
                            ? 'bg-yellow-500/20 text-yellow-400 border border-yellow-500/30'
                            : 'bg-green-500/20 text-green-400 border border-green-500/30'
                      }`}>
                        LIVE
                      </span>
                    </div>
                  </div>
                </div>

                {/* Player Count */}
                <div className="flex items-center space-x-2">
                  <div className="text-right">
                    <div className="flex items-baseline space-x-1">
                      <span className={`text-lg font-bold transition-colors duration-300 ${
                        isServerFull
                          ? 'text-red-400'
                          : isHighPopulation
                            ? 'text-yellow-400'
                            : 'text-green-400'
                      }`}>
                        {playerCount}
                      </span>
                      <span className="text-gray-400 text-sm">/{maxPlayers}</span>
                    </div>
                    <div className="flex items-center justify-end space-x-1">
                      <i className="fas fa-users text-gray-400 text-xs"></i>
                      <span className="text-gray-400 text-xs">online</span>
                    </div>
                  </div>

                  {/* Expand Indicator */}
                  <div className="text-gray-400">
                    <i className="fas fa-chevron-down text-xs"></i>
                  </div>
                </div>
              </div>
            ) : (
              /* Expanded View */
              <div className="space-y-3">
                {/* Header */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <div className={`w-3 h-3 rounded-full ${
                      isServerFull
                        ? 'bg-red-500 animate-pulse'
                        : isHighPopulation
                          ? 'bg-yellow-500 animate-pulse'
                          : 'bg-green-500 animate-pulse'
                    }`}></div>
                    <span className="text-white text-sm font-semibold">LIVE SERVER</span>
                  </div>
                  <i className="fas fa-chevron-up text-gray-400 text-xs"></i>
                </div>

                {/* Player Count Large */}
                <div className="text-center">
                  <div className="flex items-baseline justify-center space-x-1">
                    <span className={`text-2xl font-bold transition-colors duration-300 ${
                      isServerFull
                        ? 'text-red-400'
                        : isHighPopulation
                          ? 'text-yellow-400'
                          : 'text-green-400'
                    }`}>
                      {playerCount}
                    </span>
                    <span className="text-gray-400 text-lg">/{maxPlayers}</span>
                  </div>
                  <p className="text-gray-300 text-xs mt-1">Players Online</p>
                </div>

                {/* Progress Bar */}
                <div className="space-y-2">
                  <div className="w-full bg-gray-700 rounded-full h-2 overflow-hidden">
                    <div
                      className={`h-full transition-all duration-1000 ease-out ${
                        isServerFull
                          ? 'bg-gradient-to-r from-red-500 to-red-400'
                          : isHighPopulation
                            ? 'bg-gradient-to-r from-yellow-500 to-yellow-400'
                            : 'bg-gradient-to-r from-green-500 to-green-400'
                      }`}
                      style={{ width: `${fillPercentage}%` }}
                    ></div>
                  </div>
                  <div className="flex justify-between text-xs text-gray-400">
                    <span>0</span>
                    <span className={`font-medium ${
                      isServerFull
                        ? 'text-red-400'
                        : isHighPopulation
                          ? 'text-yellow-400'
                          : 'text-green-400'
                    }`}>
                      {Math.round(fillPercentage)}% Full
                    </span>
                    <span>{maxPlayers}</span>
                  </div>
                </div>

                {/* Status Text */}
                <div className="text-center">
                  <span className={`text-xs font-medium ${
                    isServerFull
                      ? 'text-red-400'
                      : isHighPopulation
                        ? 'text-yellow-400'
                        : 'text-green-400'
                  }`}>
                    {isServerFull
                      ? '🔥 SERVER FULL'
                      : isHighPopulation
                        ? '⚡ HIGH ACTIVITY'
                        : '✨ JOIN NOW'}
                  </span>
                </div>
              </div>
            )}
          </div>
        </div>


      </div>
    </div>
  );
}
