import Link from "next/link";
import Image from "next/image";

const Footer = () => {
  return (
    <footer className="bg-black/50 pattern-dots-footer border-t border-neon-orange/30 pt-12 pb-6 relative">
      <div className="container mx-auto px-4 md:px-6">
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-6 sm:gap-8 mb-6 sm:mb-8">
          {/* Logo and About */}
          <div className="col-span-1 sm:col-span-2 md:col-span-1">
            <Link href="/" className="flex items-center mb-4">
              <div className="relative h-8 w-8 sm:h-10 sm:w-10 mr-3">
                <Image
                  src="/assets/tgrs-logo.png"
                  alt="TGRS Logo"
                  width={40}
                  height={40}
                  className="w-full h-full object-contain"
                />
              </div>
              <span className="text-white font-display font-bold text-lg sm:text-xl tracking-wider">
                TGRS
              </span>
            </Link>
            <p className="text-gray-400 text-xs sm:text-sm mb-4">
              The premier Telugu community FiveM roleplay server with immersive experiences and unique features.
            </p>
            <div className="flex space-x-3 sm:space-x-4">
              {/* YouTube */}
              <a href="https://www.youtube.com/@GTA5RPTGRSCITY" target="_blank" rel="noopener noreferrer" className="text-white hover:text-red-500 transition-colors" aria-label="YouTube">
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                  <path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"/>
                </svg>
              </a>
              {/* Discord */}
              <a href="https://discord.gg/GAMravHDnB" target="_blank" rel="noopener noreferrer" className="text-white hover:text-[#5865F2] transition-colors" aria-label="Discord">
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                  <path d="M20.317 4.3698a19.7913 19.7913 0 00-4.8851-1.5152.0741.0741 0 00-.0785.0371c-.211.3753-.4447.8648-.6083 1.2495-1.8447-.2762-3.68-.2762-5.4868 0-.1636-.3933-.4058-.8742-.6177-1.2495a.077.077 0 00-.0785-.037 19.7363 19.7363 0 00-4.8852 1.515.0699.0699 0 00-.0321.0277C.5334 9.0458-.319 13.5799.0992 18.0578a.0824.0824 0 00.0312.0561c2.0528 1.5076 4.0413 2.4228 5.9929 3.0294a.0777.0777 0 00.0842-.0276c.4616-.6304.8731-1.2952 1.226-1.9942a.076.076 0 00-.0416-.1057c-.6528-.2476-1.2743-.5495-1.8722-.8923a.077.077 0 01-.0076-.1277c.1258-.0943.2517-.1923.3718-.2914a.0743.0743 0 01.0776-.0105c3.9278 1.7933 8.18 1.7933 12.0614 0a.0739.0739 0 01.0785.0095c.1202.099.246.1981.3728.2924a.077.077 0 01-.0066.1276 12.2986 12.2986 0 01-1.873.8914.0766.0766 0 00-.0407.1067c.3604.698.7719 1.3628 1.225 1.9932a.076.076 0 00.0842.0286c1.961-.6067 3.9495-1.5219 6.0023-3.0294a.077.077 0 00.0313-.0552c.5004-5.177-.8382-9.6739-3.5485-13.6604a.061.061 0 00-.0312-.0286zM8.02 15.3312c-1.1825 0-2.1569-1.0857-2.1569-2.419 0-1.3332.9555-2.4189 2.157-2.4189 1.2108 0 2.1757 1.0952 2.1568 2.419-.0002 1.3332-.9555 2.4189-2.1569 2.4189zm7.9748 0c-1.1825 0-2.1569-1.0857-2.1569-2.419 0-1.3332.9554-2.4189 2.1569-2.4189 1.2108 0 2.1757 1.0952 2.1568 2.419 0 1.3332-.9554 2.4189-2.1568 2.4189Z"/>
                </svg>
              </a>
              {/* Website */}
              <a href="/" className="text-white hover:text-neon-orange transition-colors" aria-label="Website">
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                  <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-1 17.93c-3.94-.49-7-3.85-7-7.93 0-.62.08-1.21.21-1.79L9 15v1c0 1.1.9 2 2 2v1.93zm6.9-2.54c-.26-.81-1-1.39-1.9-1.39h-1v-3c0-.55-.45-1-1-1H8v-2h2c.55 0 1-.45 1-1V7h2c1.1 0 2-.9 2-2v-.41c2.93 1.19 5 4.06 5 7.41 0 2.08-.8 3.97-2.1 5.39z"/>
                </svg>
              </a>
            </div>

            {/* Developer Credits - Desktop only, back in first column */}
            <div className="hidden md:block mt-6 sm:mt-8">
              <div className="relative inline-block group">
                {/* Main content */}
                <div className="relative flex items-center justify-center space-x-2 py-2 rounded-lg transition-all duration-300">
                  <span className="text-gray-400 text-xs sm:text-sm font-medium">Made with</span>

                  {/* Animated heart */}
                  <div className="relative">
                    <span className="text-red-500 text-sm sm:text-base animate-pulse group-hover:animate-bounce transition-all duration-300">
                      ❤️
                    </span>
                    {/* Heart particles on hover */}
                    <div className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-all duration-500 pointer-events-none">
                      <span className="absolute -top-1 -left-1 text-pink-400 text-xs animate-ping">💖</span>
                      <span className="absolute -top-1 -right-1 text-red-400 text-xs animate-ping" style={{animationDelay: '0.2s'}}>💕</span>
                      <span className="absolute -bottom-1 left-0 text-pink-300 text-xs animate-ping" style={{animationDelay: '0.4s'}}>💗</span>
                    </div>
                  </div>

                  <span className="text-gray-400 text-xs sm:text-sm font-medium">by</span>

                  {/* Developer name with special styling */}
                  <a
                    href="https://discord.com/users/223121046830579715"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="relative group/dev"
                  >
                    {/* Background glow */}
                    <div className="absolute inset-0 bg-gradient-to-r from-neon-orange/30 to-neon-red/30 rounded-md blur-sm opacity-0 group-hover/dev:opacity-100 transition-all duration-300"></div>

                    {/* Text with gradient */}
                    <span className="relative bg-gradient-to-r from-neon-orange via-yellow-400 to-neon-orange bg-clip-text text-transparent font-bold text-xs sm:text-sm hover:from-yellow-300 hover:via-neon-orange hover:to-yellow-300 transition-all duration-300 cursor-pointer">
                      Macpie
                    </span>

                    {/* Hover indicator */}
                    <div className="absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-to-r from-neon-orange to-neon-red group-hover/dev:w-full transition-all duration-300"></div>

                    {/* Discord icon on hover */}
                    <div className="absolute -top-6 left-1/2 transform -translate-x-1/2 opacity-0 group-hover/dev:opacity-100 transition-all duration-300 pointer-events-none">
                      <div className="bg-[#5865F2] text-white px-2 py-1 rounded text-xs whitespace-nowrap flex items-center space-x-1">
                        <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 24 24">
                          <path d="M20.317 4.3698a19.7913 19.7913 0 00-4.8851-1.5152.0741.0741 0 00-.0785.0371c-.211.3753-.4447.8648-.6083 1.2495-1.8447-.2762-3.68-.2762-5.4868 0-.1636-.3933-.4058-.8742-.6177-1.2495a.077.077 0 00-.0785-.037 19.7363 19.7363 0 00-4.8852 1.515.0699.0699 0 00-.0321.0277C.5334 9.0458-.319 13.5799.0992 18.0578a.0824.0824 0 00.0312.0561c2.0528 1.5076 4.0413 2.4228 5.9929 3.0294a.0777.0777 0 00.0842-.0276c.4616-.6304.8731-1.2952 1.226-1.9942a.076.076 0 00-.0416-.1057c-.6528-.2476-1.2743-.5495-1.8722-.8923a.077.077 0 01-.0076-.1277c.1258-.0943.2517-.1923.3718-.2914a.0743.0743 0 01.0776-.0105c3.9278 1.7933 8.18 1.7933 12.0614 0a.0739.0739 0 01.0785.0095c.1202.099.246.1981.3728.2924a.077.077 0 01-.0066.1276 12.2986 12.2986 0 01-1.873.8914.0766.0766 0 00-.0407.1067c.3604.698.7719 1.3628 1.225 1.9932a.076.076 0 00.0842.0286c1.961-.6067 3.9495-1.5219 6.0023-3.0294a.077.077 0 00.0313-.0552c.5004-5.177-.8382-9.6739-3.5485-13.6604a.061.061 0 00-.0312-.0286zM8.02 15.3312c-1.1825 0-2.1569-1.0857-2.1569-2.419 0-1.3332.9555-2.4189 2.157-2.4189 1.2108 0 2.1757 1.0952 2.1568 2.419 0 1.3332-.9555 2.4189-2.1569 2.4189zm7.9748 0c-1.1825 0-2.1569-1.0857-2.1569-2.419 0-1.3332.9554-2.4189 2.1569-2.4189 1.2108 0 2.1757 1.0952 2.1568 2.419 0 1.3332-.946 2.4189-2.1568 2.4189Z"/>
                        </svg>
                        <span>Add Friend</span>
                      </div>
                      {/* Tooltip arrow */}
                      <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-2 border-r-2 border-t-2 border-transparent border-t-[#5865F2]"></div>
                    </div>
                  </a>
                </div>
              </div>
            </div>
          </div>

          {/* Quick Links */}
          <div className="col-span-1">
            <h3 className="text-white font-display font-semibold text-base sm:text-lg mb-3 sm:mb-4">Quick Links</h3>
            <ul className="space-y-1 sm:space-y-2">
              <li>
                <Link href="/" className="text-gray-400 hover:text-neon-orange transition-colors text-xs sm:text-sm">
                  Home
                </Link>
              </li>
              <li>
                <Link href="/about" className="text-gray-400 hover:text-neon-orange transition-colors text-xs sm:text-sm">
                  About
                </Link>
              </li>
              <li>
                <Link href="/features" className="text-gray-400 hover:text-neon-orange transition-colors text-xs sm:text-sm">
                  Features
                </Link>
              </li>
              <li>
                <Link href="/rules" className="text-gray-400 hover:text-neon-orange transition-colors text-xs sm:text-sm">
                  Rules
                </Link>
              </li>
              <li>
                <Link href="/contact" className="text-gray-400 hover:text-neon-orange transition-colors text-xs sm:text-sm">
                  Contact
                </Link>
              </li>
            </ul>
          </div>

          {/* Server Info */}
          <div className="col-span-1">
            <h3 className="text-white font-display font-semibold text-base sm:text-lg mb-3 sm:mb-4">Server Info</h3>
            <ul className="space-y-1 sm:space-y-2">
              <li className="text-gray-400 text-xs sm:text-sm">
                <span className="text-neon-orange mr-2">•</span> 24/7 Uptime
              </li>
              <li className="text-gray-400 text-xs sm:text-sm">
                <span className="text-neon-orange mr-2">•</span> Active Staff
              </li>
              <li className="text-gray-400 text-xs sm:text-sm">
                <span className="text-neon-orange mr-2">•</span> Custom Scripts
              </li>
              <li className="text-gray-400 text-xs sm:text-sm">
                <span className="text-neon-orange mr-2">•</span> Regular Updates
              </li>
              <li className="text-gray-400 text-xs sm:text-sm">
                <span className="text-neon-orange mr-2">•</span> Community Events
              </li>
            </ul>
          </div>

          {/* Discord */}
          <div className="col-span-1 sm:col-span-2 md:col-span-1">
            <h3 className="text-white font-display font-semibold text-base sm:text-lg mb-3 sm:mb-4">Join Our Discord</h3>
            <p className="text-gray-400 text-xs sm:text-sm mb-3 sm:mb-4">
              Join our Discord community for server updates, events, and to connect with other players.
            </p>
            <a
              href="https://discord.gg/GAMravHDnB"
              target="_blank"
              rel="noopener noreferrer"
              className="px-3 py-2 sm:px-4 sm:py-2 rounded-md bg-[#5865F2] hover:bg-[#4752C4] text-white font-medium transition-colors flex items-center justify-center space-x-2 w-full sm:w-auto text-xs sm:text-sm"
            >
              <svg className="w-4 h-4 sm:w-5 sm:h-5" fill="currentColor" viewBox="0 0 24 24">
                <path d="M20.317 4.3698a19.7913 19.7913 0 00-4.8851-1.5152.0741.0741 0 00-.0785.0371c-.211.3753-.4447.8648-.6083 1.2495-1.8447-.2762-3.68-.2762-5.4868 0-.1636-.3933-.4058-.8742-.6177-1.2495a.077.077 0 00-.0785-.037 19.7363 19.7363 0 00-4.8852 1.515.0699.0699 0 00-.0321.0277C.5334 9.0458-.319 13.5799.0992 18.0578a.0824.0824 0 00.0312.0561c2.0528 1.5076 4.0413 2.4228 5.9929 3.0294a.0777.0777 0 00.0842-.0276c.4616-.6304.8731-1.2952 1.226-1.9942a.076.076 0 00-.0416-.1057c-.6528-.2476-1.2743-.5495-1.8722-.8923a.077.077 0 01-.0076-.1277c.1258-.0943.2517-.1923.3718-.2914a.0743.0743 0 01.0776-.0105c3.9278 1.7933 8.18 1.7933 12.0614 0a.0739.0739 0 01.0785.0095c.1202.099.246.1981.3728.2924a.077.077 0 01-.0066.1276 12.2986 12.2986 0 01-1.873.8914.0766.0766 0 00-.0407.1067c.3604.698.7719 1.3628 1.225 1.9932a.076.076 0 00.0842.0286c1.961-.6067 3.9495-1.5219 6.0023-3.0294a.077.077 0 00.0313-.0552c.5004-5.177-.8382-9.6739-3.5485-13.6604a.061.061 0 00-.0312-.0286zM8.02 15.3312c-1.1825 0-2.1569-1.0857-2.1569-2.419 0-1.3332.9555-2.4189 2.157-2.4189 1.2108 0 2.1757 1.0952 2.1568 2.419 0 1.3332-.9555 2.4189-2.1569 2.4189zm7.9748 0c-1.1825 0-2.1569-1.0857-2.1569-2.419 0-1.3332.9554-2.4189 2.1569-2.4189 1.2108 0 2.1757 1.0952 2.1568 2.419 0 1.3332-.946 2.4189-2.1568 2.4189Z"></path>
              </svg>
              <span>Join Discord</span>
            </a>

            {/* Developer Credits - Mobile only, right below Discord button */}
            <div className="md:hidden mt-4 text-center">
              <div className="relative inline-block group">
                {/* Main content */}
                <div className="relative flex items-center justify-center space-x-2 py-2 rounded-lg transition-all duration-300">
                  <span className="text-gray-400 text-xs font-medium">Made with</span>

                  {/* Animated heart */}
                  <div className="relative">
                    <span className="text-red-500 text-sm animate-pulse group-hover:animate-bounce transition-all duration-300">
                      ❤️
                    </span>
                    {/* Heart particles on hover */}
                    <div className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-all duration-500 pointer-events-none">
                      <span className="absolute -top-1 -left-1 text-pink-400 text-xs animate-ping">💖</span>
                      <span className="absolute -top-1 -right-1 text-red-400 text-xs animate-ping" style={{animationDelay: '0.2s'}}>💕</span>
                      <span className="absolute -bottom-1 left-0 text-pink-300 text-xs animate-ping" style={{animationDelay: '0.4s'}}>💗</span>
                    </div>
                  </div>

                  <span className="text-gray-400 text-xs font-medium">by</span>

                  {/* Developer name with special styling */}
                  <a
                    href="https://discord.com/users/223121046830579715"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="relative group/dev"
                  >
                    {/* Background glow */}
                    <div className="absolute inset-0 bg-gradient-to-r from-neon-orange/30 to-neon-red/30 rounded-md blur-sm opacity-0 group-hover/dev:opacity-100 transition-all duration-300"></div>

                    {/* Text with gradient */}
                    <span className="relative bg-gradient-to-r from-neon-orange via-yellow-400 to-neon-orange bg-clip-text text-transparent font-bold text-xs hover:from-yellow-300 hover:via-neon-orange hover:to-yellow-300 transition-all duration-300 cursor-pointer">
                      Macpie
                    </span>

                    {/* Hover indicator */}
                    <div className="absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-to-r from-neon-orange to-neon-red group-hover/dev:w-full transition-all duration-300"></div>

                    {/* Discord icon on hover */}
                    <div className="absolute -top-6 left-1/2 transform -translate-x-1/2 opacity-0 group-hover/dev:opacity-100 transition-all duration-300 pointer-events-none">
                      <div className="bg-[#5865F2] text-white px-2 py-1 rounded text-xs whitespace-nowrap flex items-center space-x-1">
                        <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 24 24">
                          <path d="M20.317 4.3698a19.7913 19.7913 0 00-4.8851-1.5152.0741.0741 0 00-.0785.0371c-.211.3753-.4447.8648-.6083 1.2495-1.8447-.2762-3.68-.2762-5.4868 0-.1636-.3933-.4058-.8742-.6177-1.2495a.077.077 0 00-.0785-.037 19.7363 19.7363 0 00-4.8852 1.515.0699.0699 0 00-.0321.0277C.5334 9.0458-.319 13.5799.0992 18.0578a.0824.0824 0 00.0312.0561c2.0528 1.5076 4.0413 2.4228 5.9929 3.0294a.0777.0777 0 00.0842-.0276c.4616-.6304.8731-1.2952 1.226-1.9942a.076.076 0 00-.0416-.1057c-.6528-.2476-1.2743-.5495-1.8722-.8923a.077.077 0 01-.0076-.1277c.1258-.0943.2517-.1923.3718-.2914a.0743.0743 0 01.0776-.0105c3.9278 1.7933 8.18 1.7933 12.0614 0a.0739.0739 0 01.0785.0095c.1202.099.246.1981.3728.2924a.077.077 0 01-.0066.1276 12.2986 12.2986 0 01-1.873.8914.0766.0766 0 00-.0407.1067c.3604.698.7719 1.3628 1.225 1.9932a.076.076 0 00.0842.0286c1.961-.6067 3.9495-1.5219 6.0023-3.0294a.077.077 0 00.0313-.0552c.5004-5.177-.8382-9.6739-3.5485-13.6604a.061.061 0 00-.0312-.0286zM8.02 15.3312c-1.1825 0-2.1569-1.0857-2.1569-2.419 0-1.3332.9555-2.4189 2.157-2.4189 1.2108 0 2.1757 1.0952 2.1568 2.419 0 1.3332-.9555 2.4189-2.1569 2.4189zm7.9748 0c-1.1825 0-2.1569-1.0857-2.1569-2.419 0-1.3332.9554-2.4189 2.1569-2.4189 1.2108 0 2.1757 1.0952 2.1568 2.419 0 1.3332-.946 2.4189-2.1568 2.4189Z"/>
                        </svg>
                        <span>Add Friend</span>
                      </div>
                      {/* Tooltip arrow */}
                      <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-2 border-r-2 border-t-2 border-transparent border-t-[#5865F2]"></div>
                    </div>
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Copyright */}
        <div className="border-t border-gray-800 pt-4 sm:pt-6 mt-4 sm:mt-6 text-center">
          <p className="text-gray-500 text-xs sm:text-sm">
            &copy; {new Date().getFullYear()} TGRS - Telugu Gaming Roleplay Server. All rights reserved.
          </p>
        </div>


      </div>
    </footer>
  );
};

export default Footer;
