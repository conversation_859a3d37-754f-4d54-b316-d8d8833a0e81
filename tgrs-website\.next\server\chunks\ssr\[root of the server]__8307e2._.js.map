{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[next]/internal/font/google/raj<PERSON><PERSON>_a0e8a26a.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_export_value__({\n  \"className\": \"rajdhani_a0e8a26a-module__vFaEcW__className\",\n  \"variable\": \"rajdhani_a0e8a26a-module__vFaEcW__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 10, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 16, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[next]/internal/font/google/raj<PERSON><PERSON>_a0e8a26a.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Rajdhani%22,%22arguments%22:[{%22weight%22:[%22300%22,%22400%22,%22500%22,%22600%22,%22700%22],%22subsets%22:[%22latin%22],%22display%22:%22swap%22,%22variable%22:%22--font-rajdhani%22}],%22variableName%22:%22rajdhani%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Rajdhani', 'Rajdhani Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,2JAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,2JAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,2JAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0]}}, {"offset": {"line": 32, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[next]/internal/font/google/orbitron_4ccad759.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_export_value__({\n  \"className\": \"orbitron_4ccad759-module__mSD3XW__className\",\n  \"variable\": \"orbitron_4ccad759-module__mSD3XW__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 41, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 47, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[next]/internal/font/google/orbitron_4ccad759.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Orbitron%22,%22arguments%22:[{%22weight%22:[%22400%22,%22500%22,%22600%22,%22700%22,%22800%22,%22900%22],%22subsets%22:[%22latin%22],%22display%22:%22swap%22,%22variable%22:%22--font-orbitron%22}],%22variableName%22:%22orbitron%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Orbitron', 'Orbitron Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,2JAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,2JAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,2JAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0]}}, {"offset": {"line": 63, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 69, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/TGRS%20Website/tgrs-website/src/components/Analytics.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/Analytics.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Analytics.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAgS,GAC7T,8DACA"}}, {"offset": {"line": 77, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 83, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/TGRS%20Website/tgrs-website/src/components/Analytics.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/Analytics.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Analytics.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA4Q,GACzS,0CACA"}}, {"offset": {"line": 91, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 97, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": ""}}, {"offset": {"line": 101, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 107, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/TGRS%20Website/tgrs-website/src/components/MobileLiveCount.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/MobileLiveCount.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/MobileLiveCount.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAsS,GACnU,oEACA"}}, {"offset": {"line": 115, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 121, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/TGRS%20Website/tgrs-website/src/components/MobileLiveCount.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/MobileLiveCount.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/MobileLiveCount.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAkR,GAC/S,gDACA"}}, {"offset": {"line": 129, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 135, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": ""}}, {"offset": {"line": 139, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 145, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/TGRS%20Website/tgrs-website/src/app/layout.tsx"], "sourcesContent": ["import type { <PERSON>ada<PERSON> } from \"next\";\nimport { <PERSON><PERSON><PERSON>, Or<PERSON><PERSON> } from \"next/font/google\";\nimport \"./globals.css\";\nimport Analytics from \"@/components/Analytics\";\nimport MobileLiveCount from \"@/components/MobileLiveCount\";\nimport MobileServerPulse from \"@/components/MobileServerPulse\";\n\nconst raj<PERSON><PERSON> = <PERSON><PERSON><PERSON>({\n  weight: [\"300\", \"400\", \"500\", \"600\", \"700\"],\n  subsets: [\"latin\"],\n  display: \"swap\",\n  variable: \"--font-rajdhani\",\n});\n\nconst orbitron = Orbitron({\n  weight: [\"400\", \"500\", \"600\", \"700\", \"800\", \"900\"],\n  subsets: [\"latin\"],\n  display: \"swap\",\n  variable: \"--font-orbitron\",\n});\n\nexport const metadata: Metadata = {\n  title: {\n    default: \"TGRS - Telugu Gaming Roleplay Server | Best FiveM Telugu Community\",\n    template: \"%s | TGRS - Telugu Gaming Roleplay Server\"\n  },\n  description: \"Join TGRS - The #1 Telugu FiveM roleplay server! Experience immersive GTA 5 roleplay with Telugu community, custom scripts, realistic economy, and 24/7 uptime. Best Telugu gaming server for FiveM enthusiasts.\",\n  keywords: [\n    // Primary FiveM Keywords\n    \"FiveM\", \"FiveM server\", \"FiveM Telugu\", \"Telugu FiveM\", \"FiveM roleplay\", \"FiveM RP\",\n    \"GTA 5 roleplay\", \"GTA V RP\", \"GTA roleplay server\", \"GTA 5 server\", \"GTA 5 Telugu\",\n    \"Telugu GTA 5\", \"GTA Telugu\", \"Telugu GTA\", \"GTA 5 Telugu server\", \"Telugu GTA 5 server\",\n\n    // Telugu Gaming Keywords - COMPREHENSIVE\n    \"Telugu gaming\", \"Telugu gamers\", \"Telugu gaming community\", \"Telugu gaming server\",\n    \"Telugu online gaming\", \"Telugu multiplayer games\", \"Telugu game server\", \"Telugu\",\n    \"Telugu esports\", \"Telugu gaming clan\", \"Telugu gaming group\", \"Telugu gaming discord\",\n    \"Telugu gaming YouTube\", \"Telugu gaming streamers\", \"Telugu gaming content\",\n    \"Telugu gaming news\", \"Telugu gaming updates\", \"Telugu gaming events\",\n\n    // SAMP Telugu Keywords\n    \"SAMP Telugu\", \"Telugu SAMP\", \"SAMP Telugu server\", \"Telugu SAMP server\",\n    \"San Andreas Telugu\", \"Telugu San Andreas\", \"SAMP roleplay Telugu\",\n    \"Telugu SAMP community\", \"SAMP Telugu players\", \"Telugu SAMP gaming\",\n\n    // Server Keywords\n    \"Telugu server\", \"server Telugu\", \"Telugu gaming server\", \"Telugu roleplay server\",\n    \"Telugu multiplayer server\", \"Telugu community server\", \"Telugu FiveM server\",\n    \"Telugu GTA server\", \"Telugu game server\", \"Telugu online server\",\n\n    // Location-based Keywords\n    \"Hyderabad gaming\", \"Telangana gaming\", \"Andhra Pradesh gaming\", \"South India gaming\",\n    \"Indian gaming server\", \"Indian FiveM\", \"India roleplay server\", \"Hyderabad FiveM\",\n    \"Telangana FiveM\", \"Andhra Pradesh FiveM\", \"Hyderabad Telugu gaming\",\n    \"Telangana Telugu gaming\", \"Andhra Pradesh Telugu gaming\", \"Vijayawada gaming\",\n    \"Visakhapatnam gaming\", \"Warangal gaming\", \"Guntur gaming\", \"Nellore gaming\",\n\n    // Gaming Keywords\n    \"roleplay server\", \"RP server\", \"gaming community\", \"online gaming\",\n    \"multiplayer gaming\", \"gaming server\", \"game server\", \"gaming clan\",\n    \"esports\", \"competitive gaming\", \"casual gaming\", \"gaming hub\",\n    \"gaming platform\", \"gaming network\", \"gaming portal\",\n\n    // Telugu Culture Gaming\n    \"Telugu culture gaming\", \"Telugu tradition gaming\", \"Telugu language gaming\",\n    \"Telugu speaking gamers\", \"Telugu gaming culture\", \"Telugu gaming tradition\",\n    \"Telugu gaming heritage\", \"Telugu gaming pride\", \"Telugu gaming unity\",\n\n    // Specific Features\n    \"custom scripts\", \"realistic economy\", \"24/7 uptime\", \"active community\",\n    \"VIP features\", \"custom cars\", \"immersive roleplay\", \"professional staff\",\n    \"Telugu scripts\", \"Telugu features\", \"Telugu customization\",\n\n    // Community Keywords\n    \"TGRS\", \"Telugu Gaming Roleplay Server\", \"Telugu community\", \"gaming friends\",\n    \"Discord server\", \"gaming discord\", \"Telugu discord\", \"Telugu gaming family\",\n    \"Telugu gaming brotherhood\", \"Telugu gaming sisterhood\", \"Telugu gaming unity\",\n\n    // Additional Gaming Terms\n    \"Telugu FPS\", \"Telugu racing\", \"Telugu simulation\", \"Telugu adventure\",\n    \"Telugu action games\", \"Telugu strategy games\", \"Telugu RPG\", \"Telugu MMO\",\n    \"Telugu battle royale\", \"Telugu survival games\", \"Telugu sandbox games\",\n\n    // Regional Variations\n    \"తెలుగు గేమింగ్\", \"తెలుగు గేమర్స్\", \"తెలుగు సర్వర్\", \"తెలుగు కమ్యూనిటీ\",\n    \"Telugu gaming 2024\", \"Telugu gaming 2025\", \"best Telugu gaming\",\n    \"top Telugu gaming\", \"popular Telugu gaming\", \"famous Telugu gaming\"\n  ],\n  authors: [{ name: \"TGRS Team\", url: \"https://tgrs.com\" }],\n  creator: \"TGRS Development Team\",\n  publisher: \"TGRS\",\n  formatDetection: {\n    email: false,\n    address: false,\n    telephone: false,\n  },\n  metadataBase: new URL(\"https://tgrs.com\"),\n  alternates: {\n    canonical: \"/\",\n    languages: {\n      \"en-US\": \"/\",\n      \"te-IN\": \"/\",\n    },\n  },\n  icons: {\n    icon: [\n      { url: \"/assets/favicon-16x16.png\", sizes: \"16x16\", type: \"image/png\" },\n      { url: \"/assets/favicon-32x32.png\", sizes: \"32x32\", type: \"image/png\" },\n      { url: \"/assets/favicon-96x96.png\", sizes: \"96x96\", type: \"image/png\" },\n    ],\n    shortcut: \"/assets/favicon-32x32.png\",\n    apple: \"/assets/favicon-96x96.png\",\n  },\n  openGraph: {\n    title: \"TGRS - #1 Telugu FiveM Roleplay Server | Best Telugu Gaming Community\",\n    description: \"Join TGRS - The premier Telugu FiveM roleplay server! Experience immersive GTA 5 roleplay with Telugu community, custom scripts, realistic economy, and 24/7 uptime. Best Telugu gaming server for FiveM enthusiasts.\",\n    url: \"https://tgrs.com\",\n    siteName: \"TGRS - Telugu Gaming Roleplay Server\",\n    locale: \"en_US\",\n    type: \"website\",\n    images: [\n      {\n        url: \"/assets/tgrs-og-image.jpg\",\n        width: 1200,\n        height: 630,\n        alt: \"TGRS - Telugu Gaming Roleplay Server - Best FiveM Telugu Community\",\n      },\n      {\n        url: \"/assets/tgrs-logo.png\",\n        width: 512,\n        height: 512,\n        alt: \"TGRS Logo\",\n      },\n    ],\n  },\n  twitter: {\n    card: \"summary_large_image\",\n    title: \"TGRS - #1 Telugu FiveM Roleplay Server\",\n    description: \"Join the best Telugu FiveM community! Immersive roleplay, custom scripts, 24/7 uptime. Experience GTA 5 like never before with TGRS!\",\n    images: [\"/assets/tgrs-og-image.jpg\"],\n    creator: \"@TGRS_Official\",\n    site: \"@TGRS_Official\",\n  },\n  robots: {\n    index: true,\n    follow: true,\n    googleBot: {\n      index: true,\n      follow: true,\n      \"max-video-preview\": -1,\n      \"max-image-preview\": \"large\",\n      \"max-snippet\": -1,\n    },\n  },\n  verification: {\n    google: \"your-google-verification-code\",\n    yandex: \"your-yandex-verification-code\",\n    yahoo: \"your-yahoo-verification-code\",\n  },\n};\n\nexport default function RootLayout({\n  children,\n}: Readonly<{\n  children: React.ReactNode;\n}>) {\n  return (\n    <html lang=\"en\" className=\"scroll-smooth\">\n      <head>\n        <link\n          rel=\"stylesheet\"\n          href=\"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css\"\n          integrity=\"sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw==\"\n          crossOrigin=\"anonymous\"\n          referrerPolicy=\"no-referrer\"\n        />\n\n        {/* Structured Data for Organization */}\n        <script\n          type=\"application/ld+json\"\n          dangerouslySetInnerHTML={{\n            __html: JSON.stringify({\n              \"@context\": \"https://schema.org\",\n              \"@type\": \"Organization\",\n              \"name\": \"TGRS - Telugu Gaming Roleplay Server\",\n              \"alternateName\": \"TGRS\",\n              \"url\": \"https://tgrs.com\",\n              \"logo\": \"https://tgrs.com/assets/tgrs-logo.png\",\n              \"description\": \"The premier Telugu FiveM roleplay server with immersive experiences and unique features\",\n              \"foundingDate\": \"2024\",\n              \"sameAs\": [\n                \"https://discord.gg/GAMravHDnB\",\n                \"https://www.youtube.com/@GTA5RPTGRSCITY\"\n              ],\n              \"contactPoint\": {\n                \"@type\": \"ContactPoint\",\n                \"contactType\": \"customer service\",\n                \"url\": \"https://discord.gg/GAMravHDnB\"\n              }\n            })\n          }}\n        />\n\n        {/* Structured Data for Gaming Server */}\n        <script\n          type=\"application/ld+json\"\n          dangerouslySetInnerHTML={{\n            __html: JSON.stringify({\n              \"@context\": \"https://schema.org\",\n              \"@type\": \"SoftwareApplication\",\n              \"name\": \"TGRS FiveM Server\",\n              \"applicationCategory\": \"GameApplication\",\n              \"operatingSystem\": \"Windows, Mac, Linux\",\n              \"description\": \"Telugu FiveM roleplay server offering immersive GTA 5 roleplay experience with custom scripts and active community\",\n              \"offers\": {\n                \"@type\": \"Offer\",\n                \"price\": \"0\",\n                \"priceCurrency\": \"USD\",\n                \"availability\": \"https://schema.org/InStock\"\n              },\n              \"aggregateRating\": {\n                \"@type\": \"AggregateRating\",\n                \"ratingValue\": \"4.8\",\n                \"ratingCount\": \"500\",\n                \"bestRating\": \"5\"\n              },\n              \"provider\": {\n                \"@type\": \"Organization\",\n                \"name\": \"TGRS\"\n              }\n            })\n          }}\n        />\n\n        {/* Structured Data for Website */}\n        <script\n          type=\"application/ld+json\"\n          dangerouslySetInnerHTML={{\n            __html: JSON.stringify({\n              \"@context\": \"https://schema.org\",\n              \"@type\": \"WebSite\",\n              \"name\": \"TGRS - Telugu Gaming Roleplay Server\",\n              \"url\": \"https://tgrs.com\",\n              \"description\": \"Official website of TGRS - The premier Telugu FiveM roleplay server\",\n              \"inLanguage\": \"en-US\",\n              \"potentialAction\": {\n                \"@type\": \"SearchAction\",\n                \"target\": \"https://tgrs.com/search?q={search_term_string}\",\n                \"query-input\": \"required name=search_term_string\"\n              }\n            })\n          }}\n        />\n\n        {/* Preconnect to external domains */}\n        <link rel=\"preconnect\" href=\"https://fonts.googleapis.com\" />\n        <link rel=\"preconnect\" href=\"https://fonts.gstatic.com\" crossOrigin=\"\" />\n        <link rel=\"preconnect\" href=\"https://cdnjs.cloudflare.com\" />\n\n        {/* DNS Prefetch */}\n        <link rel=\"dns-prefetch\" href=\"//discord.gg\" />\n        <link rel=\"dns-prefetch\" href=\"//youtube.com\" />\n        <link rel=\"dns-prefetch\" href=\"//www.youtube.com\" />\n      </head>\n      <body\n        className={`${rajdhani.variable} ${orbitron.variable} antialiased`}\n        suppressHydrationWarning={true}\n      >\n        <Analytics />\n        <MobileLiveCount />\n        {children}\n      </body>\n    </html>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;AAGA;AACA;;;;;;;AAiBO,MAAM,WAAqB;IAChC,OAAO;QACL,SAAS;QACT,UAAU;IACZ;IACA,aAAa;IACb,UAAU;QACR,yBAAyB;QACzB;QAAS;QAAgB;QAAgB;QAAgB;QAAkB;QAC3E;QAAkB;QAAY;QAAuB;QAAgB;QACrE;QAAgB;QAAc;QAAc;QAAuB;QAEnE,yCAAyC;QACzC;QAAiB;QAAiB;QAA2B;QAC7D;QAAwB;QAA4B;QAAsB;QAC1E;QAAkB;QAAsB;QAAuB;QAC/D;QAAyB;QAA2B;QACpD;QAAsB;QAAyB;QAE/C,uBAAuB;QACvB;QAAe;QAAe;QAAsB;QACpD;QAAsB;QAAsB;QAC5C;QAAyB;QAAuB;QAEhD,kBAAkB;QAClB;QAAiB;QAAiB;QAAwB;QAC1D;QAA6B;QAA2B;QACxD;QAAqB;QAAsB;QAE3C,0BAA0B;QAC1B;QAAoB;QAAoB;QAAyB;QACjE;QAAwB;QAAgB;QAAyB;QACjE;QAAmB;QAAwB;QAC3C;QAA2B;QAAgC;QAC3D;QAAwB;QAAmB;QAAiB;QAE5D,kBAAkB;QAClB;QAAmB;QAAa;QAAoB;QACpD;QAAsB;QAAiB;QAAe;QACtD;QAAW;QAAsB;QAAiB;QAClD;QAAmB;QAAkB;QAErC,wBAAwB;QACxB;QAAyB;QAA2B;QACpD;QAA0B;QAAyB;QACnD;QAA0B;QAAuB;QAEjD,oBAAoB;QACpB;QAAkB;QAAqB;QAAe;QACtD;QAAgB;QAAe;QAAsB;QACrD;QAAkB;QAAmB;QAErC,qBAAqB;QACrB;QAAQ;QAAiC;QAAoB;QAC7D;QAAkB;QAAkB;QAAkB;QACtD;QAA6B;QAA4B;QAEzD,0BAA0B;QAC1B;QAAc;QAAiB;QAAqB;QACpD;QAAuB;QAAyB;QAAc;QAC9D;QAAwB;QAAyB;QAEjD,sBAAsB;QACtB;QAAkB;QAAkB;QAAiB;QACrD;QAAsB;QAAsB;QAC5C;QAAqB;QAAyB;KAC/C;IACD,SAAS;QAAC;YAAE,MAAM;YAAa,KAAK;QAAmB;KAAE;IACzD,SAAS;IACT,WAAW;IACX,iBAAiB;QACf,OAAO;QACP,SAAS;QACT,WAAW;IACb;IACA,cAAc,IAAI,IAAI;IACtB,YAAY;QACV,WAAW;QACX,WAAW;YACT,SAAS;YACT,SAAS;QACX;IACF;IACA,OAAO;QACL,MAAM;YACJ;gBAAE,KAAK;gBAA6B,OAAO;gBAAS,MAAM;YAAY;YACtE;gBAAE,KAAK;gBAA6B,OAAO;gBAAS,MAAM;YAAY;YACtE;gBAAE,KAAK;gBAA6B,OAAO;gBAAS,MAAM;YAAY;SACvE;QACD,UAAU;QACV,OAAO;IACT;IACA,WAAW;QACT,OAAO;QACP,aAAa;QACb,KAAK;QACL,UAAU;QACV,QAAQ;QACR,MAAM;QACN,QAAQ;YACN;gBACE,KAAK;gBACL,OAAO;gBACP,QAAQ;gBACR,KAAK;YACP;YACA;gBACE,KAAK;gBACL,OAAO;gBACP,QAAQ;gBACR,KAAK;YACP;SACD;IACH;IACA,SAAS;QACP,MAAM;QACN,OAAO;QACP,aAAa;QACb,QAAQ;YAAC;SAA4B;QACrC,SAAS;QACT,MAAM;IACR;IACA,QAAQ;QACN,OAAO;QACP,QAAQ;QACR,WAAW;YACT,OAAO;YACP,QAAQ;YACR,qBAAqB,CAAC;YACtB,qBAAqB;YACrB,eAAe,CAAC;QAClB;IACF;IACA,cAAc;QACZ,QAAQ;QACR,QAAQ;QACR,OAAO;IACT;AACF;AAEe,SAAS,WAAW,EACjC,QAAQ,EAGR;IACA,qBACE,8OAAC;QAAK,MAAK;QAAK,WAAU;;0BACxB,8OAAC;;kCACC,8OAAC;wBACC,KAAI;wBACJ,MAAK;wBACL,WAAU;wBACV,aAAY;wBACZ,gBAAe;;;;;;kCAIjB,8OAAC;wBACC,MAAK;wBACL,yBAAyB;4BACvB,QAAQ,KAAK,SAAS,CAAC;gCACrB,YAAY;gCACZ,SAAS;gCACT,QAAQ;gCACR,iBAAiB;gCACjB,OAAO;gCACP,QAAQ;gCACR,eAAe;gCACf,gBAAgB;gCAChB,UAAU;oCACR;oCACA;iCACD;gCACD,gBAAgB;oCACd,SAAS;oCACT,eAAe;oCACf,OAAO;gCACT;4BACF;wBACF;;;;;;kCAIF,8OAAC;wBACC,MAAK;wBACL,yBAAyB;4BACvB,QAAQ,KAAK,SAAS,CAAC;gCACrB,YAAY;gCACZ,SAAS;gCACT,QAAQ;gCACR,uBAAuB;gCACvB,mBAAmB;gCACnB,eAAe;gCACf,UAAU;oCACR,SAAS;oCACT,SAAS;oCACT,iBAAiB;oCACjB,gBAAgB;gCAClB;gCACA,mBAAmB;oCACjB,SAAS;oCACT,eAAe;oCACf,eAAe;oCACf,cAAc;gCAChB;gCACA,YAAY;oCACV,SAAS;oCACT,QAAQ;gCACV;4BACF;wBACF;;;;;;kCAIF,8OAAC;wBACC,MAAK;wBACL,yBAAyB;4BACvB,QAAQ,KAAK,SAAS,CAAC;gCACrB,YAAY;gCACZ,SAAS;gCACT,QAAQ;gCACR,OAAO;gCACP,eAAe;gCACf,cAAc;gCACd,mBAAmB;oCACjB,SAAS;oCACT,UAAU;oCACV,eAAe;gCACjB;4BACF;wBACF;;;;;;kCAIF,8OAAC;wBAAK,KAAI;wBAAa,MAAK;;;;;;kCAC5B,8OAAC;wBAAK,KAAI;wBAAa,MAAK;wBAA4B,aAAY;;;;;;kCACpE,8OAAC;wBAAK,KAAI;wBAAa,MAAK;;;;;;kCAG5B,8OAAC;wBAAK,KAAI;wBAAe,MAAK;;;;;;kCAC9B,8OAAC;wBAAK,KAAI;wBAAe,MAAK;;;;;;kCAC9B,8OAAC;wBAAK,KAAI;wBAAe,MAAK;;;;;;;;;;;;0BAEhC,8OAAC;gBACC,WAAW,GAAG,4IAAA,CAAA,UAAQ,CAAC,QAAQ,CAAC,CAAC,EAAE,4IAAA,CAAA,UAAQ,CAAC,QAAQ,CAAC,YAAY,CAAC;gBAClE,0BAA0B;;kCAE1B,8OAAC,+HAAA,CAAA,UAAS;;;;;kCACV,8OAAC,qIAAA,CAAA,UAAe;;;;;oBACf;;;;;;;;;;;;;AAIT"}}, {"offset": {"line": 593, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 604, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/TGRS%20Website/tgrs-website/node_modules/next/src/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-rsc'\n].ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,0HAAyBC,QAAQ,CACxD,YACD,CAACC,kBAAkB", "ignoreList": [0]}}, {"offset": {"line": 606, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}