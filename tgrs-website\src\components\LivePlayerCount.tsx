'use client';

import { useState, useEffect } from 'react';

interface ServerData {
  clients: number;
  sv_maxclients: number;
  hostname: string;
}

interface ApiResponse {
  Data: ServerData;
}

export default function LivePlayerCount() {
  const [playerCount, setPlayerCount] = useState<number | null>(null);
  const [maxPlayers, setMaxPlayers] = useState<number>(48);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(false);

  const fetchPlayerCount = async () => {
    try {
      const response = await fetch('https://servers-frontend.fivem.net/api/servers/single/o57lj7');
      
      if (!response.ok) {
        throw new Error('Failed to fetch server data');
      }
      
      const data: ApiResponse = await response.json();
      
      if (data.Data) {
        setPlayerCount(data.Data.clients);
        setMaxPlayers(data.Data.sv_maxclients);
        setError(false);
      } else {
        throw new Error('Invalid server data');
      }
    } catch (err) {
      console.error('Error fetching player count:', err);
      setError(true);
      // Fallback to a default count if API fails
      setPlayerCount(null);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    // Initial fetch
    fetchPlayerCount();
    
    // Update every 30 seconds
    const interval = setInterval(fetchPlayerCount, 30000);
    
    return () => clearInterval(interval);
  }, []);

  if (isLoading) {
    return (
      <div className="flex items-center space-x-2 text-gray-400">
        <div className="w-2 h-2 bg-gray-400 rounded-full animate-pulse"></div>
        <span className="text-sm">Loading...</span>
      </div>
    );
  }

  if (error || playerCount === null) {
    return (
      <div className="flex items-center space-x-2 text-gray-500">
        <div className="w-2 h-2 bg-gray-500 rounded-full"></div>
        <span className="text-sm">Server Offline</span>
      </div>
    );
  }

  const isServerFull = playerCount >= maxPlayers;
  const isHighPopulation = playerCount >= maxPlayers * 0.8;

  return (
    <div className="flex items-center space-x-2 group cursor-pointer" title={`${playerCount}/${maxPlayers} players online`}>
      {/* Status indicator */}
      <div className={`w-2 h-2 rounded-full transition-all duration-300 ${
        isServerFull 
          ? 'bg-red-500 animate-pulse' 
          : isHighPopulation 
            ? 'bg-yellow-500 animate-pulse' 
            : 'bg-green-500 animate-pulse'
      }`}></div>
      
      {/* Player count */}
      <div className="flex items-center space-x-1">
        <span className={`text-sm font-medium transition-colors duration-300 ${
          isServerFull 
            ? 'text-red-400' 
            : isHighPopulation 
              ? 'text-yellow-400' 
              : 'text-green-400'
        } group-hover:text-neon-orange`}>
          {playerCount}
        </span>
        <span className="text-gray-400 text-sm">/{maxPlayers}</span>
      </div>
      
      {/* Players icon */}
      <i className="fas fa-users text-gray-400 text-sm group-hover:text-neon-orange transition-colors duration-300"></i>
    </div>
  );
}
