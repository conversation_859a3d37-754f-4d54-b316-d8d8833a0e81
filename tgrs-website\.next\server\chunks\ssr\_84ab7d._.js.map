{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/TGRS%20Website/tgrs-website/src/components/Navbar.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/Navbar.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Navbar.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA6R,GAC1T,2DACA"}}, {"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 21, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/TGRS%20Website/tgrs-website/src/components/Navbar.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/Navbar.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Navbar.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAyQ,GACtS,uCACA"}}, {"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 35, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": ""}}, {"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 45, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/TGRS%20Website/tgrs-website/src/components/Footer.tsx"], "sourcesContent": ["import Link from \"next/link\";\nimport Image from \"next/image\";\n\nconst Footer = () => {\n  return (\n    <footer className=\"bg-black/50 pattern-dots-footer border-t border-neon-orange/30 pt-12 pb-6 relative\">\n      <div className=\"container mx-auto px-4 md:px-6\">\n        <div className=\"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-6 sm:gap-8 mb-6 sm:mb-8\">\n          {/* Logo and About */}\n          <div className=\"col-span-1 sm:col-span-2 md:col-span-1\">\n            <Link href=\"/\" className=\"flex items-center mb-4\">\n              <div className=\"relative h-8 w-8 sm:h-10 sm:w-10 mr-3\">\n                <Image\n                  src=\"/assets/tgrs-logo.png\"\n                  alt=\"TGRS Logo\"\n                  width={40}\n                  height={40}\n                  className=\"w-full h-full object-contain\"\n                />\n              </div>\n              <span className=\"text-white font-display font-bold text-lg sm:text-xl tracking-wider\">\n                TGRS\n              </span>\n            </Link>\n            <p className=\"text-gray-400 text-xs sm:text-sm mb-4\">\n              The premier Telugu community FiveM roleplay server with immersive experiences and unique features.\n            </p>\n            <div className=\"flex space-x-3 sm:space-x-4\">\n              {/* YouTube */}\n              <a href=\"https://www.youtube.com/@GTA5RPTGRSCITY\" target=\"_blank\" rel=\"noopener noreferrer\" className=\"text-white hover:text-red-500 transition-colors\" aria-label=\"YouTube\">\n                <svg className=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\" aria-hidden=\"true\">\n                  <path d=\"M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z\"/>\n                </svg>\n              </a>\n              {/* Discord */}\n              <a href=\"https://discord.gg/GAMravHDnB\" target=\"_blank\" rel=\"noopener noreferrer\" className=\"text-white hover:text-[#5865F2] transition-colors\" aria-label=\"Discord\">\n                <svg className=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\" aria-hidden=\"true\">\n                  <path d=\"M20.317 4.3698a19.7913 19.7913 0 00-4.8851-1.5152.0741.0741 0 00-.0785.0371c-.211.3753-.4447.8648-.6083 1.2495-1.8447-.2762-3.68-.2762-5.4868 0-.1636-.3933-.4058-.8742-.6177-1.2495a.077.077 0 00-.0785-.037 19.7363 19.7363 0 00-4.8852 1.515.0699.0699 0 00-.0321.0277C.5334 9.0458-.319 13.5799.0992 18.0578a.0824.0824 0 00.0312.0561c2.0528 1.5076 4.0413 2.4228 5.9929 3.0294a.0777.0777 0 00.0842-.0276c.4616-.6304.8731-1.2952 1.226-1.9942a.076.076 0 00-.0416-.1057c-.6528-.2476-1.2743-.5495-1.8722-.8923a.077.077 0 01-.0076-.1277c.1258-.0943.2517-.1923.3718-.2914a.0743.0743 0 01.0776-.0105c3.9278 1.7933 8.18 1.7933 12.0614 0a.0739.0739 0 01.0785.0095c.1202.099.246.1981.3728.2924a.077.077 0 01-.0066.1276 12.2986 12.2986 0 01-1.873.8914.0766.0766 0 00-.0407.1067c.3604.698.7719 1.3628 1.225 1.9932a.076.076 0 00.0842.0286c1.961-.6067 3.9495-1.5219 6.0023-3.0294a.077.077 0 00.0313-.0552c.5004-5.177-.8382-9.6739-3.5485-13.6604a.061.061 0 00-.0312-.0286zM8.02 15.3312c-1.1825 0-2.1569-1.0857-2.1569-2.419 0-1.3332.9555-2.4189 2.157-2.4189 1.2108 0 2.1757 1.0952 2.1568 2.419-.0002 1.3332-.9555 2.4189-2.1569 2.4189zm7.9748 0c-1.1825 0-2.1569-1.0857-2.1569-2.419 0-1.3332.9554-2.4189 2.1569-2.4189 1.2108 0 2.1757 1.0952 2.1568 2.419 0 1.3332-.9554 2.4189-2.1568 2.4189Z\"/>\n                </svg>\n              </a>\n              {/* Website */}\n              <a href=\"/\" className=\"text-white hover:text-neon-orange transition-colors\" aria-label=\"Website\">\n                <svg className=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\" aria-hidden=\"true\">\n                  <path d=\"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-1 17.93c-3.94-.49-7-3.85-7-7.93 0-.62.08-1.21.21-1.79L9 15v1c0 1.1.9 2 2 2v1.93zm6.9-2.54c-.26-.81-1-1.39-1.9-1.39h-1v-3c0-.55-.45-1-1-1H8v-2h2c.55 0 1-.45 1-1V7h2c1.1 0 2-.9 2-2v-.41c2.93 1.19 5 4.06 5 7.41 0 2.08-.8 3.97-2.1 5.39z\"/>\n                </svg>\n              </a>\n\n            </div>\n          </div>\n\n          {/* Quick Links */}\n          <div className=\"col-span-1\">\n            <h3 className=\"text-white font-display font-semibold text-base sm:text-lg mb-3 sm:mb-4\">Quick Links</h3>\n            <ul className=\"space-y-1 sm:space-y-2\">\n              <li>\n                <Link href=\"/\" className=\"text-gray-400 hover:text-neon-orange transition-colors text-xs sm:text-sm\">\n                  Home\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/about\" className=\"text-gray-400 hover:text-neon-orange transition-colors text-xs sm:text-sm\">\n                  About\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/features\" className=\"text-gray-400 hover:text-neon-orange transition-colors text-xs sm:text-sm\">\n                  Features\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/rules\" className=\"text-gray-400 hover:text-neon-orange transition-colors text-xs sm:text-sm\">\n                  Rules\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/contact\" className=\"text-gray-400 hover:text-neon-orange transition-colors text-xs sm:text-sm\">\n                  Contact\n                </Link>\n              </li>\n            </ul>\n          </div>\n\n          {/* Server Info */}\n          <div className=\"col-span-1\">\n            <h3 className=\"text-white font-display font-semibold text-base sm:text-lg mb-3 sm:mb-4\">Server Info</h3>\n            <ul className=\"space-y-1 sm:space-y-2\">\n              <li className=\"text-gray-400 text-xs sm:text-sm\">\n                <span className=\"text-neon-orange mr-2\">•</span> 24/7 Uptime\n              </li>\n              <li className=\"text-gray-400 text-xs sm:text-sm\">\n                <span className=\"text-neon-orange mr-2\">•</span> Active Staff\n              </li>\n              <li className=\"text-gray-400 text-xs sm:text-sm\">\n                <span className=\"text-neon-orange mr-2\">•</span> Custom Scripts\n              </li>\n              <li className=\"text-gray-400 text-xs sm:text-sm\">\n                <span className=\"text-neon-orange mr-2\">•</span> Regular Updates\n              </li>\n              <li className=\"text-gray-400 text-xs sm:text-sm\">\n                <span className=\"text-neon-orange mr-2\">•</span> Community Events\n              </li>\n            </ul>\n          </div>\n\n          {/* Discord */}\n          <div className=\"col-span-1 sm:col-span-2 md:col-span-1\">\n            <h3 className=\"text-white font-display font-semibold text-base sm:text-lg mb-3 sm:mb-4\">Join Our Discord</h3>\n            <p className=\"text-gray-400 text-xs sm:text-sm mb-3 sm:mb-4\">\n              Join our Discord community for server updates, events, and to connect with other players.\n            </p>\n            <a\n              href=\"https://discord.gg/GAMravHDnB\"\n              target=\"_blank\"\n              rel=\"noopener noreferrer\"\n              className=\"px-3 py-2 sm:px-4 sm:py-2 rounded-md bg-[#5865F2] hover:bg-[#4752C4] text-white font-medium transition-colors flex items-center justify-center space-x-2 w-full sm:w-auto text-xs sm:text-sm\"\n            >\n              <svg className=\"w-4 h-4 sm:w-5 sm:h-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path d=\"M20.317 4.3698a19.7913 19.7913 0 00-4.8851-1.5152.0741.0741 0 00-.0785.0371c-.211.3753-.4447.8648-.6083 1.2495-1.8447-.2762-3.68-.2762-5.4868 0-.1636-.3933-.4058-.8742-.6177-1.2495a.077.077 0 00-.0785-.037 19.7363 19.7363 0 00-4.8852 1.515.0699.0699 0 00-.0321.0277C.5334 9.0458-.319 13.5799.0992 18.0578a.0824.0824 0 00.0312.0561c2.0528 1.5076 4.0413 2.4228 5.9929 3.0294a.0777.0777 0 00.0842-.0276c.4616-.6304.8731-1.2952 1.226-1.9942a.076.076 0 00-.0416-.1057c-.6528-.2476-1.2743-.5495-1.8722-.8923a.077.077 0 01-.0076-.1277c.1258-.0943.2517-.1923.3718-.2914a.0743.0743 0 01.0776-.0105c3.9278 1.7933 8.18 1.7933 12.0614 0a.0739.0739 0 01.0785.0095c.1202.099.246.1981.3728.2924a.077.077 0 01-.0066.1276 12.2986 12.2986 0 01-1.873.8914.0766.0766 0 00-.0407.1067c.3604.698.7719 1.3628 1.225 1.9932a.076.076 0 00.0842.0286c1.961-.6067 3.9495-1.5219 6.0023-3.0294a.077.077 0 00.0313-.0552c.5004-5.177-.8382-9.6739-3.5485-13.6604a.061.061 0 00-.0312-.0286zM8.02 15.3312c-1.1825 0-2.1569-1.0857-2.1569-2.419 0-1.3332.9555-2.4189 2.157-2.4189 1.2108 0 2.1757 1.0952 2.1568 2.419 0 1.3332-.9555 2.4189-2.1569 2.4189zm7.9748 0c-1.1825 0-2.1569-1.0857-2.1569-2.419 0-1.3332.9554-2.4189 2.1569-2.4189 1.2108 0 2.1757 1.0952 2.1568 2.419 0 1.3332-.946 2.4189-2.1568 2.4189Z\"></path>\n              </svg>\n              <span>Join Discord</span>\n            </a>\n          </div>\n        </div>\n\n        {/* Copyright */}\n        <div className=\"border-t border-gray-800 pt-4 sm:pt-6 mt-4 sm:mt-6 text-center\">\n          <p className=\"text-gray-500 text-xs sm:text-sm\">\n            &copy; {new Date().getFullYear()} TGRS - Telugu Gaming Roleplay Server. All rights reserved.\n          </p>\n        </div>\n      </div>\n    </footer>\n  );\n};\n\nexport default Footer;\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAEA,MAAM,SAAS;IACb,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAI,WAAU;;sDACvB,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;gDACJ,KAAI;gDACJ,KAAI;gDACJ,OAAO;gDACP,QAAQ;gDACR,WAAU;;;;;;;;;;;sDAGd,8OAAC;4CAAK,WAAU;sDAAsE;;;;;;;;;;;;8CAIxF,8OAAC;oCAAE,WAAU;8CAAwC;;;;;;8CAGrD,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC;4CAAE,MAAK;4CAA0C,QAAO;4CAAS,KAAI;4CAAsB,WAAU;4CAAkD,cAAW;sDACjK,cAAA,8OAAC;gDAAI,WAAU;gDAAU,MAAK;gDAAe,SAAQ;gDAAY,eAAY;0DAC3E,cAAA,8OAAC;oDAAK,GAAE;;;;;;;;;;;;;;;;sDAIZ,8OAAC;4CAAE,MAAK;4CAAgC,QAAO;4CAAS,KAAI;4CAAsB,WAAU;4CAAoD,cAAW;sDACzJ,cAAA,8OAAC;gDAAI,WAAU;gDAAU,MAAK;gDAAe,SAAQ;gDAAY,eAAY;0DAC3E,cAAA,8OAAC;oDAAK,GAAE;;;;;;;;;;;;;;;;sDAIZ,8OAAC;4CAAE,MAAK;4CAAI,WAAU;4CAAsD,cAAW;sDACrF,cAAA,8OAAC;gDAAI,WAAU;gDAAU,MAAK;gDAAe,SAAQ;gDAAY,eAAY;0DAC3E,cAAA,8OAAC;oDAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQhB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA0E;;;;;;8CACxF,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAI,WAAU;0DAA4E;;;;;;;;;;;sDAIvG,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAS,WAAU;0DAA4E;;;;;;;;;;;sDAI5G,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAY,WAAU;0DAA4E;;;;;;;;;;;sDAI/G,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAS,WAAU;0DAA4E;;;;;;;;;;;sDAI5G,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;0DAA4E;;;;;;;;;;;;;;;;;;;;;;;sCAQlH,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA0E;;;;;;8CACxF,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAK,WAAU;8DAAwB;;;;;;gDAAQ;;;;;;;sDAElD,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAK,WAAU;8DAAwB;;;;;;gDAAQ;;;;;;;sDAElD,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAK,WAAU;8DAAwB;;;;;;gDAAQ;;;;;;;sDAElD,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAK,WAAU;8DAAwB;;;;;;gDAAQ;;;;;;;sDAElD,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAK,WAAU;8DAAwB;;;;;;gDAAQ;;;;;;;;;;;;;;;;;;;sCAMtD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA0E;;;;;;8CACxF,8OAAC;oCAAE,WAAU;8CAAgD;;;;;;8CAG7D,8OAAC;oCACC,MAAK;oCACL,QAAO;oCACP,KAAI;oCACJ,WAAU;;sDAEV,8OAAC;4CAAI,WAAU;4CAAwB,MAAK;4CAAe,SAAQ;sDACjE,cAAA,8OAAC;gDAAK,GAAE;;;;;;;;;;;sDAEV,8OAAC;sDAAK;;;;;;;;;;;;;;;;;;;;;;;;8BAMZ,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAE,WAAU;;4BAAmC;4BACtC,IAAI,OAAO,WAAW;4BAAG;;;;;;;;;;;;;;;;;;;;;;;AAM7C;uCAEe"}}, {"offset": {"line": 522, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 528, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/TGRS%20Website/tgrs-website/src/components/Hero.tsx"], "sourcesContent": ["import React from \"react\";\nimport Button from \"./Button\";\n\ninterface HeroProps {\n  title: string;\n  subtitle: string;\n  primaryButtonText?: string;\n  primaryButtonHref?: string;\n  secondaryButtonText?: string;\n  secondaryButtonHref?: string;\n  backgroundImage?: string;\n}\n\nconst Hero = ({\n  title,\n  subtitle,\n  primaryButtonText,\n  primaryButtonHref,\n  secondaryButtonText,\n  secondaryButtonHref,\n  backgroundImage = '/assets/image-3.jpg',\n}: HeroProps) => {\n  return (\n    <div className=\"relative min-h-screen flex items-center justify-center overflow-hidden\">\n      {/* Background Image */}\n      <div\n        className=\"absolute inset-0 z-0 bg-cover bg-center bg-no-repeat\"\n        style={{\n          backgroundImage: `url('${backgroundImage}')`\n        }}\n      ></div>\n\n      {/* Background overlays */}\n      <div className=\"absolute inset-0 z-10\">\n        <div className=\"absolute inset-0 bg-black opacity-60\"></div>\n        <div className=\"absolute top-0 left-0 w-full h-full bg-gradient-to-b from-black/20 via-transparent to-black/80\"></div>\n\n        {/* Subtle animated grid lines (reduced opacity) */}\n        <div className=\"absolute inset-0 flex flex-col opacity-5\">\n          {Array.from({ length: 15 }).map((_, i) => (\n            <div key={`h-${i}`} className=\"h-px bg-neon-orange w-full animate-pulse-slow\" style={{ marginTop: `${i * 7}vh`, animationDelay: `${i * 0.2}s` }}></div>\n          ))}\n        </div>\n        <div className=\"absolute inset-0 flex flex-row opacity-5\">\n          {Array.from({ length: 15 }).map((_, i) => (\n            <div key={`v-${i}`} className=\"w-px bg-neon-orange h-full animate-pulse-slow\" style={{ marginLeft: `${i * 7}vw`, animationDelay: `${i * 0.2}s` }}></div>\n          ))}\n        </div>\n\n        {/* Subtle glowing orbs (reduced and repositioned) */}\n        <div className=\"absolute top-1/4 left-1/4 w-96 h-96 rounded-full bg-neon-orange/10 blur-3xl animate-float\"></div>\n        <div className=\"absolute bottom-1/4 right-1/4 w-64 h-64 rounded-full bg-neon-red/10 blur-3xl animate-float\" style={{ animationDelay: '3s' }}></div>\n      </div>\n\n      {/* Content */}\n      <div className=\"container mx-auto px-4 md:px-6 relative z-20 text-center flex flex-col justify-center min-h-[75vh] pt-20 md:pt-36\">\n        <div className=\"space-y-4 md:space-y-6\">\n          <h1 className=\"text-3xl sm:text-4xl md:text-6xl lg:text-7xl font-display font-bold tracking-wider text-white leading-tight\">\n            <span className=\"inline-block\">{title}</span>\n          </h1>\n          <p className=\"text-lg sm:text-xl md:text-2xl text-gray-300 max-w-3xl mx-auto leading-relaxed px-2\">\n            {subtitle}\n          </p>\n        </div>\n      </div>\n\n      {/* Scroll indicator */}\n      <div className=\"absolute bottom-6 md:bottom-10 left-1/2 transform -translate-x-1/2 flex flex-col items-center z-20\">\n        {/* Mobile: Swipe indicator */}\n        <div className=\"block md:hidden\">\n          <span className=\"text-white text-xs mb-2\">Swipe Up</span>\n          <div className=\"flex items-center justify-center\">\n            <div className=\"w-8 h-8 flex items-center justify-center\">\n              <svg className=\"w-6 h-6 text-white animate-bounce\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                {/* Finger */}\n                <path d=\"M12 2c-1.1 0-2 .9-2 2v8c0 .55-.45 1-1 1s-1-.45-1-1V8c0-1.1-.9-2-2-2s-2 .9-2 2v4c0 .55-.45 1-1 1s-1-.45-1-1V10c0-1.1-.9-2-2-2s-2 .9-2 2v8c0 3.31 2.69 6 6 6h6c3.31 0 6-2.69 6-6V4c0-1.1-.9-2-2-2s-2 .9-2 2v8c0 .55-.45 1-1 1s-1-.45-1-1V4c0-1.1-.9-2-2-2z\"/>\n                {/* Upward arrow */}\n                <path d=\"M12 8l-3 3h2v4h2v-4h2l-3-3z\" fill=\"white\" opacity=\"0.8\"/>\n              </svg>\n            </div>\n          </div>\n        </div>\n\n        {/* Desktop: Mouse scroll indicator */}\n        <div className=\"hidden md:flex md:flex-col md:items-center\">\n          <span className=\"text-white text-sm mb-2\">Scroll Down</span>\n          <div className=\"w-6 h-10 border-2 border-white rounded-full flex justify-center items-start pt-2\">\n            <div className=\"w-1 h-2 bg-white rounded-full animate-bounce\"></div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Hero;\n"], "names": [], "mappings": ";;;;;AAaA,MAAM,OAAO,CAAC,EACZ,KAAK,EACL,QAAQ,EACR,iBAAiB,EACjB,iBAAiB,EACjB,mBAAmB,EACnB,mBAAmB,EACnB,kBAAkB,qBAAqB,EAC7B;IACV,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBACC,WAAU;gBACV,OAAO;oBACL,iBAAiB,CAAC,KAAK,EAAE,gBAAgB,EAAE,CAAC;gBAC9C;;;;;;0BAIF,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;kCAGf,8OAAC;wBAAI,WAAU;kCACZ,MAAM,IAAI,CAAC;4BAAE,QAAQ;wBAAG,GAAG,GAAG,CAAC,CAAC,GAAG,kBAClC,8OAAC;gCAAmB,WAAU;gCAAgD,OAAO;oCAAE,WAAW,GAAG,IAAI,EAAE,EAAE,CAAC;oCAAE,gBAAgB,GAAG,IAAI,IAAI,CAAC,CAAC;gCAAC;+BAApI,CAAC,EAAE,EAAE,GAAG;;;;;;;;;;kCAGtB,8OAAC;wBAAI,WAAU;kCACZ,MAAM,IAAI,CAAC;4BAAE,QAAQ;wBAAG,GAAG,GAAG,CAAC,CAAC,GAAG,kBAClC,8OAAC;gCAAmB,WAAU;gCAAgD,OAAO;oCAAE,YAAY,GAAG,IAAI,EAAE,EAAE,CAAC;oCAAE,gBAAgB,GAAG,IAAI,IAAI,CAAC,CAAC;gCAAC;+BAArI,CAAC,EAAE,EAAE,GAAG;;;;;;;;;;kCAKtB,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;wBAA6F,OAAO;4BAAE,gBAAgB;wBAAK;;;;;;;;;;;;0BAI5I,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCACZ,cAAA,8OAAC;gCAAK,WAAU;0CAAgB;;;;;;;;;;;sCAElC,8OAAC;4BAAE,WAAU;sCACV;;;;;;;;;;;;;;;;;0BAMP,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAK,WAAU;0CAA0B;;;;;;0CAC1C,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;wCAAoC,MAAK;wCAAe,SAAQ;;0DAE7E,8OAAC;gDAAK,GAAE;;;;;;0DAER,8OAAC;gDAAK,GAAE;gDAA8B,MAAK;gDAAQ,SAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAOnE,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAK,WAAU;0CAA0B;;;;;;0CAC1C,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM3B;uCAEe"}}, {"offset": {"line": 772, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 778, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/TGRS%20Website/tgrs-website/src/components/FeatureCard.tsx"], "sourcesContent": ["import React from \"react\";\n\ninterface FeatureCardProps {\n  title: string;\n  description: string;\n  icon?: React.ReactNode;\n  className?: string;\n  tiltDirection?: \"left\" | \"right\" | \"none\";\n  pattern?: \"dots\" | \"grid\" | \"hexagon\" | \"circuit\" | \"none\";\n}\n\nconst FeatureCard = ({\n  title,\n  description,\n  icon,\n  className = \"\",\n  tiltDirection = \"none\",\n  pattern = \"circuit\",\n}: FeatureCardProps) => {\n  const getTiltClass = () => {\n    switch (tiltDirection) {\n      case \"left\":\n        return \"card-tilt-left\";\n      case \"right\":\n        return \"card-tilt-right\";\n      default:\n        return \"\";\n    }\n  };\n\n  const getPatternClass = () => {\n    switch (pattern) {\n      case \"dots\":\n        return \"pattern-dots\";\n      case \"grid\":\n        return \"pattern-grid\";\n      case \"hexagon\":\n        return \"pattern-hexagon\";\n      case \"circuit\":\n        return \"pattern-circuit\";\n      default:\n        return \"\";\n    }\n  };\n\n  return (\n    <div className={`glass-enhanced rounded-lg p-3 sm:p-4 md:p-5 border border-neon-orange/30 hover:border-neon-orange/70 transition-all duration-500 hover:shadow-neon-strong group relative h-full flex flex-col ${getTiltClass()} ${getPatternClass()} ${className}`}>\n      {/* Background overlay for pattern */}\n      <div className=\"absolute inset-0 bg-black/20 rounded-xl\"></div>\n\n      {/* Content */}\n      <div className=\"relative z-10 flex flex-col h-full\">\n        {icon && (\n          <div className=\"mb-6 sm:mb-4 md:mb-3 text-neon-orange group-hover:text-white transition-all duration-300 relative icon-container\">\n            <div className=\"w-6 h-6 sm:w-7 sm:h-7 md:w-8 md:h-8 relative transform group-hover:scale-105 transition-transform duration-300 origin-center\">\n              {/* Animated particles around the icon */}\n              <div className=\"absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-500 pointer-events-none\">\n                {/* Top particles */}\n                <div className=\"absolute w-1 h-1 bg-neon-orange rounded-full animate-ping\" style={{top: '-4px', left: '20%', animationDelay: '0s'}}></div>\n                <div className=\"absolute w-0.5 h-0.5 bg-yellow-400 rounded-full animate-pulse\" style={{top: '-2px', right: '25%', animationDelay: '0.4s'}}></div>\n\n                {/* Right particles */}\n                <div className=\"absolute w-1 h-1 bg-orange-400 rounded-full animate-ping\" style={{right: '-4px', top: '30%', animationDelay: '0.2s'}}></div>\n                <div className=\"absolute w-0.5 h-0.5 bg-yellow-300 rounded-full animate-pulse\" style={{right: '-2px', bottom: '35%', animationDelay: '0.7s'}}></div>\n\n                {/* Bottom particles */}\n                <div className=\"absolute w-1 h-1 bg-neon-orange rounded-full animate-ping\" style={{bottom: '-4px', right: '20%', animationDelay: '0.5s'}}></div>\n                <div className=\"absolute w-0.5 h-0.5 bg-orange-500 rounded-full animate-pulse\" style={{bottom: '-2px', left: '30%', animationDelay: '0.9s'}}></div>\n\n                {/* Left particles */}\n                <div className=\"absolute w-1 h-1 bg-yellow-400 rounded-full animate-ping\" style={{left: '-4px', bottom: '25%', animationDelay: '0.3s'}}></div>\n                <div className=\"absolute w-0.5 h-0.5 bg-neon-orange rounded-full animate-pulse\" style={{left: '-2px', top: '40%', animationDelay: '0.6s'}}></div>\n              </div>\n              <div className=\"w-full h-full relative z-10\">\n                {icon}\n              </div>\n            </div>\n          </div>\n        )}\n        <h3 className=\"text-sm sm:text-base md:text-lg font-display font-bold text-white mb-1 sm:mb-2 group-hover:text-neon-orange transition-colors duration-300\">\n          {title}\n        </h3>\n        <p className=\"text-xs sm:text-sm md:text-base text-gray-400 group-hover:text-gray-200 transition-colors duration-300 leading-relaxed flex-grow\">\n          {description}\n        </p>\n      </div>\n\n      {/* Decorative corner elements */}\n      <div className=\"absolute top-2 right-2 w-3 h-3 border-t-2 border-r-2 border-neon-orange/30 group-hover:border-neon-orange transition-colors duration-300 z-20\"></div>\n      <div className=\"absolute bottom-2 left-2 w-3 h-3 border-b-2 border-l-2 border-neon-orange/30 group-hover:border-neon-orange transition-colors duration-300 z-20\"></div>\n    </div>\n  );\n};\n\nexport default FeatureCard;\n"], "names": [], "mappings": ";;;;;AAWA,MAAM,cAAc,CAAC,EACnB,KAAK,EACL,WAAW,EACX,IAAI,EACJ,YAAY,EAAE,EACd,gBAAgB,MAAM,EACtB,UAAU,SAAS,EACF;IACjB,MAAM,eAAe;QACnB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,kBAAkB;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAC,8LAA8L,EAAE,eAAe,CAAC,EAAE,kBAAkB,CAAC,EAAE,WAAW;;0BAEjQ,8OAAC;gBAAI,WAAU;;;;;;0BAGf,8OAAC;gBAAI,WAAU;;oBACZ,sBACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC;4CAAI,WAAU;4CAA4D,OAAO;gDAAC,KAAK;gDAAQ,MAAM;gDAAO,gBAAgB;4CAAI;;;;;;sDACjI,8OAAC;4CAAI,WAAU;4CAAgE,OAAO;gDAAC,KAAK;gDAAQ,OAAO;gDAAO,gBAAgB;4CAAM;;;;;;sDAGxI,8OAAC;4CAAI,WAAU;4CAA2D,OAAO;gDAAC,OAAO;gDAAQ,KAAK;gDAAO,gBAAgB;4CAAM;;;;;;sDACnI,8OAAC;4CAAI,WAAU;4CAAgE,OAAO;gDAAC,OAAO;gDAAQ,QAAQ;gDAAO,gBAAgB;4CAAM;;;;;;sDAG3I,8OAAC;4CAAI,WAAU;4CAA4D,OAAO;gDAAC,QAAQ;gDAAQ,OAAO;gDAAO,gBAAgB;4CAAM;;;;;;sDACvI,8OAAC;4CAAI,WAAU;4CAAgE,OAAO;gDAAC,QAAQ;gDAAQ,MAAM;gDAAO,gBAAgB;4CAAM;;;;;;sDAG1I,8OAAC;4CAAI,WAAU;4CAA2D,OAAO;gDAAC,MAAM;gDAAQ,QAAQ;gDAAO,gBAAgB;4CAAM;;;;;;sDACrI,8OAAC;4CAAI,WAAU;4CAAiE,OAAO;gDAAC,MAAM;gDAAQ,KAAK;gDAAO,gBAAgB;4CAAM;;;;;;;;;;;;8CAE1I,8OAAC;oCAAI,WAAU;8CACZ;;;;;;;;;;;;;;;;;kCAKT,8OAAC;wBAAG,WAAU;kCACX;;;;;;kCAEH,8OAAC;wBAAE,WAAU;kCACV;;;;;;;;;;;;0BAKL,8OAAC;gBAAI,WAAU;;;;;;0BACf,8OAAC;gBAAI,WAAU;;;;;;;;;;;;AAGrB;uCAEe"}}, {"offset": {"line": 994, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1000, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/TGRS%20Website/tgrs-website/src/components/Button.tsx"], "sourcesContent": ["import React from \"react\";\nimport Link from \"next/link\";\n\ninterface ButtonProps {\n  children: React.ReactNode;\n  href?: string;\n  variant?: \"primary\" | \"secondary\" | \"outline\" | \"ghost\";\n  size?: \"sm\" | \"md\" | \"lg\";\n  className?: string;\n  onClick?: () => void;\n}\n\nconst Button = ({\n  children,\n  href,\n  variant = \"primary\",\n  size = \"md\",\n  className = \"\",\n  onClick,\n}: ButtonProps) => {\n  const baseStyles = \"inline-flex items-center justify-center font-medium transition-all duration-300 rounded-full\";\n\n  const variantStyles = {\n    primary: \"bg-gradient-to-r from-neon-orange to-neon-red text-white hover:shadow-neon\",\n    secondary: \"bg-neon-orange text-white hover:bg-neon-orange/90 hover:shadow-neon-orange\",\n    outline: \"border border-neon-orange text-neon-orange hover:bg-neon-orange/10\",\n    ghost: \"text-white hover:text-neon-orange hover:bg-white/5\",\n  };\n\n  const sizeStyles = {\n    sm: \"text-xs px-3 py-1.5\",\n    md: \"text-sm px-4 py-2\",\n    lg: \"text-base px-6 py-3\",\n  };\n\n  const styles = `${baseStyles} ${variantStyles[variant]} ${sizeStyles[size]} ${className}`;\n\n  if (href) {\n    // Check if it's an external link\n    const isExternal = href.startsWith('http') || href.startsWith('https');\n\n    if (isExternal) {\n      return (\n        <a href={href} className={styles} target=\"_blank\" rel=\"noopener noreferrer\">\n          {children}\n        </a>\n      );\n    }\n\n    return (\n      <Link href={href} className={styles}>\n        {children}\n      </Link>\n    );\n  }\n\n  return (\n    <button className={styles} onClick={onClick}>\n      {children}\n    </button>\n  );\n};\n\nexport default Button;\n"], "names": [], "mappings": ";;;;AACA;;;AAWA,MAAM,SAAS,CAAC,EACd,QAAQ,EACR,IAAI,EACJ,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,YAAY,EAAE,EACd,OAAO,EACK;IACZ,MAAM,aAAa;IAEnB,MAAM,gBAAgB;QACpB,SAAS;QACT,WAAW;QACX,SAAS;QACT,OAAO;IACT;IAEA,MAAM,aAAa;QACjB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,SAAS,GAAG,WAAW,CAAC,EAAE,aAAa,CAAC,QAAQ,CAAC,CAAC,EAAE,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE,WAAW;IAEzF,IAAI,MAAM;QACR,iCAAiC;QACjC,MAAM,aAAa,KAAK,UAAU,CAAC,WAAW,KAAK,UAAU,CAAC;QAE9D,IAAI,YAAY;YACd,qBACE,8OAAC;gBAAE,MAAM;gBAAM,WAAW;gBAAQ,QAAO;gBAAS,KAAI;0BACnD;;;;;;QAGP;QAEA,qBACE,8OAAC,4JAAA,CAAA,UAAI;YAAC,MAAM;YAAM,WAAW;sBAC1B;;;;;;IAGP;IAEA,qBACE,8OAAC;QAAO,WAAW;QAAQ,SAAS;kBACjC;;;;;;AAGP;uCAEe"}}, {"offset": {"line": 1058, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1064, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/TGRS%20Website/tgrs-website/src/components/BouncingShapes.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/BouncingShapes.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/BouncingShapes.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAqS,GAClU,mEACA"}}, {"offset": {"line": 1072, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1078, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/TGRS%20Website/tgrs-website/src/components/BouncingShapes.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/BouncingShapes.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/BouncingShapes.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAiR,GAC9S,+CACA"}}, {"offset": {"line": 1086, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1092, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": ""}}, {"offset": {"line": 1096, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1102, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/TGRS%20Website/tgrs-website/src/app/page.tsx"], "sourcesContent": ["import Navbar from \"@/components/Navbar\";\nimport Footer from \"@/components/Footer\";\nimport Hero from \"@/components/Hero\";\nimport FeatureCard from \"@/components/FeatureCard\";\nimport Button from \"@/components/Button\";\nimport BouncingShapes from \"@/components/BouncingShapes\";\nimport { <PERSON>ada<PERSON> } from \"next\";\n\nexport const metadata: Metadata = {\n  title: \"TGRS - #1 Telugu FiveM Roleplay Server | Best Telugu Gaming Community\",\n  description: \"Join TGRS - The premier Telugu FiveM roleplay server! Experience immersive GTA 5 roleplay with Telugu community, custom scripts, realistic economy, and 24/7 uptime. Best Telugu gaming server for FiveM enthusiasts.\",\n  keywords: [\n    // Primary Telugu FiveM Keywords\n    \"Telugu FiveM server\", \"FiveM Telugu\", \"Telugu gaming\", \"GTA 5 roleplay Telugu\",\n    \"Telugu gaming community\", \"FiveM roleplay server\", \"Telugu gamers\", \"TGRS\",\n    \"Telugu online gaming\", \"FiveM RP Telugu\", \"GTA roleplay Telugu\", \"Telugu gaming server\",\n\n    // GTA Telugu Keywords\n    \"GTA 5 Telugu\", \"Telugu GTA 5\", \"GTA Telugu\", \"Telugu GTA\", \"GTA 5 Telugu server\",\n    \"Telugu GTA 5 server\", \"GTA V Telugu\", \"Telugu GTA V\", \"GTA roleplay Telugu\",\n\n    // SAMP Telugu Keywords\n    \"SAMP Telugu\", \"Telugu SAMP\", \"SAMP Telugu server\", \"Telugu SAMP server\",\n    \"San Andreas Telugu\", \"Telugu San Andreas\", \"SAMP roleplay Telugu\",\n\n    // Server Keywords\n    \"Telugu server\", \"server Telugu\", \"Telugu gaming server\", \"Telugu roleplay server\",\n    \"Telugu multiplayer server\", \"Telugu community server\", \"Telugu game server\",\n\n    // Regional Keywords\n    \"Indian FiveM server\", \"South India gaming\", \"Hyderabad gaming\", \"Telugu esports\",\n    \"Telangana gaming\", \"Andhra Pradesh gaming\", \"Hyderabad FiveM\", \"Telangana FiveM\",\n    \"Andhra Pradesh FiveM\", \"Vijayawada gaming\", \"Visakhapatnam gaming\", \"Warangal gaming\",\n\n    // Gaming Keywords\n    \"Telugu online gaming\", \"Telugu multiplayer gaming\", \"Telugu gaming community\",\n    \"Telugu gaming discord\", \"Telugu gaming YouTube\", \"Telugu gaming clan\",\n    \"Telugu gaming group\", \"Telugu gaming family\", \"Telugu gaming friends\",\n\n    // Cultural Keywords\n    \"Telugu culture gaming\", \"Telugu tradition gaming\", \"Telugu language gaming\",\n    \"Telugu speaking gamers\", \"Telugu gaming culture\", \"Telugu gaming pride\",\n\n    // Additional Keywords\n    \"తెలుగు గేమింగ్\", \"తెలుగు గేమర్స్\", \"తెలుగు సర్వర్\", \"Telugu gaming 2024\",\n    \"best Telugu gaming\", \"top Telugu gaming\", \"popular Telugu gaming\"\n  ],\n  openGraph: {\n    title: \"TGRS - #1 Telugu FiveM Roleplay Server | Best Telugu Gaming Community\",\n    description: \"Join TGRS - The premier Telugu FiveM roleplay server! Experience immersive GTA 5 roleplay with Telugu community, custom scripts, realistic economy, and 24/7 uptime.\",\n    images: [\"/assets/tgrs-og-image.jpg\"],\n  },\n  alternates: {\n    canonical: \"/\",\n  },\n};\n\nexport default function Home() {\n  return (\n    <>\n      <Navbar />\n\n      {/* Hero Section */}\n      <Hero\n        title=\"WELCOME TO TGRS\"\n        subtitle=\"Experience immersive roleplay in a unique Telugu community with custom features and scripts\"\n        primaryButtonText=\"Join Discord\"\n        primaryButtonHref=\"https://discord.gg/GAMravHDnB\"\n        secondaryButtonText=\"Learn More\"\n        secondaryButtonHref=\"#features\"\n      />\n\n      {/* Section Divider */}\n      <div className=\"section-divider\"></div>\n\n      {/* Character Showcase Section */}\n      <section id=\"about\" className=\"py-8 md:py-20 relative overflow-hidden min-h-screen\">\n        {/* Background Video */}\n        <div className=\"absolute inset-0 w-full h-full\">\n          {/* Desktop Video */}\n          <iframe\n            src=\"https://www.youtube.com/embed/pZbzoJAj4TE?autoplay=1&mute=1&loop=1&playlist=pZbzoJAj4TE&controls=0&showinfo=0&rel=0&iv_load_policy=3&modestbranding=1&disablekb=1&fs=0&cc_load_policy=0&start=33\"\n            className=\"w-full h-full hidden md:block\"\n            style={{\n              position: 'absolute',\n              top: '50%',\n              left: '50%',\n              width: '100vw',\n              height: '56.25vw', // 16:9 aspect ratio\n              minHeight: '100vh',\n              minWidth: '177.77vh', // 16:9 aspect ratio\n              transform: 'translate(-50%, -50%)',\n              pointerEvents: 'none',\n              border: 'none'\n            }}\n            allow=\"autoplay; encrypted-media\"\n            allowFullScreen={false}\n          />\n\n          {/* Mobile Video */}\n          <div className=\"block md:hidden absolute inset-0 w-full h-full\">\n            <iframe\n              src=\"https://www.youtube.com/embed/waN7QSRC-YU?autoplay=1&mute=1&loop=1&playlist=waN7QSRC-YU&controls=0&showinfo=0&rel=0&iv_load_policy=3&modestbranding=1&disablekb=1&fs=0&cc_load_policy=0&start=0&end=45&playsinline=1&enablejsapi=0\"\n              className=\"absolute\"\n              style={{\n                top: '-50%',\n                left: '-50%',\n                width: '200%',\n                height: '200%',\n                minHeight: '200%',\n                minWidth: '200%',\n                pointerEvents: 'none',\n                border: 'none',\n                objectFit: 'cover'\n              }}\n              allow=\"autoplay; encrypted-media\"\n              allowFullScreen={false}\n            />\n          </div>\n\n          {/* Dark overlay */}\n          <div className=\"absolute inset-0 bg-black/60\"></div>\n        </div>\n\n        <div className=\"container mx-auto px-4 md:px-6 relative z-10\">\n          <div className=\"text-center mb-6 sm:mb-8 md:mb-12 lg:mb-16\">\n            <h2 className=\"text-xl sm:text-2xl md:text-3xl lg:text-4xl font-display font-bold text-white mb-3 sm:mb-4 wave-heading-bg\">\n              Create Your <span className=\"text-neon-orange\">Unique Story</span>\n            </h2>\n            <p className=\"text-xs sm:text-sm md:text-base lg:text-lg text-gray-400 max-w-3xl mx-auto px-2\">\n              Step into Los Santos and become anyone you want to be. Your story, your rules.\n            </p>\n          </div>\n\n          {/* Character Cards */}\n          <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4 md:gap-6 lg:gap-8 mb-6 sm:mb-8 md:mb-12 lg:mb-16 px-2 sm:px-0\">\n            {/* Criminal Character */}\n            <div className=\"glass-enhanced rounded-lg sm:rounded-xl p-3 sm:p-4 md:p-5 lg:p-6 border border-red-500/30 hover:border-red-500/70 transition-all duration-500 hover:shadow-neon-strong group relative h-full flex flex-col pattern-circuit\">\n              <div className=\"absolute inset-0 bg-black/20 rounded-xl\"></div>\n\n              <div className=\"relative z-10 flex flex-col h-full\">\n                <div className=\"mb-3 text-red-500 group-hover:text-white transition-all duration-300 relative icon-container\">\n                  <div className=\"w-8 h-8 md:w-10 md:h-10 relative transform group-hover:scale-105 transition-transform duration-300 origin-center\">\n                    <div className=\"absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-500 pointer-events-none\">\n                      {/* Top particles */}\n                      <div className=\"absolute w-1 h-1 bg-red-500 rounded-full animate-ping\" style={{top: '-4px', left: '20%', animationDelay: '0s'}}></div>\n                      <div className=\"absolute w-0.5 h-0.5 bg-red-400 rounded-full animate-pulse\" style={{top: '-2px', right: '25%', animationDelay: '0.4s'}}></div>\n\n                      {/* Right particles */}\n                      <div className=\"absolute w-1 h-1 bg-red-600 rounded-full animate-ping\" style={{right: '-4px', top: '30%', animationDelay: '0.2s'}}></div>\n                      <div className=\"absolute w-0.5 h-0.5 bg-red-300 rounded-full animate-pulse\" style={{right: '-2px', bottom: '35%', animationDelay: '0.7s'}}></div>\n\n                      {/* Bottom particles */}\n                      <div className=\"absolute w-1 h-1 bg-red-500 rounded-full animate-ping\" style={{bottom: '-4px', right: '20%', animationDelay: '0.5s'}}></div>\n                      <div className=\"absolute w-0.5 h-0.5 bg-red-700 rounded-full animate-pulse\" style={{bottom: '-2px', left: '30%', animationDelay: '0.9s'}}></div>\n\n                      {/* Left particles */}\n                      <div className=\"absolute w-1 h-1 bg-red-400 rounded-full animate-ping\" style={{left: '-4px', bottom: '25%', animationDelay: '0.3s'}}></div>\n                      <div className=\"absolute w-0.5 h-0.5 bg-red-500 rounded-full animate-pulse\" style={{left: '-2px', top: '40%', animationDelay: '0.6s'}}></div>\n                    </div>\n                    <svg className=\"w-full h-full relative z-10\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                      <path d=\"M10 2L3 7v11h4v-6h6v6h4V7l-7-5z\"/>\n                    </svg>\n                  </div>\n                </div>\n                <h3 className=\"text-lg sm:text-xl md:text-2xl font-display font-bold text-white mb-2 group-hover:text-red-400 transition-colors duration-300\">\n                  Criminal\n                </h3>\n                <p className=\"text-sm sm:text-base text-gray-400 group-hover:text-gray-200 transition-colors duration-300 leading-relaxed flex-grow\">\n                  Build your empire from the streets. Rob banks, run illegal businesses, and climb the criminal hierarchy.\n                </p>\n              </div>\n\n              <div className=\"absolute top-2 right-2 w-3 h-3 border-t-2 border-r-2 border-red-500/30 group-hover:border-red-500 transition-colors duration-300 z-20\"></div>\n              <div className=\"absolute bottom-2 left-2 w-3 h-3 border-b-2 border-l-2 border-red-500/30 group-hover:border-red-500 transition-colors duration-300 z-20\"></div>\n            </div>\n\n            {/* Police Character */}\n            <div className=\"glass-enhanced rounded-xl p-4 sm:p-5 md:p-6 border border-blue-500/30 hover:border-blue-500/70 transition-all duration-500 hover:shadow-neon-strong group relative h-full flex flex-col pattern-hexagon\">\n              <div className=\"absolute inset-0 bg-black/20 rounded-xl\"></div>\n\n              <div className=\"relative z-10 flex flex-col h-full\">\n                <div className=\"mb-3 text-blue-500 group-hover:text-white transition-all duration-300 relative icon-container\">\n                  <div className=\"w-8 h-8 md:w-10 md:h-10 relative transform group-hover:scale-105 transition-transform duration-300 origin-center\">\n                    <div className=\"absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-500 pointer-events-none\">\n                      {/* Top particles */}\n                      <div className=\"absolute w-1 h-1 bg-blue-500 rounded-full animate-ping\" style={{top: '-4px', left: '20%', animationDelay: '0s'}}></div>\n                      <div className=\"absolute w-0.5 h-0.5 bg-blue-400 rounded-full animate-pulse\" style={{top: '-2px', right: '25%', animationDelay: '0.4s'}}></div>\n\n                      {/* Right particles */}\n                      <div className=\"absolute w-1 h-1 bg-blue-600 rounded-full animate-ping\" style={{right: '-4px', top: '30%', animationDelay: '0.2s'}}></div>\n                      <div className=\"absolute w-0.5 h-0.5 bg-blue-300 rounded-full animate-pulse\" style={{right: '-2px', bottom: '35%', animationDelay: '0.7s'}}></div>\n\n                      {/* Bottom particles */}\n                      <div className=\"absolute w-1 h-1 bg-blue-500 rounded-full animate-ping\" style={{bottom: '-4px', right: '20%', animationDelay: '0.5s'}}></div>\n                      <div className=\"absolute w-0.5 h-0.5 bg-blue-700 rounded-full animate-pulse\" style={{bottom: '-2px', left: '30%', animationDelay: '0.9s'}}></div>\n\n                      {/* Left particles */}\n                      <div className=\"absolute w-1 h-1 bg-blue-400 rounded-full animate-ping\" style={{left: '-4px', bottom: '25%', animationDelay: '0.3s'}}></div>\n                      <div className=\"absolute w-0.5 h-0.5 bg-blue-500 rounded-full animate-pulse\" style={{left: '-2px', top: '40%', animationDelay: '0.6s'}}></div>\n                    </div>\n                    <svg className=\"w-full h-full relative z-10\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                      <path fillRule=\"evenodd\" d=\"M9.383 3.076A1 1 0 0110 4v12a1 1 0 01-1.707.707L4.586 13H2a1 1 0 01-1-1V8a1 1 0 011-1h2.586l3.707-3.707a1 1 0 011.09-.217z\" clipRule=\"evenodd\"/>\n                    </svg>\n                  </div>\n                </div>\n                <h3 className=\"text-lg sm:text-xl md:text-2xl font-display font-bold text-white mb-2 group-hover:text-blue-400 transition-colors duration-300\">\n                  Legal\n                </h3>\n                <p className=\"text-sm sm:text-base text-gray-400 group-hover:text-gray-200 transition-colors duration-300 leading-relaxed flex-grow\">\n                  Work in the legal system. Practice law, represent clients, handle legal cases, and ensure justice is served.\n                </p>\n              </div>\n\n              <div className=\"absolute top-2 right-2 w-3 h-3 border-t-2 border-r-2 border-blue-500/30 group-hover:border-blue-500 transition-colors duration-300 z-20\"></div>\n              <div className=\"absolute bottom-2 left-2 w-3 h-3 border-b-2 border-l-2 border-blue-500/30 group-hover:border-blue-500 transition-colors duration-300 z-20\"></div>\n            </div>\n\n            {/* Civilian Character */}\n            <div className=\"glass-enhanced rounded-xl p-4 sm:p-5 md:p-6 border border-green-500/30 hover:border-green-500/70 transition-all duration-500 hover:shadow-neon-strong group relative h-full flex flex-col pattern-dots\">\n              <div className=\"absolute inset-0 bg-black/20 rounded-xl\"></div>\n\n              <div className=\"relative z-10 flex flex-col h-full\">\n                <div className=\"mb-3 text-green-500 group-hover:text-white transition-all duration-300 relative icon-container\">\n                  <div className=\"w-8 h-8 md:w-10 md:h-10 relative transform group-hover:scale-105 transition-transform duration-300 origin-center\">\n                    <div className=\"absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-500 pointer-events-none\">\n                      {/* Top particles */}\n                      <div className=\"absolute w-1 h-1 bg-green-500 rounded-full animate-ping\" style={{top: '-4px', left: '20%', animationDelay: '0s'}}></div>\n                      <div className=\"absolute w-0.5 h-0.5 bg-green-400 rounded-full animate-pulse\" style={{top: '-2px', right: '25%', animationDelay: '0.4s'}}></div>\n\n                      {/* Right particles */}\n                      <div className=\"absolute w-1 h-1 bg-green-600 rounded-full animate-ping\" style={{right: '-4px', top: '30%', animationDelay: '0.2s'}}></div>\n                      <div className=\"absolute w-0.5 h-0.5 bg-green-300 rounded-full animate-pulse\" style={{right: '-2px', bottom: '35%', animationDelay: '0.7s'}}></div>\n\n                      {/* Bottom particles */}\n                      <div className=\"absolute w-1 h-1 bg-green-500 rounded-full animate-ping\" style={{bottom: '-4px', right: '20%', animationDelay: '0.5s'}}></div>\n                      <div className=\"absolute w-0.5 h-0.5 bg-green-700 rounded-full animate-pulse\" style={{bottom: '-2px', left: '30%', animationDelay: '0.9s'}}></div>\n\n                      {/* Left particles */}\n                      <div className=\"absolute w-1 h-1 bg-green-400 rounded-full animate-ping\" style={{left: '-4px', bottom: '25%', animationDelay: '0.3s'}}></div>\n                      <div className=\"absolute w-0.5 h-0.5 bg-green-500 rounded-full animate-pulse\" style={{left: '-2px', top: '40%', animationDelay: '0.6s'}}></div>\n                    </div>\n                    <svg className=\"w-full h-full relative z-10\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                      <path d=\"M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z\"/>\n                    </svg>\n                  </div>\n                </div>\n                <h3 className=\"text-lg sm:text-xl md:text-2xl font-display font-bold text-white mb-2 group-hover:text-green-400 transition-colors duration-300\">\n                  Civilian\n                </h3>\n                <p className=\"text-sm sm:text-base text-gray-400 group-hover:text-gray-200 transition-colors duration-300 leading-relaxed flex-grow\">\n                  Live a normal life. Start businesses, buy properties, and build relationships in the community.\n                </p>\n              </div>\n\n              <div className=\"absolute top-2 right-2 w-3 h-3 border-t-2 border-r-2 border-green-500/30 group-hover:border-green-500 transition-colors duration-300 z-20\"></div>\n              <div className=\"absolute bottom-2 left-2 w-3 h-3 border-b-2 border-l-2 border-green-500/30 group-hover:border-green-500 transition-colors duration-300 z-20\"></div>\n            </div>\n          </div>\n\n          {/* Stats Section */}\n          <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4 sm:gap-6 md:gap-8 px-2 sm:px-0\">\n            <div className=\"text-center p-3 sm:p-4\">\n              <div className=\"text-2xl sm:text-3xl md:text-4xl font-display font-bold text-neon-orange mb-2\">80+</div>\n              <div className=\"text-xs sm:text-sm md:text-base text-gray-400\">Active Players</div>\n            </div>\n            <div className=\"text-center p-3 sm:p-4\">\n              <div className=\"text-2xl sm:text-3xl md:text-4xl font-display font-bold text-neon-orange mb-2\">24/7</div>\n              <div className=\"text-xs sm:text-sm md:text-base text-gray-400\">Server Uptime</div>\n            </div>\n            <div className=\"text-center p-3 sm:p-4\">\n              <div className=\"text-2xl sm:text-3xl md:text-4xl font-display font-bold text-neon-orange mb-2\">10+</div>\n              <div className=\"text-xs sm:text-sm md:text-base text-gray-400\">Custom Scripts</div>\n            </div>\n            <div className=\"text-center p-3 sm:p-4\">\n              <div className=\"text-2xl sm:text-3xl md:text-4xl font-display font-bold text-neon-orange mb-2\">100%</div>\n              <div className=\"text-xs sm:text-sm md:text-base text-gray-400\">Telugu Community</div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Section Divider */}\n      <div className=\"section-divider\"></div>\n\n      {/* Features Section */}\n      <section id=\"features\" className=\"py-12 md:py-20 relative bg-black/50\">\n        <div className=\"container mx-auto px-6 sm:px-12 md:px-16 lg:px-20 xl:px-24\">\n          <div className=\"text-center mb-8 md:mb-12\">\n            <h2 className=\"text-2xl sm:text-3xl md:text-4xl font-display font-bold text-white mb-4 wave-heading-bg\">\n              <span className=\"text-neon-orange\">UNIQUE</span> FEATURES\n            </h2>\n            <p className=\"text-sm sm:text-base md:text-lg text-gray-400 max-w-3xl mx-auto px-2\">\n              Discover what makes our server special with these exclusive features\n            </p>\n          </div>\n\n          <div className=\"max-w-6xl mx-auto\">\n            <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-5 md:gap-6 items-stretch\">\n            <FeatureCard\n              title=\"24/7 Server Uptime\"\n              description=\"Our server is online 24/7, ensuring you can play whenever you want without interruptions.\"\n              tiltDirection=\"left\"\n              pattern=\"circuit\"\n              className=\"animate-slide-in-left\"\n              icon={\n                <svg className=\"w-10 h-10\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                </svg>\n              }\n            />\n\n            <FeatureCard\n              title=\"Realistic Economy\"\n              description=\"Experience a balanced and realistic economy system with multiple jobs and business opportunities.\"\n              tiltDirection=\"right\"\n              pattern=\"hexagon\"\n              className=\"animate-slide-in-right\"\n              icon={\n                <svg className=\"w-10 h-10\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                </svg>\n              }\n            />\n\n            <FeatureCard\n              title=\"Immersive Roleplay\"\n              description=\"Dive into a rich roleplay experience with custom scripts and scenarios designed for the Telugu community.\"\n              tiltDirection=\"left\"\n              pattern=\"dots\"\n              className=\"animate-slide-in-left\"\n              icon={\n                <svg className=\"w-10 h-10\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M7 4v16M17 4v16M3 8h4m10 0h4M3 12h18M3 16h4m10 0h4M4 20h16a1 1 0 001-1V5a1 1 0 00-1-1H4a1 1 0 00-1 1v14a1 1 0 001 1z\" />\n                </svg>\n              }\n            />\n\n            <FeatureCard\n              title=\"Friendly Community\"\n              description=\"Join a welcoming Telugu community that values respect, creativity, and fun roleplay experiences.\"\n              tiltDirection=\"right\"\n              pattern=\"grid\"\n              className=\"animate-slide-in-right\"\n              icon={\n                <svg className=\"w-10 h-10\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\" />\n                </svg>\n              }\n            />\n\n            <FeatureCard\n              title=\"Supportive Staff\"\n              description=\"Our dedicated staff team is always ready to help, ensuring a smooth and enjoyable experience for all players.\"\n              tiltDirection=\"left\"\n              pattern=\"circuit\"\n              className=\"animate-slide-in-left\"\n              icon={\n                <svg className=\"w-10 h-10\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192l-3.536 3.536M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-5 0a4 4 0 11-8 0 4 4 0 018 0z\" />\n                </svg>\n              }\n            />\n\n            <FeatureCard\n              title=\"Active Development\"\n              description=\"Regular updates and new features keep the server fresh and exciting with continuous improvements.\"\n              tiltDirection=\"right\"\n              pattern=\"hexagon\"\n              className=\"animate-slide-in-right\"\n              icon={\n                <svg className=\"w-10 h-10\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4\" />\n                </svg>\n              }\n            />\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Section Divider */}\n      <div className=\"section-divider\"></div>\n\n      {/* Join Section */}\n      <section id=\"join\" className=\"py-8 sm:py-12 md:py-20 relative bg-black/50\">\n        <BouncingShapes />\n        <div className=\"container mx-auto px-4 md:px-6 relative z-10\">\n          <div className=\"glass rounded-lg p-4 sm:p-6 md:p-8 lg:p-12 border border-neon-orange/30 max-w-4xl mx-auto\">\n            <div className=\"text-center mb-4 sm:mb-6 md:mb-8\">\n              <h2 className=\"text-xl sm:text-2xl md:text-3xl lg:text-4xl font-display font-bold text-white mb-3 sm:mb-4 wave-heading-bg\">\n                <span className=\"text-neon-orange\">JOIN</span> OUR SERVER\n              </h2>\n              <p className=\"text-xs sm:text-sm md:text-base text-gray-400 px-2\">\n                Ready to start your journey in the TGRS community? Follow these steps to join our server.\n              </p>\n            </div>\n\n            <div className=\"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-3 sm:gap-4 md:gap-6 mb-4 sm:mb-6 md:mb-8\">\n              <div className=\"text-center p-2 sm:p-3 md:p-4\">\n                <div className=\"w-10 h-10 sm:w-12 sm:h-12 md:w-16 md:h-16 rounded-full bg-neon-orange/20 flex items-center justify-center mx-auto mb-2 sm:mb-3\">\n                  <span className=\"text-neon-orange font-display font-bold text-lg sm:text-xl md:text-2xl\">1</span>\n                </div>\n                <h3 className=\"text-white font-semibold mb-1 sm:mb-2 text-xs sm:text-sm md:text-base\">Join Discord</h3>\n                <p className=\"text-gray-400 text-xs sm:text-sm\">Connect with our community on Discord for server updates and support.</p>\n              </div>\n\n              <div className=\"text-center p-2 sm:p-3 md:p-4\">\n                <div className=\"w-10 h-10 sm:w-12 sm:h-12 md:w-16 md:h-16 rounded-full bg-neon-orange/20 flex items-center justify-center mx-auto mb-2 sm:mb-3\">\n                  <span className=\"text-neon-orange font-display font-bold text-lg sm:text-xl md:text-2xl\">2</span>\n                </div>\n                <h3 className=\"text-white font-semibold mb-1 sm:mb-2 text-xs sm:text-sm md:text-base\">Read Rules</h3>\n                <p className=\"text-gray-400 text-xs sm:text-sm\">Familiarize yourself with our server rules to ensure a positive experience.</p>\n              </div>\n\n              <div className=\"text-center p-2 sm:p-3 md:p-4 sm:col-span-2 md:col-span-1\">\n                <div className=\"w-10 h-10 sm:w-12 sm:h-12 md:w-16 md:h-16 rounded-full bg-neon-orange/20 flex items-center justify-center mx-auto mb-2 sm:mb-3\">\n                  <span className=\"text-neon-orange font-display font-bold text-lg sm:text-xl md:text-2xl\">3</span>\n                </div>\n                <h3 className=\"text-white font-semibold mb-1 sm:mb-2 text-xs sm:text-sm md:text-base\">Connect & Play</h3>\n                <p className=\"text-gray-400 text-xs sm:text-sm\">Use FiveM to connect to our server and start your roleplay journey.</p>\n              </div>\n            </div>\n\n            <div className=\"flex flex-col sm:flex-row gap-2 sm:gap-3 md:gap-4 justify-center px-2 sm:px-4 md:px-0\">\n              <Button href=\"https://discord.gg/GAMravHDnB\" variant=\"primary\" size=\"md\" className=\"w-full sm:w-auto text-xs sm:text-sm md:text-base py-2 sm:py-3\">\n                Join Discord\n              </Button>\n              <Button href=\"/rules\" variant=\"outline\" size=\"md\" className=\"w-full sm:w-auto text-xs sm:text-sm md:text-base py-2 sm:py-3\">\n                View Rules\n              </Button>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* SEO Content Section */}\n      <section className=\"py-16 md:py-20 bg-black relative overflow-hidden\">\n        <div className=\"absolute inset-0 pattern-dots opacity-5\"></div>\n        <div className=\"container mx-auto px-4 md:px-6 relative z-10\">\n          <div className=\"max-w-4xl mx-auto text-center\">\n            <h2 className=\"text-3xl md:text-4xl font-display font-bold text-white mb-8\">\n              Why Choose <span className=\"text-neon-orange\">TGRS</span> for Telugu Gaming?\n            </h2>\n\n            <div className=\"grid md:grid-cols-2 gap-8 text-left\">\n              <div>\n                <h3 className=\"text-xl font-semibold text-neon-orange mb-4\">Best Telugu FiveM Experience</h3>\n                <p className=\"text-gray-300 mb-4\">\n                  TGRS is the premier Telugu FiveM roleplay server, offering an authentic Telugu gaming experience.\n                  Our server features custom scripts designed specifically for the Telugu community, ensuring\n                  immersive roleplay that resonates with Telugu culture and language.\n                </p>\n                <p className=\"text-gray-300\">\n                  Join thousands of Telugu gamers who have made TGRS their home for GTA 5 roleplay.\n                  Experience the best FiveM server with 24/7 uptime, professional staff, and regular updates.\n                </p>\n              </div>\n\n              <div>\n                <h3 className=\"text-xl font-semibold text-neon-orange mb-4\">Telugu Gaming Community</h3>\n                <p className=\"text-gray-300 mb-4\">\n                  Connect with fellow Telugu gamers from Hyderabad, Telangana, Andhra Pradesh, and across India.\n                  Our active Discord community provides a platform for Telugu gamers to interact, share experiences,\n                  and participate in community events.\n                </p>\n                <p className=\"text-gray-300\">\n                  Whether you're into competitive gaming, casual roleplay, or esports, TGRS offers the perfect\n                  environment for Telugu gaming enthusiasts to thrive and build lasting friendships.\n                </p>\n              </div>\n            </div>\n\n            <div className=\"mt-12 grid md:grid-cols-3 gap-6 text-center\">\n              <div className=\"bg-gray-900/50 p-6 rounded-lg border border-neon-orange/20\">\n                <h4 className=\"text-lg font-semibold text-white mb-2\">500+ Active Players</h4>\n                <p className=\"text-gray-400 text-sm\">Growing Telugu gaming community</p>\n              </div>\n              <div className=\"bg-gray-900/50 p-6 rounded-lg border border-neon-orange/20\">\n                <h4 className=\"text-lg font-semibold text-white mb-2\">24/7 Server Uptime</h4>\n                <p className=\"text-gray-400 text-sm\">Always online, never miss the action</p>\n              </div>\n              <div className=\"bg-gray-900/50 p-6 rounded-lg border border-neon-orange/20\">\n                <h4 className=\"text-lg font-semibold text-white mb-2\">Custom Telugu Scripts</h4>\n                <p className=\"text-gray-400 text-sm\">Unique features for Telugu players</p>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      <Footer />\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;AACA;;;;;;;;AAGO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;IACb,UAAU;QACR,gCAAgC;QAChC;QAAuB;QAAgB;QAAiB;QACxD;QAA2B;QAAyB;QAAiB;QACrE;QAAwB;QAAmB;QAAuB;QAElE,sBAAsB;QACtB;QAAgB;QAAgB;QAAc;QAAc;QAC5D;QAAuB;QAAgB;QAAgB;QAEvD,uBAAuB;QACvB;QAAe;QAAe;QAAsB;QACpD;QAAsB;QAAsB;QAE5C,kBAAkB;QAClB;QAAiB;QAAiB;QAAwB;QAC1D;QAA6B;QAA2B;QAExD,oBAAoB;QACpB;QAAuB;QAAsB;QAAoB;QACjE;QAAoB;QAAyB;QAAmB;QAChE;QAAwB;QAAqB;QAAwB;QAErE,kBAAkB;QAClB;QAAwB;QAA6B;QACrD;QAAyB;QAAyB;QAClD;QAAuB;QAAwB;QAE/C,oBAAoB;QACpB;QAAyB;QAA2B;QACpD;QAA0B;QAAyB;QAEnD,sBAAsB;QACtB;QAAkB;QAAkB;QAAiB;QACrD;QAAsB;QAAqB;KAC5C;IACD,WAAW;QACT,OAAO;QACP,aAAa;QACb,QAAQ;YAAC;SAA4B;IACvC;IACA,YAAY;QACV,WAAW;IACb;AACF;AAEe,SAAS;IACtB,qBACE;;0BACE,8OAAC,4HAAA,CAAA,UAAM;;;;;0BAGP,8OAAC,0HAAA,CAAA,UAAI;gBACH,OAAM;gBACN,UAAS;gBACT,mBAAkB;gBAClB,mBAAkB;gBAClB,qBAAoB;gBACpB,qBAAoB;;;;;;0BAItB,8OAAC;gBAAI,WAAU;;;;;;0BAGf,8OAAC;gBAAQ,IAAG;gBAAQ,WAAU;;kCAE5B,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCACC,KAAI;gCACJ,WAAU;gCACV,OAAO;oCACL,UAAU;oCACV,KAAK;oCACL,MAAM;oCACN,OAAO;oCACP,QAAQ;oCACR,WAAW;oCACX,UAAU;oCACV,WAAW;oCACX,eAAe;oCACf,QAAQ;gCACV;gCACA,OAAM;gCACN,iBAAiB;;;;;;0CAInB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCACC,KAAI;oCACJ,WAAU;oCACV,OAAO;wCACL,KAAK;wCACL,MAAM;wCACN,OAAO;wCACP,QAAQ;wCACR,WAAW;wCACX,UAAU;wCACV,eAAe;wCACf,QAAQ;wCACR,WAAW;oCACb;oCACA,OAAM;oCACN,iBAAiB;;;;;;;;;;;0CAKrB,8OAAC;gCAAI,WAAU;;;;;;;;;;;;kCAGjB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;;4CAA6G;0DAC7G,8OAAC;gDAAK,WAAU;0DAAmB;;;;;;;;;;;;kDAEjD,8OAAC;wCAAE,WAAU;kDAAkF;;;;;;;;;;;;0CAMjG,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DAEf,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;sFAEb,8OAAC;4EAAI,WAAU;4EAAwD,OAAO;gFAAC,KAAK;gFAAQ,MAAM;gFAAO,gBAAgB;4EAAI;;;;;;sFAC7H,8OAAC;4EAAI,WAAU;4EAA6D,OAAO;gFAAC,KAAK;gFAAQ,OAAO;gFAAO,gBAAgB;4EAAM;;;;;;sFAGrI,8OAAC;4EAAI,WAAU;4EAAwD,OAAO;gFAAC,OAAO;gFAAQ,KAAK;gFAAO,gBAAgB;4EAAM;;;;;;sFAChI,8OAAC;4EAAI,WAAU;4EAA6D,OAAO;gFAAC,OAAO;gFAAQ,QAAQ;gFAAO,gBAAgB;4EAAM;;;;;;sFAGxI,8OAAC;4EAAI,WAAU;4EAAwD,OAAO;gFAAC,QAAQ;gFAAQ,OAAO;gFAAO,gBAAgB;4EAAM;;;;;;sFACnI,8OAAC;4EAAI,WAAU;4EAA6D,OAAO;gFAAC,QAAQ;gFAAQ,MAAM;gFAAO,gBAAgB;4EAAM;;;;;;sFAGvI,8OAAC;4EAAI,WAAU;4EAAwD,OAAO;gFAAC,MAAM;gFAAQ,QAAQ;gFAAO,gBAAgB;4EAAM;;;;;;sFAClI,8OAAC;4EAAI,WAAU;4EAA6D,OAAO;gFAAC,MAAM;gFAAQ,KAAK;gFAAO,gBAAgB;4EAAM;;;;;;;;;;;;8EAEtI,8OAAC;oEAAI,WAAU;oEAA8B,MAAK;oEAAe,SAAQ;8EACvE,cAAA,8OAAC;wEAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;kEAId,8OAAC;wDAAG,WAAU;kEAAgI;;;;;;kEAG9I,8OAAC;wDAAE,WAAU;kEAAwH;;;;;;;;;;;;0DAKvI,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;;;;;;;;;;;;kDAIjB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DAEf,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;sFAEb,8OAAC;4EAAI,WAAU;4EAAyD,OAAO;gFAAC,KAAK;gFAAQ,MAAM;gFAAO,gBAAgB;4EAAI;;;;;;sFAC9H,8OAAC;4EAAI,WAAU;4EAA8D,OAAO;gFAAC,KAAK;gFAAQ,OAAO;gFAAO,gBAAgB;4EAAM;;;;;;sFAGtI,8OAAC;4EAAI,WAAU;4EAAyD,OAAO;gFAAC,OAAO;gFAAQ,KAAK;gFAAO,gBAAgB;4EAAM;;;;;;sFACjI,8OAAC;4EAAI,WAAU;4EAA8D,OAAO;gFAAC,OAAO;gFAAQ,QAAQ;gFAAO,gBAAgB;4EAAM;;;;;;sFAGzI,8OAAC;4EAAI,WAAU;4EAAyD,OAAO;gFAAC,QAAQ;gFAAQ,OAAO;gFAAO,gBAAgB;4EAAM;;;;;;sFACpI,8OAAC;4EAAI,WAAU;4EAA8D,OAAO;gFAAC,QAAQ;gFAAQ,MAAM;gFAAO,gBAAgB;4EAAM;;;;;;sFAGxI,8OAAC;4EAAI,WAAU;4EAAyD,OAAO;gFAAC,MAAM;gFAAQ,QAAQ;gFAAO,gBAAgB;4EAAM;;;;;;sFACnI,8OAAC;4EAAI,WAAU;4EAA8D,OAAO;gFAAC,MAAM;gFAAQ,KAAK;gFAAO,gBAAgB;4EAAM;;;;;;;;;;;;8EAEvI,8OAAC;oEAAI,WAAU;oEAA8B,MAAK;oEAAe,SAAQ;8EACvE,cAAA,8OAAC;wEAAK,UAAS;wEAAU,GAAE;wEAA6H,UAAS;;;;;;;;;;;;;;;;;;;;;;kEAIvK,8OAAC;wDAAG,WAAU;kEAAiI;;;;;;kEAG/I,8OAAC;wDAAE,WAAU;kEAAwH;;;;;;;;;;;;0DAKvI,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;;;;;;;;;;;;kDAIjB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DAEf,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;sFAEb,8OAAC;4EAAI,WAAU;4EAA0D,OAAO;gFAAC,KAAK;gFAAQ,MAAM;gFAAO,gBAAgB;4EAAI;;;;;;sFAC/H,8OAAC;4EAAI,WAAU;4EAA+D,OAAO;gFAAC,KAAK;gFAAQ,OAAO;gFAAO,gBAAgB;4EAAM;;;;;;sFAGvI,8OAAC;4EAAI,WAAU;4EAA0D,OAAO;gFAAC,OAAO;gFAAQ,KAAK;gFAAO,gBAAgB;4EAAM;;;;;;sFAClI,8OAAC;4EAAI,WAAU;4EAA+D,OAAO;gFAAC,OAAO;gFAAQ,QAAQ;gFAAO,gBAAgB;4EAAM;;;;;;sFAG1I,8OAAC;4EAAI,WAAU;4EAA0D,OAAO;gFAAC,QAAQ;gFAAQ,OAAO;gFAAO,gBAAgB;4EAAM;;;;;;sFACrI,8OAAC;4EAAI,WAAU;4EAA+D,OAAO;gFAAC,QAAQ;gFAAQ,MAAM;gFAAO,gBAAgB;4EAAM;;;;;;sFAGzI,8OAAC;4EAAI,WAAU;4EAA0D,OAAO;gFAAC,MAAM;gFAAQ,QAAQ;gFAAO,gBAAgB;4EAAM;;;;;;sFACpI,8OAAC;4EAAI,WAAU;4EAA+D,OAAO;gFAAC,MAAM;gFAAQ,KAAK;gFAAO,gBAAgB;4EAAM;;;;;;;;;;;;8EAExI,8OAAC;oEAAI,WAAU;oEAA8B,MAAK;oEAAe,SAAQ;8EACvE,cAAA,8OAAC;wEAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;kEAId,8OAAC;wDAAG,WAAU;kEAAkI;;;;;;kEAGhJ,8OAAC;wDAAE,WAAU;kEAAwH;;;;;;;;;;;;0DAKvI,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;;;;;;;;;;;;;;;;;;0CAKnB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAAgF;;;;;;0DAC/F,8OAAC;gDAAI,WAAU;0DAAgD;;;;;;;;;;;;kDAEjE,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAAgF;;;;;;0DAC/F,8OAAC;gDAAI,WAAU;0DAAgD;;;;;;;;;;;;kDAEjE,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAAgF;;;;;;0DAC/F,8OAAC;gDAAI,WAAU;0DAAgD;;;;;;;;;;;;kDAEjE,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAAgF;;;;;;0DAC/F,8OAAC;gDAAI,WAAU;0DAAgD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOvE,8OAAC;gBAAI,WAAU;;;;;;0BAGf,8OAAC;gBAAQ,IAAG;gBAAW,WAAU;0BAC/B,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;4CAAK,WAAU;sDAAmB;;;;;;wCAAa;;;;;;;8CAElD,8OAAC;oCAAE,WAAU;8CAAuE;;;;;;;;;;;;sCAKtF,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACf,8OAAC,iIAAA,CAAA,UAAW;wCACV,OAAM;wCACN,aAAY;wCACZ,eAAc;wCACd,SAAQ;wCACR,WAAU;wCACV,oBACE,8OAAC;4CAAI,WAAU;4CAAY,MAAK;4CAAO,QAAO;4CAAe,SAAQ;4CAAY,OAAM;sDACrF,cAAA,8OAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;;;;;;kDAK3E,8OAAC,iIAAA,CAAA,UAAW;wCACV,OAAM;wCACN,aAAY;wCACZ,eAAc;wCACd,SAAQ;wCACR,WAAU;wCACV,oBACE,8OAAC;4CAAI,WAAU;4CAAY,MAAK;4CAAO,QAAO;4CAAe,SAAQ;4CAAY,OAAM;sDACrF,cAAA,8OAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;;;;;;kDAK3E,8OAAC,iIAAA,CAAA,UAAW;wCACV,OAAM;wCACN,aAAY;wCACZ,eAAc;wCACd,SAAQ;wCACR,WAAU;wCACV,oBACE,8OAAC;4CAAI,WAAU;4CAAY,MAAK;4CAAO,QAAO;4CAAe,SAAQ;4CAAY,OAAM;sDACrF,cAAA,8OAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;;;;;;kDAK3E,8OAAC,iIAAA,CAAA,UAAW;wCACV,OAAM;wCACN,aAAY;wCACZ,eAAc;wCACd,SAAQ;wCACR,WAAU;wCACV,oBACE,8OAAC;4CAAI,WAAU;4CAAY,MAAK;4CAAO,QAAO;4CAAe,SAAQ;4CAAY,OAAM;sDACrF,cAAA,8OAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;;;;;;kDAK3E,8OAAC,iIAAA,CAAA,UAAW;wCACV,OAAM;wCACN,aAAY;wCACZ,eAAc;wCACd,SAAQ;wCACR,WAAU;wCACV,oBACE,8OAAC;4CAAI,WAAU;4CAAY,MAAK;4CAAO,QAAO;4CAAe,SAAQ;4CAAY,OAAM;sDACrF,cAAA,8OAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;;;;;;kDAK3E,8OAAC,iIAAA,CAAA,UAAW;wCACV,OAAM;wCACN,aAAY;wCACZ,eAAc;wCACd,SAAQ;wCACR,WAAU;wCACV,oBACE,8OAAC;4CAAI,WAAU;4CAAY,MAAK;4CAAO,QAAO;4CAAe,SAAQ;4CAAY,OAAM;sDACrF,cAAA,8OAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUjF,8OAAC;gBAAI,WAAU;;;;;;0BAGf,8OAAC;gBAAQ,IAAG;gBAAO,WAAU;;kCAC3B,8OAAC,oIAAA,CAAA,UAAc;;;;;kCACf,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAK,WAAU;8DAAmB;;;;;;gDAAW;;;;;;;sDAEhD,8OAAC;4CAAE,WAAU;sDAAqD;;;;;;;;;;;;8CAKpE,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAK,WAAU;kEAAyE;;;;;;;;;;;8DAE3F,8OAAC;oDAAG,WAAU;8DAAwE;;;;;;8DACtF,8OAAC;oDAAE,WAAU;8DAAmC;;;;;;;;;;;;sDAGlD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAK,WAAU;kEAAyE;;;;;;;;;;;8DAE3F,8OAAC;oDAAG,WAAU;8DAAwE;;;;;;8DACtF,8OAAC;oDAAE,WAAU;8DAAmC;;;;;;;;;;;;sDAGlD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAK,WAAU;kEAAyE;;;;;;;;;;;8DAE3F,8OAAC;oDAAG,WAAU;8DAAwE;;;;;;8DACtF,8OAAC;oDAAE,WAAU;8DAAmC;;;;;;;;;;;;;;;;;;8CAIpD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,4HAAA,CAAA,UAAM;4CAAC,MAAK;4CAAgC,SAAQ;4CAAU,MAAK;4CAAK,WAAU;sDAAgE;;;;;;sDAGnJ,8OAAC,4HAAA,CAAA,UAAM;4CAAC,MAAK;4CAAS,SAAQ;4CAAU,MAAK;4CAAK,WAAU;sDAAgE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASpI,8OAAC;gBAAQ,WAAU;;kCACjB,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;;wCAA8D;sDAC/D,8OAAC;4CAAK,WAAU;sDAAmB;;;;;;wCAAW;;;;;;;8CAG3D,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAA8C;;;;;;8DAC5D,8OAAC;oDAAE,WAAU;8DAAqB;;;;;;8DAKlC,8OAAC;oDAAE,WAAU;8DAAgB;;;;;;;;;;;;sDAM/B,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAA8C;;;;;;8DAC5D,8OAAC;oDAAE,WAAU;8DAAqB;;;;;;8DAKlC,8OAAC;oDAAE,WAAU;8DAAgB;;;;;;;;;;;;;;;;;;8CAOjC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAwC;;;;;;8DACtD,8OAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;sDAEvC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAwC;;;;;;8DACtD,8OAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;sDAEvC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAwC;;;;;;8DACtD,8OAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO/C,8OAAC,4HAAA,CAAA,UAAM;;;;;;;AAGb"}}, {"offset": {"line": 2772, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2783, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": ""}}, {"offset": {"line": 2783, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}