exports.id=685,exports.ids=[685],exports.modules={5635:(e,t,i)=>{"use strict";i.d(t,{default:()=>o.a});var r=i(8516),o=i.n(r)},9607:(e,t,i)=>{let{createProxy:r}=i(3439);e.exports=r("C:\\Users\\<USER>\\Desktop\\TGRS Website\\tgrs-website\\node_modules\\next\\dist\\client\\app-dir\\link.js")},1066:(e,t,i)=>{let{createProxy:r}=i(3439);e.exports=r("C:\\Users\\<USER>\\Desktop\\TGRS Website\\tgrs-website\\node_modules\\next\\dist\\client\\image-component.js")},2326:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImgProps",{enumerable:!0,get:function(){return a}}),i(5843);let r=i(6749),o=i(2833);function s(e){return void 0!==e.default}function n(e){return void 0===e?e:"number"==typeof e?Number.isFinite(e)?e:NaN:"string"==typeof e&&/^[0-9]+$/.test(e)?parseInt(e,10):NaN}function a(e,t){var i,a;let l,d,u,{src:c,sizes:f,unoptimized:g=!1,priority:m=!1,loading:h,className:p,quality:b,width:v,height:w,fill:y=!1,style:x,overrideSrc:S,onLoad:_,onLoadingComplete:j,placeholder:C="empty",blurDataURL:P,fetchPriority:z,decoding:O="async",layout:E,objectFit:M,objectPosition:I,lazyBoundary:R,lazyRoot:k,...D}=e,{imgConf:G,showAltText:A,blurComplete:W,defaultLoader:F}=t,N=G||o.imageConfigDefault;if("allSizes"in N)l=N;else{let e=[...N.deviceSizes,...N.imageSizes].sort((e,t)=>e-t),t=N.deviceSizes.sort((e,t)=>e-t),r=null==(i=N.qualities)?void 0:i.sort((e,t)=>e-t);l={...N,allSizes:e,deviceSizes:t,qualities:r}}if(void 0===F)throw Error("images.loaderFile detected but the file is missing default export.\nRead more: https://nextjs.org/docs/messages/invalid-images-config");let B=D.loader||F;delete D.loader,delete D.srcSet;let L="__next_img_default"in B;if(L){if("custom"===l.loader)throw Error('Image with src "'+c+'" is missing "loader" prop.\nRead more: https://nextjs.org/docs/messages/next-image-missing-loader')}else{let e=B;B=t=>{let{config:i,...r}=t;return e(r)}}if(E){"fill"===E&&(y=!0);let e={intrinsic:{maxWidth:"100%",height:"auto"},responsive:{width:"100%",height:"auto"}}[E];e&&(x={...x,...e});let t={responsive:"100vw",fill:"100vw"}[E];t&&!f&&(f=t)}let q="",T=n(v),U=n(w);if((a=c)&&"object"==typeof a&&(s(a)||void 0!==a.src)){let e=s(c)?c.default:c;if(!e.src)throw Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include src. Received "+JSON.stringify(e));if(!e.height||!e.width)throw Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include height and width. Received "+JSON.stringify(e));if(d=e.blurWidth,u=e.blurHeight,P=P||e.blurDataURL,q=e.src,!y){if(T||U){if(T&&!U){let t=T/e.width;U=Math.round(e.height*t)}else if(!T&&U){let t=U/e.height;T=Math.round(e.width*t)}}else T=e.width,U=e.height}}let V=!m&&("lazy"===h||void 0===h);(!(c="string"==typeof c?c:q)||c.startsWith("data:")||c.startsWith("blob:"))&&(g=!0,V=!1),l.unoptimized&&(g=!0),L&&!l.dangerouslyAllowSVG&&c.split("?",1)[0].endsWith(".svg")&&(g=!0);let J=n(b),Y=Object.assign(y?{position:"absolute",height:"100%",width:"100%",left:0,top:0,right:0,bottom:0,objectFit:M,objectPosition:I}:{},A?{}:{color:"transparent"},x),H=W||"empty"===C?null:"blur"===C?'url("data:image/svg+xml;charset=utf-8,'+(0,r.getImageBlurSvg)({widthInt:T,heightInt:U,blurWidth:d,blurHeight:u,blurDataURL:P||"",objectFit:Y.objectFit})+'")':'url("'+C+'")',$=H?{backgroundSize:Y.objectFit||"cover",backgroundPosition:Y.objectPosition||"50% 50%",backgroundRepeat:"no-repeat",backgroundImage:H}:{},K=function(e){let{config:t,src:i,unoptimized:r,width:o,quality:s,sizes:n,loader:a}=e;if(r)return{src:i,srcSet:void 0,sizes:void 0};let{widths:l,kind:d}=function(e,t,i){let{deviceSizes:r,allSizes:o}=e;if(i){let e=/(^|\s)(1?\d?\d)vw/g,t=[];for(let r;r=e.exec(i);r)t.push(parseInt(r[2]));if(t.length){let e=.01*Math.min(...t);return{widths:o.filter(t=>t>=r[0]*e),kind:"w"}}return{widths:o,kind:"w"}}return"number"!=typeof t?{widths:r,kind:"w"}:{widths:[...new Set([t,2*t].map(e=>o.find(t=>t>=e)||o[o.length-1]))],kind:"x"}}(t,o,n),u=l.length-1;return{sizes:n||"w"!==d?n:"100vw",srcSet:l.map((e,r)=>a({config:t,src:i,quality:s,width:e})+" "+("w"===d?e:r+1)+d).join(", "),src:a({config:t,src:i,quality:s,width:l[u]})}}({config:l,src:c,unoptimized:g,width:T,quality:J,sizes:f,loader:B});return{props:{...D,loading:V?"lazy":h,fetchPriority:z,width:T,height:U,decoding:O,className:p,style:{...Y,...$},sizes:K.sizes,srcSet:K.srcSet,src:S||K.src},meta:{unoptimized:g,priority:m,placeholder:C,fill:y}}}},6749:(e,t)=>{"use strict";function i(e){let{widthInt:t,heightInt:i,blurWidth:r,blurHeight:o,blurDataURL:s,objectFit:n}=e,a=r?40*r:t,l=o?40*o:i,d=a&&l?"viewBox='0 0 "+a+" "+l+"'":"";return"%3Csvg xmlns='http://www.w3.org/2000/svg' "+d+"%3E%3Cfilter id='b' color-interpolation-filters='sRGB'%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3CfeColorMatrix values='1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 100 -1' result='s'/%3E%3CfeFlood x='0' y='0' width='100%25' height='100%25'/%3E%3CfeComposite operator='out' in='s'/%3E%3CfeComposite in2='SourceGraphic'/%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3C/filter%3E%3Cimage width='100%25' height='100%25' x='0' y='0' preserveAspectRatio='"+(d?"none":"contain"===n?"xMidYMid":"cover"===n?"xMidYMid slice":"none")+"' style='filter: url(%23b);' href='"+s+"'/%3E%3C/svg%3E"}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImageBlurSvg",{enumerable:!0,get:function(){return i}})},2833:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var i in t)Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}(t,{VALID_LOADERS:function(){return i},imageConfigDefault:function(){return r}});let i=["default","imgix","cloudinary","akamai","custom"],r={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"attachment",localPatterns:void 0,remotePatterns:[],qualities:void 0,unoptimized:!1}},8516:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var i in t)Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}(t,{default:function(){return l},getImageProps:function(){return a}});let r=i(3264),o=i(2326),s=i(1066),n=r._(i(6352));function a(e){let{props:t}=(0,o.getImgProps)(e,{defaultLoader:n.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image/",loader:"default",dangerouslyAllowSVG:!0,unoptimized:!0}});for(let[e,i]of Object.entries(t))void 0===i&&delete t[e];return{props:t}}let l=s.Image},6352:(e,t)=>{"use strict";function i(e){var t;let{config:i,src:r,width:o,quality:s}=e,n=s||(null==(t=i.qualities)?void 0:t.reduce((e,t)=>Math.abs(t-75)<Math.abs(e-75)?t:e))||75;return i.path+"?url="+encodeURIComponent(r)+"&w="+o+"&q="+n+(r.startsWith("/_next/static/media/"),"")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return r}}),i.__next_img_default=!0;let r=i},5843:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"warnOnce",{enumerable:!0,get:function(){return i}});let i=e=>{}}};