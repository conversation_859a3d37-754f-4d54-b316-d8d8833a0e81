/** @type {import('next').NextConfig} */
const nextConfig = {
  // Enable static export for hosting
  output: 'export',
  trailingSlash: true,
  basePath: '/tgrs-website',
  assetPrefix: '/tgrs-website',

  // Enable experimental features for better SEO
  experimental: {
    // optimizeCss: true, // Disabled for static export
  },

  // Compress images for better performance (unoptimized for static export)
  images: {
    unoptimized: true,
    formats: ['image/webp', 'image/avif'],
    minimumCacheTTL: 60,
    dangerouslyAllowSVG: true,
    contentSecurityPolicy: "default-src 'self'; script-src 'none'; sandbox;",
  },

  // Enable compression
  compress: true,

  // Disable ESLint during build for deployment
  eslint: {
    ignoreDuringBuilds: true,
  },
};

module.exports = nextConfig;
