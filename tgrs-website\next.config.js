/** @type {import('next').NextConfig} */
const nextConfig = {
  // Enable experimental features for better SEO
  experimental: {
    optimizeCss: true,
  },
  
  // Compress images for better performance
  images: {
    formats: ['image/webp', 'image/avif'],
    minimumCacheTTL: 60,
    dangerouslyAllowSVG: true,
    contentSecurityPolicy: "default-src 'self'; script-src 'none'; sandbox;",
  },
  
  // Enable compression
  compress: true,
  
  // Optimize for production
  swcMinify: true,
  
  // Headers for SEO and performance
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'Referrer-Policy',
            value: 'origin-when-cross-origin',
          },
          {
            key: 'Permissions-Policy',
            value: 'camera=(), microphone=(), geolocation=()',
          },
        ],
      },
      {
        source: '/assets/(.*)',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=31536000, immutable',
          },
        ],
      },
    ];
  },
  
  // Redirects for SEO
  async redirects() {
    return [
      {
        source: '/discord',
        destination: 'https://discord.gg/GAMravHDnB',
        permanent: true,
      },
      {
        source: '/youtube',
        destination: 'https://www.youtube.com/@GTA5RPTGRSCITY',
        permanent: true,
      },
      {
        source: '/join',
        destination: 'cfx.re/join/o57lj7',
        permanent: true,
      },
    ];
  },
  
  // Rewrites for better URLs
  async rewrites() {
    return [
      {
        source: '/telugu-gaming',
        destination: '/telugu-fivem-server',
      },
      {
        source: '/fivem-server',
        destination: '/fivem-roleplay-server',
      },
    ];
  },
};

module.exports = nextConfig;
