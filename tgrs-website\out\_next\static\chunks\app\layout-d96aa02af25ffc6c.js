(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[177],{8719:(e,t,n)=>{Promise.resolve().then(n.t.bind(n,3135,23)),Promise.resolve().then(n.t.bind(n,859,23)),Promise.resolve().then(n.t.bind(n,347,23)),Promise.resolve().then(n.bind(n,5389))},8571:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{cancelIdleCallback:function(){return r},requestIdleCallback:function(){return n}});let n="undefined"!=typeof self&&self.requestIdleCallback&&self.requestIdleCallback.bind(window)||function(e){let t=Date.now();return self.setTimeout(function(){e({didTimeout:!1,timeRemaining:function(){return Math.max(0,50-(Date.now()-t))}})},1)},r="undefined"!=typeof self&&self.cancelIdleCallback&&self.cancelIdleCallback.bind(window)||function(e){return clearTimeout(e)};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},3704:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{default:function(){return y},handleClientScriptLoad:function(){return m},initScriptLoader:function(){return h}});let r=n(306),a=n(9955),i=n(5155),l=r._(n(7650)),o=a._(n(2115)),c=n(1147),s=n(2815),u=n(8571),d=new Map,f=new Set,g=e=>{if(l.default.preinit){e.forEach(e=>{l.default.preinit(e,{as:"style"})});return}if("undefined"!=typeof window){let t=document.head;e.forEach(e=>{let n=document.createElement("link");n.type="text/css",n.rel="stylesheet",n.href=e,t.appendChild(n)})}},p=e=>{let{src:t,id:n,onLoad:r=()=>{},onReady:a=null,dangerouslySetInnerHTML:i,children:l="",strategy:o="afterInteractive",onError:c,stylesheets:u}=e,p=n||t;if(p&&f.has(p))return;if(d.has(t)){f.add(p),d.get(t).then(r,c);return}let m=()=>{a&&a(),f.add(p)},h=document.createElement("script"),_=new Promise((e,t)=>{h.addEventListener("load",function(t){e(),r&&r.call(this,t),m()}),h.addEventListener("error",function(e){t(e)})}).catch(function(e){c&&c(e)});i?(h.innerHTML=i.__html||"",m()):l?(h.textContent="string"==typeof l?l:Array.isArray(l)?l.join(""):"",m()):t&&(h.src=t,d.set(t,_)),(0,s.setAttributesFromProps)(h,e),"worker"===o&&h.setAttribute("type","text/partytown"),h.setAttribute("data-nscript",o),u&&g(u),document.body.appendChild(h)};function m(e){let{strategy:t="afterInteractive"}=e;"lazyOnload"===t?window.addEventListener("load",()=>{(0,u.requestIdleCallback)(()=>p(e))}):p(e)}function h(e){e.forEach(m),[...document.querySelectorAll('[data-nscript="beforeInteractive"]'),...document.querySelectorAll('[data-nscript="beforePageRender"]')].forEach(e=>{let t=e.id||e.getAttribute("src");f.add(t)})}function _(e){let{id:t,src:n="",onLoad:r=()=>{},onReady:a=null,strategy:s="afterInteractive",onError:d,stylesheets:g,...m}=e,{updateScripts:h,scripts:_,getIsSsr:y,appDir:v,nonce:b}=(0,o.useContext)(c.HeadManagerContext),w=(0,o.useRef)(!1);(0,o.useEffect)(()=>{let e=t||n;w.current||(a&&e&&f.has(e)&&a(),w.current=!0)},[a,t,n]);let j=(0,o.useRef)(!1);if((0,o.useEffect)(()=>{!j.current&&("afterInteractive"===s?p(e):"lazyOnload"===s&&("complete"===document.readyState?(0,u.requestIdleCallback)(()=>p(e)):window.addEventListener("load",()=>{(0,u.requestIdleCallback)(()=>p(e))})),j.current=!0)},[e,s]),("beforeInteractive"===s||"worker"===s)&&(h?(_[s]=(_[s]||[]).concat([{id:t,src:n,onLoad:r,onReady:a,onError:d,...m}]),h(_)):y&&y()?f.add(t||n):y&&!y()&&p(e)),v){if(g&&g.forEach(e=>{l.default.preinit(e,{as:"style"})}),"beforeInteractive"===s)return n?(l.default.preload(n,m.integrity?{as:"script",integrity:m.integrity,nonce:b,crossOrigin:m.crossOrigin}:{as:"script",nonce:b,crossOrigin:m.crossOrigin}),(0,i.jsx)("script",{nonce:b,dangerouslySetInnerHTML:{__html:"(self.__next_s=self.__next_s||[]).push("+JSON.stringify([n,{...m,id:t}])+")"}})):(m.dangerouslySetInnerHTML&&(m.children=m.dangerouslySetInnerHTML.__html,delete m.dangerouslySetInnerHTML),(0,i.jsx)("script",{nonce:b,dangerouslySetInnerHTML:{__html:"(self.__next_s=self.__next_s||[]).push("+JSON.stringify([0,{...m,id:t}])+")"}}));"afterInteractive"===s&&n&&l.default.preload(n,m.integrity?{as:"script",integrity:m.integrity,nonce:b,crossOrigin:m.crossOrigin}:{as:"script",nonce:b,crossOrigin:m.crossOrigin})}return null}Object.defineProperty(_,"__nextScript",{value:!0});let y=_;("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},2815:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"setAttributesFromProps",{enumerable:!0,get:function(){return i}});let n={acceptCharset:"accept-charset",className:"class",htmlFor:"for",httpEquiv:"http-equiv",noModule:"noModule"},r=["onLoad","onReady","dangerouslySetInnerHTML","children","onError","strategy","stylesheets"];function a(e){return["async","defer","noModule"].includes(e)}function i(e,t){for(let[i,l]of Object.entries(t)){if(!t.hasOwnProperty(i)||r.includes(i)||void 0===l)continue;let o=n[i]||i.toLowerCase();"SCRIPT"===e.tagName&&a(o)?e[o]=!!l:e.setAttribute(o,String(l)),(!1===l||"SCRIPT"===e.tagName&&a(o)&&(!l||"false"===l))&&(e.setAttribute(o,""),e.removeAttribute(o))}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5389:(e,t,n)=>{"use strict";n.d(t,{default:()=>l});var r=n(5155),a=n(3704),i=n.n(a);function l(){return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(i(),{src:"https://www.googletagmanager.com/gtag/js?id=GA_MEASUREMENT_ID",strategy:"afterInteractive"}),(0,r.jsx)(i(),{id:"google-analytics",strategy:"afterInteractive",children:"\n          window.dataLayer = window.dataLayer || [];\n          function gtag(){dataLayer.push(arguments);}\n          gtag('js', new Date());\n          gtag('config', 'GA_MEASUREMENT_ID', {\n            page_title: document.title,\n            page_location: window.location.href,\n            custom_map: {\n              'custom_parameter_1': 'server_type',\n              'custom_parameter_2': 'user_language'\n            }\n          });\n          \n          // Track Telugu gaming events\n          gtag('event', 'page_view', {\n            'custom_parameter_1': 'telugu_fivem',\n            'custom_parameter_2': 'telugu'\n          });\n        "}),(0,r.jsx)(i(),{id:"microsoft-clarity",strategy:"afterInteractive",children:'\n          (function(c,l,a,r,i,t,y){\n            c[a]=c[a]||function(){(c[a].q=c[a].q||[]).push(arguments)};\n            t=l.createElement(r);t.async=1;t.src="https://www.clarity.ms/tag/"+i;\n            y=l.getElementsByTagName(r)[0];y.parentNode.insertBefore(t,y);\n          })(window, document, "clarity", "script", "CLARITY_PROJECT_ID");\n        '}),(0,r.jsx)(i(),{id:"facebook-pixel",strategy:"afterInteractive",children:"\n          !function(f,b,e,v,n,t,s)\n          {if(f.fbq)return;n=f.fbq=function(){n.callMethod?\n          n.callMethod.apply(n,arguments):n.queue.push(arguments)};\n          if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';\n          n.queue=[];t=b.createElement(e);t.async=!0;\n          t.src=v;s=b.getElementsByTagName(e)[0];\n          s.parentNode.insertBefore(t,s)}(window, document,'script',\n          'https://connect.facebook.net/en_US/fbevents.js');\n          fbq('init', 'FACEBOOK_PIXEL_ID');\n          fbq('track', 'PageView');\n          \n          // Track Telugu gaming interest\n          fbq('trackCustom', 'TeluguGamingInterest', {\n            server_type: 'fivem',\n            language: 'telugu',\n            community: 'tgrs'\n          });\n        "}),(0,r.jsx)(i(),{id:"hotjar",strategy:"afterInteractive",children:"\n          (function(h,o,t,j,a,r){\n            h.hj=h.hj||function(){(h.hj.q=h.hj.q||[]).push(arguments)};\n            h._hjSettings={hjid:HOTJAR_ID,hjsv:6};\n            a=o.getElementsByTagName('head')[0];\n            r=o.createElement('script');r.async=1;\n            r.src=t+h._hjSettings.hjid+j+h._hjSettings.hjsv;\n            a.appendChild(r);\n          })(window,document,'https://static.hotjar.com/c/hotjar-','.js?sv=');\n        "}),(0,r.jsx)(i(),{id:"telugu-gaming-tracking",strategy:"afterInteractive",children:"\n          // Track Telugu gaming specific events\n          function trackTeluguGamingEvent(eventName, properties = {}) {\n            // Google Analytics\n            if (typeof gtag !== 'undefined') {\n              gtag('event', eventName, {\n                event_category: 'Telugu Gaming',\n                event_label: 'TGRS',\n                ...properties\n              });\n            }\n            \n            // Facebook Pixel\n            if (typeof fbq !== 'undefined') {\n              fbq('trackCustom', eventName, {\n                server: 'tgrs',\n                language: 'telugu',\n                ...properties\n              });\n            }\n            \n            // Console log for debugging\n            console.log('Telugu Gaming Event:', eventName, properties);\n          }\n          \n          // Track page interactions\n          document.addEventListener('DOMContentLoaded', function() {\n            // Track Discord link clicks\n            document.querySelectorAll('a[href*=\"discord.gg\"]').forEach(link => {\n              link.addEventListener('click', () => {\n                trackTeluguGamingEvent('discord_click', {\n                  source: 'website',\n                  page: window.location.pathname\n                });\n              });\n            });\n            \n            // Track VIP interest\n            document.querySelectorAll('a[href*=\"/vip\"]').forEach(link => {\n              link.addEventListener('click', () => {\n                trackTeluguGamingEvent('vip_interest', {\n                  source: 'navigation',\n                  page: window.location.pathname\n                });\n              });\n            });\n            \n            // Track server join attempts\n            document.querySelectorAll('a[href*=\"cfx.re\"]').forEach(link => {\n              link.addEventListener('click', () => {\n                trackTeluguGamingEvent('server_join_attempt', {\n                  source: 'website',\n                  page: window.location.pathname\n                });\n              });\n            });\n          });\n          \n          // Track scroll depth for engagement\n          let maxScroll = 0;\n          window.addEventListener('scroll', () => {\n            const scrollPercent = Math.round((window.scrollY / (document.body.scrollHeight - window.innerHeight)) * 100);\n            if (scrollPercent > maxScroll && scrollPercent % 25 === 0) {\n              maxScroll = scrollPercent;\n              trackTeluguGamingEvent('scroll_depth', {\n                depth: scrollPercent,\n                page: window.location.pathname\n              });\n            }\n          });\n        "})]})}},347:()=>{},859:e=>{e.exports={style:{fontFamily:"'Orbitron', 'Orbitron Fallback'",fontStyle:"normal"},className:"__className_23a2c6",variable:"__variable_23a2c6"}},3135:e=>{e.exports={style:{fontFamily:"'Rajdhani', 'Rajdhani Fallback'",fontStyle:"normal"},className:"__className_6184ad",variable:"__variable_6184ad"}}},e=>{var t=t=>e(e.s=t);e.O(0,[46,441,517,358],()=>t(8719)),_N_E=e.O()}]);