(()=>{var e={};e.id=782,e.ids=[782],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},9551:e=>{"use strict";e.exports=require("url")},3971:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>c,pages:()=>m,routeModule:()=>x,tree:()=>d});var a=s(260),r=s(8203),i=s(5155),n=s.n(i),l=s(7292),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);s.d(t,o);let d=["",{children:["vip",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,1561)),"C:\\Users\\<USER>\\Desktop\\TGRS Website\\tgrs-website\\src\\app\\vip\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,4375))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,1354)),"C:\\Users\\<USER>\\Desktop\\TGRS Website\\tgrs-website\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,9937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,9116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,1485,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,4375))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],m=["C:\\Users\\<USER>\\Desktop\\TGRS Website\\tgrs-website\\src\\app\\vip\\page.tsx"],c={require:s,loadChunk:()=>Promise.resolve()},x=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/vip/page",pathname:"/vip",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},5566:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,9607,23)),Promise.resolve().then(s.t.bind(s,1066,23)),Promise.resolve().then(s.bind(s,9211)),Promise.resolve().then(s.bind(s,1443)),Promise.resolve().then(s.bind(s,5966))},1910:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,8531,23)),Promise.resolve().then(s.t.bind(s,1902,23)),Promise.resolve().then(s.bind(s,3292)),Promise.resolve().then(s.bind(s,6519)),Promise.resolve().then(s.bind(s,5930))},3292:(e,t,s)=>{"use strict";s.d(t,{default:()=>i});var a=s(5512),r=s(8009);let i=()=>{let e=(0,r.useRef)(null),t=(0,r.useRef)(void 0),s=(0,r.useRef)([]),i=(0,r.useRef)({x:0,y:0});return(0,r.useEffect)(()=>{let a=e.current;if(!a)return;let r=a.getContext("2d");if(!r)return;let n=()=>{a.width=a.offsetWidth,a.height=a.offsetHeight};n(),window.addEventListener("resize",n);let l=e=>{let t=a.getBoundingClientRect();i.current={x:e.clientX-t.left,y:e.clientY-t.top}};a.addEventListener("mousemove",l);let o=["#10B981","#3B82F6","#F59E0B","#EF4444","#8B5CF6","#F97316","#06B6D4"],d=["dollar","euro","rupee","bitcoin"],m=()=>{let e,t,s,r;let i=Math.random();return i<.3?(e=-50,t=Math.random()*a.height,s=3*Math.random()+1,r=(Math.random()-.5)*2):i<.6?(e=Math.random()*a.width,t=-50,s=(Math.random()-.5)*2,r=3*Math.random()+1):(e=a.width+50,t=Math.random()*a.height,s=-(3*Math.random()+1),r=(Math.random()-.5)*2),{x:e,y:t,vx:s,vy:r,rotation:Math.random()*Math.PI*2,rotationSpeed:(Math.random()-.5)*.05,size:25*Math.random()+20,opacity:.9*Math.random()+.4,type:d[Math.floor(Math.random()*d.length)],color:o[Math.floor(Math.random()*o.length)],life:0,maxLife:400*Math.random()+300,swayOffset:Math.random()*Math.PI*2,swaySpeed:.02*Math.random()+.01}};(()=>{s.current=[];for(let e=0;e<50;e++)s.current.push(m())})();let c=(e,t)=>{e.save(),e.translate(t.x,t.y),e.rotate(t.rotation),e.globalAlpha=t.opacity*(1-t.life/t.maxLife);let s=1.8*t.size,a=.8*t.size,r=e.createLinearGradient(-s/2,-a/2,s/2,a/2);r.addColorStop(0,t.color),r.addColorStop(.5,"#ffffff40"),r.addColorStop(1,t.color),e.fillStyle=r,e.strokeStyle=t.color,e.lineWidth=2,e.beginPath(),e.roundRect(-s/2,-a/2,s,a,4),e.fill(),e.stroke(),e.fillStyle="#ffffff",e.font=`bold ${.6*t.size}px Arial`,e.textAlign="center",e.textBaseline="middle";let i="$";switch(t.type){case"euro":i="€";break;case"rupee":i="₹";break;case"bitcoin":i="₿";break;default:i="$"}e.fillText(i,0,0);let n=e.createLinearGradient(-s/2,-a/2,s/2,a/2);n.addColorStop(0,"rgba(255,255,255,0)"),n.addColorStop(.5,"rgba(255,255,255,0.3)"),n.addColorStop(1,"rgba(255,255,255,0)"),e.fillStyle=n,e.beginPath(),e.roundRect(-s/2,-a/2,s,a,4),e.fill(),e.restore()},x=()=>{r.clearRect(0,0,a.width,a.height);let e=s.current;for(let t=e.length-1;t>=0;t--){let s=e[t],n=i.current.x-s.x,l=i.current.y-s.y,o=Math.sqrt(n*n+l*l);if(o<150){let e=(150-o)/150;s.vx+=n/o*e*.02,s.vy+=l/o*e*.02}s.swayOffset+=s.swaySpeed;let d=.5*Math.sin(s.swayOffset),m=.3*Math.cos(.7*s.swayOffset);s.x+=s.vx+d,s.y+=s.vy+m,s.rotation+=s.rotationSpeed,s.life++,s.vx*=.995,s.vy+=.01,s.vy*=.995;let x=Math.sqrt(s.vx*s.vx+s.vy*s.vy);x>4&&(s.vx=s.vx/x*4,s.vy=s.vy/x*4),s.x>a.width+100||s.y>a.height+100||s.x<-100||s.y<-100||s.life>s.maxLife?e.splice(t,1):c(r,s)}e.length<80&&.6>Math.random()&&e.push(m()),r.strokeStyle="rgba(16, 185, 129, 0.2)",r.lineWidth=1.5;for(let t=0;t<e.length;t++)for(let s=t+1;s<e.length;s++){let a=e[t].x-e[s].x,i=e[t].y-e[s].y,n=Math.sqrt(a*a+i*i);if(n<120){let a=(120-n)/120*.25;r.globalAlpha=a,r.beginPath(),r.moveTo(e[t].x,e[t].y),r.lineTo(e[s].x,e[s].y),r.stroke(),r.globalAlpha=1}}t.current=requestAnimationFrame(x)};return x(),()=>{window.removeEventListener("resize",n),a.removeEventListener("mousemove",l),t.current&&cancelAnimationFrame(t.current)}},[]),(0,a.jsx)("canvas",{ref:e,className:"absolute inset-0 w-full h-full pointer-events-none",style:{zIndex:1}})}},5930:(e,t,s)=>{"use strict";s.d(t,{default:()=>n});var a=s(5512),r=s(8009),i=s(5103);let n=({tier:e,price:t,title:s,description:n,icon:l,mainFeatures:o,timeFeatures:d,clothing:m,peds:c,isPopular:x=!1,className:g="",currency:h="₹"})=>{let[u,p]=(0,r.useState)("features"),[b,f]=(0,r.useState)(!1),v="border-orange-500",j="text-orange-400",y="bg-orange-500",N="from-orange-500/30 to-yellow-500/30",w="shadow-orange-500/30",P="fas fa-star",T=!1;return 1===e?(v="border-red-800",j="text-red-300",y="bg-red-800",N="from-red-800/30 to-red-700/30",w="shadow-red-800/30",P="fas fa-medal"):2===e?(v="border-gray-400",j="text-gray-300",y="bg-gray-400",N="from-gray-400/30 to-gray-300/30",w="shadow-gray-400/30",P="fas fa-award"):3===e?(v="border-amber-600",j="text-amber-400",y="bg-amber-600",N="from-amber-600/30 to-amber-500/30",w="shadow-amber-600/30",P="fas fa-trophy"):4===e?(v="border-violet-500",j="text-violet-400",y="bg-violet-500",N="from-violet-500/30 to-violet-400/30",w="shadow-violet-500/30",P="fas fa-gem",T=!0):5===e&&(v="border-yellow-400",j="text-yellow-300",y="bg-yellow-400",N="from-yellow-400/30 to-yellow-300/30",w="shadow-yellow-400/30",P="fas fa-crown",T=!0),(0,a.jsxs)("div",{className:`relative ${g} group`,children:[x&&(0,a.jsx)("div",{className:"absolute -top-2 right-4 z-30",children:(0,a.jsx)("div",{className:"bg-gradient-to-r from-yellow-400 to-orange-500 px-3 py-1 rounded-md text-black text-xs font-bold shadow-lg",children:"POPULAR"})}),(0,a.jsx)("div",{className:`absolute inset-0 rounded-3xl bg-gradient-to-r ${N} opacity-20 blur-xl pointer-events-none`}),T&&(0,a.jsx)("div",{className:`absolute inset-0 rounded-3xl bg-gradient-to-r ${N} opacity-40 blur-2xl pointer-events-none animate-pulse`}),(0,a.jsxs)("div",{className:`bg-black/95 backdrop-blur-sm rounded-3xl border-2 ${v} hover:border-opacity-100 transition-all duration-500 overflow-hidden relative h-full flex flex-col shadow-xl ${w} group-hover:shadow-2xl group-hover:${w.replace("/30","/50")}`,children:[(0,a.jsxs)("div",{className:"p-8 border-b border-gray-700/30 bg-gradient-to-r from-gray-900/50 to-black/50 relative overflow-hidden",children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent -skew-x-12 translate-x-[-100%] group-hover:translate-x-[200%] transition-transform duration-1000 ease-out"}),(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("div",{className:`w-16 h-16 rounded-2xl bg-gradient-to-br ${N} border-2 ${v} flex items-center justify-center ${T?"animate-pulse shadow-lg shadow-current/50":"shadow-lg"}`,children:(0,a.jsx)("i",{className:`${P} ${j} text-2xl`})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-3xl font-bold text-white mb-1",children:s||`VIP ${e}`}),(0,a.jsx)("p",{className:"text-gray-300 text-lg font-medium",children:n})]})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsxs)("div",{className:"text-5xl font-bold text-white mb-1",children:[h,t.toLocaleString()]}),(0,a.jsx)("div",{className:"text-gray-400 text-base font-medium",children:"One-time purchase"})]})]})]}),(0,a.jsx)("div",{className:"px-8 py-4 border-b border-gray-700/30 bg-gray-900/30",children:(0,a.jsxs)("div",{className:"flex space-x-3",children:[(0,a.jsx)("button",{onClick:()=>p("features"),className:`flex-1 px-4 py-3 rounded-xl text-sm font-bold transition-all duration-300 text-center whitespace-nowrap ${"features"===u?`${j} bg-gray-800/90 shadow-lg border-2 border-current/40`:"text-gray-400 hover:text-white hover:bg-gray-800/50 border-2 border-transparent"}`,children:"Features"}),c.length>0&&(0,a.jsxs)("button",{onClick:()=>p("peds"),className:`flex-1 px-4 py-3 rounded-xl text-sm font-bold transition-all duration-300 text-center whitespace-nowrap ${"peds"===u?`${j} bg-gray-800/90 shadow-lg border-2 border-current/40`:"text-gray-400 hover:text-white hover:bg-gray-800/50 border-2 border-transparent"}`,children:["Peds (",c.length,")"]}),m.length>0&&(0,a.jsx)("button",{onClick:()=>p("clothing"),className:`flex-1 px-4 py-3 rounded-xl text-sm font-bold transition-all duration-300 text-center whitespace-nowrap ${"clothing"===u?`${j} bg-gray-800/90 shadow-lg border-2 border-current/40`:"text-gray-400 hover:text-white hover:bg-gray-800/50 border-2 border-transparent"}`,children:"Clothing"})]})}),(0,a.jsxs)("div",{className:"p-8 flex-grow",children:["features"===u&&(0,a.jsxs)("div",{className:"space-y-5",children:[o.map((e,t)=>(0,a.jsxs)("div",{className:"flex items-start space-x-4 p-5 rounded-2xl bg-gray-800/40 border border-gray-700/40 hover:bg-gray-800/60 hover:border-gray-600/60 transition-all duration-300 shadow-lg",children:[(0,a.jsx)("div",{className:`w-3 h-3 rounded-full ${y} mt-3 flex-shrink-0 shadow-lg`}),(0,a.jsxs)("div",{className:"flex-grow",children:[(0,a.jsx)("div",{className:"text-white font-bold text-lg mb-3",children:e.text}),e.details&&(0,a.jsx)("div",{className:"text-gray-300 text-base mb-4",children:e.details}),(0,a.jsxs)("div",{className:"flex flex-wrap gap-3",children:[e.highlight&&(0,a.jsx)("span",{className:`${j} text-base font-bold bg-gray-800/80 px-4 py-2 rounded-xl border-2 ${v} shadow-lg`,children:e.highlight}),e.example&&(0,a.jsx)("span",{className:"text-gray-200 text-base font-mono bg-gray-900/80 px-4 py-2 rounded-xl border border-gray-600 shadow-lg",children:e.example})]})]})]},t)),d.length>0&&(0,a.jsxs)("div",{className:"mt-8 p-6 bg-gray-800/30 rounded-2xl border border-gray-700/40 shadow-lg",children:[(0,a.jsx)("h5",{className:"text-white font-bold mb-5 text-xl",children:"Time-Limited Benefits"}),(0,a.jsx)("div",{className:"space-y-4",children:d.map((e,t)=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 bg-gray-800/50 rounded-xl border border-gray-700/50 hover:bg-gray-800/70 transition-all duration-300",children:[(0,a.jsx)("span",{className:"text-white text-lg font-semibold",children:e.text}),(0,a.jsx)("span",{className:`${j} text-base font-bold px-4 py-2 rounded-xl bg-gray-800/80 border-2 ${v} shadow-lg`,children:e.duration})]},t))})]})]}),"peds"===u&&c.length>0&&(0,a.jsxs)("div",{children:[(0,a.jsxs)("h5",{className:"text-white font-semibold mb-6 text-lg",children:["Character Models (",c.length,")"]}),(0,a.jsx)("div",{className:"grid grid-cols-3 gap-4",children:c.map((e,t)=>(0,a.jsx)("div",{className:"group relative",children:(0,a.jsxs)("div",{className:"aspect-square rounded-xl overflow-hidden bg-gray-800/50 border border-gray-700/50 group-hover:border-gray-600 transition-all duration-300 shadow-lg",children:[(0,a.jsx)(i.default,{src:e.image,alt:e.name,width:120,height:120,className:"w-full h-full object-cover group-hover:scale-110 transition-transform duration-300"}),(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-t from-black/80 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"}),(0,a.jsx)("div",{className:"absolute bottom-2 left-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300",children:(0,a.jsx)("div",{className:"text-white text-sm font-semibold truncate text-center bg-black/90 rounded-lg px-3 py-2",children:e.name})})]})},t))})]}),"clothing"===u&&m.length>0&&(0,a.jsxs)("div",{children:[(0,a.jsx)("h5",{className:"text-white font-semibold mb-4 text-base",children:"Exclusive Clothing"}),(0,a.jsx)("div",{className:"space-y-4",children:m.map((e,t)=>(0,a.jsxs)("div",{className:"flex items-center space-x-4 p-4 bg-gray-800/30 rounded-lg border border-gray-700/30 hover:bg-gray-800/50 transition-colors",children:[(0,a.jsx)("div",{className:"w-10 h-10 rounded-lg bg-orange-500/20 flex items-center justify-center",children:(0,a.jsx)("i",{className:"fas fa-tshirt text-orange-400 text-base"})}),(0,a.jsxs)("div",{className:"flex-grow",children:[(0,a.jsx)("h6",{className:"text-white font-semibold text-base mb-1",children:e.name}),(0,a.jsx)("p",{className:"text-gray-400 text-sm mb-2",children:e.description}),e.variants>0&&(0,a.jsxs)("span",{className:"text-orange-400 text-sm font-semibold bg-orange-500/10 px-3 py-1 rounded border border-orange-500/20",children:[e.variants," variants"]})]})]},t))})]})]}),(0,a.jsx)("div",{className:"p-6 border-t border-gray-700/30 bg-gray-900/20 flex justify-center",children:(0,a.jsxs)("button",{className:`relative overflow-hidden px-6 py-2 rounded-lg font-bold text-base transition-all duration-300 ${T?`bg-gradient-to-r ${N} ${j} border-2 ${v} hover:shadow-lg hover:shadow-current/40`:"bg-gray-800/80 text-white border-2 border-gray-600/50 hover:bg-gray-700/80 hover:border-gray-500/70"} hover:scale-110 flex items-center justify-center gap-2 group shadow-md`,onMouseEnter:()=>{(4===e||5===e)&&f(!0)},onMouseLeave:()=>{(4===e||5===e)&&f(!1)},onClick:()=>window.open("https://discord.gg/GAMravHDnB","_blank"),children:[(0,a.jsx)("span",{className:"text-base",children:"\uD83C\uDFAB"}),(0,a.jsx)("span",{children:"Grab Now"}),4===e&&b&&(0,a.jsx)("div",{className:"absolute inset-0 bg-violet-500/30 rounded-lg animate-pulse pointer-events-none"}),5===e&&b&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-yellow-400/30 rounded-lg animate-pulse pointer-events-none"}),(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-transparent via-yellow-300/50 to-transparent -skew-x-12 translate-x-[-100%] animate-shimmer pointer-events-none"})]})]})})]})]})}},1561:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>m,metadata:()=>d});var a=s(2740),r=s(1443),i=s(3852),n=s(3145),l=s(5966),o=s(9211);let d={title:"VIP Packages - TGRS Telugu FiveM Server | Premium Gaming Experience",description:"Unlock premium features with TGRS VIP packages! Get custom cars, priority queue, exclusive clothing, and more. Best VIP benefits for Telugu FiveM players. Join the elite gaming community today!",keywords:["TGRS VIP","FiveM VIP packages","Telugu gaming VIP","FiveM premium features","VIP benefits FiveM","Telugu server VIP","gaming VIP membership","FiveM priority queue","Telugu VIP","VIP Telugu","Telugu VIP server","VIP Telugu server","custom cars FiveM","exclusive gaming features","premium gaming experience","Telugu gaming premium","VIP roleplay features","FiveM VIP perks","premium Telugu gaming","VIP Telugu gaming","Telugu premium server","GTA 5 VIP Telugu","Telugu GTA 5 VIP","GTA VIP Telugu","Telugu GTA VIP","GTA 5 premium Telugu","Telugu GTA 5 premium","GTA roleplay VIP Telugu","SAMP VIP Telugu","Telugu SAMP VIP","SAMP premium Telugu","Telugu SAMP premium","Telugu server VIP","VIP Telugu server","Telugu gaming VIP membership","VIP Telugu gaming membership","Telugu community VIP","VIP Telugu community","Hyderabad gaming VIP","Telangana gaming VIP","Andhra Pradesh gaming VIP","South India gaming VIP","Indian FiveM VIP","Telugu esports VIP","Telugu VIP features","VIP Telugu features","Telugu premium features","premium Telugu features","Telugu VIP benefits","VIP Telugu benefits","Telugu VIP perks","VIP Telugu perks","Telugu VIP packages","Telugu culture VIP","VIP Telugu culture","Telugu tradition VIP","VIP Telugu tradition","Telugu language VIP","VIP Telugu language"],openGraph:{title:"VIP Packages - TGRS Telugu FiveM Server | Premium Gaming Experience",description:"Unlock premium features with TGRS VIP packages! Get custom cars, priority queue, exclusive clothing, and more. Best VIP benefits for Telugu FiveM players.",images:["/assets/vip-og-image.jpg"]},alternates:{canonical:"/vip"}};function m(){let e=[{tier:1,price:299,title:"VIP 1",description:"Start your premium journey",icon:"fas fa-medal",mainFeatures:[{text:"Custom Phone Number",details:"with 9 digits",highlight:"(3 number sequence max)",example:"111888222"},{text:"Custom Car",details:"from TGRS category",highlight:"(< ₹10,00,000)"},{text:"In-game Cash",details:"₹20,000"},{text:"Lucky Wheel Coins",details:"500"}],timeFeatures:[{text:"VIP Role",duration:"1 month"},{text:"Priority Queue",duration:"1 month"}],clothing:[{name:"Yeezes",variants:5,description:"Premium footwear with unique design"}],peds:[],pets:[],tiltDirection:"left",pattern:"circuit"},{tier:2,price:699,title:"VIP 2",description:"Enhanced premium experience",icon:"fas fa-award",mainFeatures:[{text:"Custom Phone Number",details:"with 8 digits",highlight:"(4 number sequence max)",example:"44443333"},{text:"Custom Car",details:"from TGRS category",highlight:"(< ₹18,00,000)"},{text:"Custom Bike",details:"from ANY category",highlight:"(< ₹1,00,000)"},{text:"Custom Numberplate",details:"",highlight:"(4 alphabets 4 numbers)",example:"TGRS1234"},{text:"In-game Cash",details:"₹30,000"},{text:"Lucky Wheel Coins",details:"750"}],timeFeatures:[{text:"VIP Role",duration:"1 month"},{text:"Priority Queue",duration:"1 month"},{text:"Rental Apartment",duration:"1 month"}],clothing:[{name:"TGG Pant",variants:4,description:"Exclusive pants with TGRS branding"}],peds:[],pets:[],tiltDirection:"right",pattern:"hexagon"},{tier:3,price:999,title:"VIP 3",description:"Premium experience with exclusive content",icon:"fas fa-trophy",isPopular:!1,mainFeatures:[{text:"Custom Phone Number",details:"with 6 digits",highlight:"(5 number sequence max)",example:"444441"},{text:"Custom Car",details:"from TGRS category",highlight:"(< ₹25,00,000)"},{text:"Custom Bike",details:"from ANY category",highlight:"(< ₹2,50,000)"},{text:"Custom Numberplate x2",details:"",highlight:"(3 alphabets 3 numbers)",example:"TGR123"},{text:"In-game Cash",details:"₹50,000"},{text:"Lucky Wheel Coins",details:"1250"},{text:"Tax Free",details:"No Taxes"},{text:"Extra Character Slot",details:"+1 slot"}],timeFeatures:[{text:"VIP Role",duration:"1 month"},{text:"Priority Queue",duration:"1 month"},{text:"Tier 1 House",duration:"1 month"}],clothing:[{name:"TGYeezes",variants:5,description:"Premium TGRS edition footwear"}],peds:[{name:"Brenda",image:"/assets/peds/Brenda.jpg"},{name:"Kim Ji Won",image:"/assets/peds/Kim Ji Won.png"},{name:"Rhodeey",image:"/assets/peds/Rhodeey.jpg"},{name:"Leo Fric",image:"/assets/peds/Leo Fric.jpg"}],pets:[],tiltDirection:"left",pattern:"dots"},{tier:4,price:1699,title:"VIP 4",description:"Elite tier with premium vehicles and housing",icon:"fas fa-gem",mainFeatures:[{text:"Custom Phone Number",details:"with 5 digits",highlight:"(4 number sequence max)",example:"44441"},{text:"Custom Car",details:"from TGRS category",highlight:"(< ₹25,00,000 & < ₹10,00,000)"},{text:"Custom Bike",details:"from TGRS category",highlight:"(< ₹3,50,000)"},{text:"Custom Numberplate x3",details:"",highlight:"(2 alphabets 2 numbers)",example:"TG12"},{text:"Boat",details:"Standard model"},{text:"In-game Cash",details:"₹75,000"},{text:"Lucky Wheel Coins",details:"1500"},{text:"Tax Free",details:"No Taxes"},{text:"Extra Character Slot",details:"+1 slot"},{text:"Mechanic Discount",details:"5% off repairs"},{text:"Permanent Apartment",details:"Forever ownership"}],timeFeatures:[{text:"VIP Role",duration:"1 month"},{text:"Priority Queue Premium",duration:"1 month"},{text:"Tier 2 House",duration:"1 month"}],clothing:[{name:"TGYeezes",variants:5,description:"Premium TGRS edition footwear"}],peds:[{name:"Selena",image:"/assets/peds/Selena.jpg"},{name:"Jimmy",image:"/assets/peds/Jimmy.jpg"},{name:"Will Stone",image:"/assets/peds/Will Stone.jpg"},{name:"Manuel",image:"/assets/peds/Manuel.jpg"},{name:"Spencer",image:"/assets/peds/Spencer.jpg"},{name:"Carter80",image:"/assets/peds/Carter80.png"},{name:"P Ballas",image:"/assets/peds/P Ballas.png"},{name:"Vicente",image:"/assets/peds/Vicente.jpg"},{name:"Milton",image:"/assets/peds/Milton.jpg"}],pets:[],tiltDirection:"right",pattern:"grid"},{tier:5,price:2899,title:"VIP 5",description:"Ultimate VIP experience with everything unlocked",icon:"fas fa-crown",mainFeatures:[{text:"Custom Phone Number",details:"with 3 digits",highlight:"(2 number sequence max)",example:"101-999"},{text:"Custom Car",details:"from TGRS category",highlight:"(< ₹45,00,000, ₹18,00,000)"},{text:"Custom Bike",details:"from TGRS category",highlight:"(< ₹5,00,000)"},{text:"Helicopter",details:"1 premium model",highlight:"(One time)"},{text:"Premium Boat",details:"1 luxury model",highlight:"(One time)"},{text:"Custom Numberplate x4",details:"",highlight:"(2 alphabets 2 numbers)",example:"TG12"},{text:"In-game Cash",details:"₹100,000"},{text:"Lucky Wheel Coins",details:"2000"},{text:"Tax Free",details:"No Taxes"},{text:"Extra Character Slot",details:"+1 slot"},{text:"Mechanic Discount",details:"10% off repairs"},{text:"Tier 1 House",details:"Permanent ownership"}],timeFeatures:[{text:"VIP Role",duration:"1 month"},{text:"Priority Queue",duration:"1 month"},{text:"Tier 5 House with Helipad",duration:"1 month"}],clothing:[{name:"Full Clothing Set",variants:0,description:"Complete collection from VIP1 to VIP5"},{name:"TGYeezes",variants:5,description:"Premium TGRS edition footwear"}],peds:[{name:"Adeline",image:"/assets/peds/Adeline.jpg"},{name:"Akira",image:"/assets/peds/Akira.jpg"},{name:"Ana Jasmine",image:"/assets/peds/Ana Jasmine.jpg"},{name:"Andreas",image:"/assets/peds/Andreas.png"},{name:"Anita",image:"/assets/peds/Anita.png"},{name:"Antonio",image:"/assets/peds/Antonio.png"},{name:"Beany",image:"/assets/peds/Beany.jpg"},{name:"Bjorn",image:"/assets/peds/Bjorn.jpg"},{name:"Bobby",image:"/assets/peds/Bobby.jpg"},{name:"Brucie Kibbutz",image:"/assets/peds/Brucie Kibbutz.png"},{name:"Carlos",image:"/assets/peds/Carlos.jpg"},{name:"Catline",image:"/assets/peds/Catline.jpg"},{name:"Cheng",image:"/assets/peds/Cheng.jpg"},{name:"Dario Mafia",image:"/assets/peds/Dario Mafia.jpg"},{name:"Diyo80",image:"/assets/peds/Diyo80.jpg"},{name:"Djarot",image:"/assets/peds/Djarot.jpg"},{name:"Estina Meil",image:"/assets/peds/Estina Meil.jpg"},{name:"Ethan",image:"/assets/peds/Ethan.jpg"},{name:"Geo",image:"/assets/peds/Geo.jpg"},{name:"Gregor",image:"/assets/peds/Gregor.jpg"},{name:"Hao",image:"/assets/peds/Hao.png"},{name:"Hendrix",image:"/assets/peds/Hendrix.jpg"},{name:"Hendry80",image:"/assets/peds/Hendry80.png"},{name:"Jacob",image:"/assets/peds/Jacob.jpg"},{name:"Jennifer",image:"/assets/peds/Jennifer.jpg"},{name:"Jimenez",image:"/assets/peds/Jimenez.png"},{name:"Jonson",image:"/assets/peds/Jonson.jpg"},{name:"Julio Bert",image:"/assets/peds/Julio Bert.jpg"},{name:"Lucia",image:"/assets/peds/Lucia.jpg"},{name:"Marcella",image:"/assets/peds/Marcella.jpg"},{name:"Marlo",image:"/assets/peds/Marlo.jpg"},{name:"Martinez",image:"/assets/peds/Martinez.jpg"},{name:"Michelle",image:"/assets/peds/Michelle.jpg"},{name:"Moretio",image:"/assets/peds/Moretio.jpg"},{name:"Norman",image:"/assets/peds/Norman.jpg"},{name:"Pablo",image:"/assets/peds/Pablo.jpg"},{name:"Pietro",image:"/assets/peds/Pietro.jpg"},{name:"Raymond",image:"/assets/peds/Raymond.jpg"},{name:"Rocky",image:"/assets/peds/Rocky.png"},{name:"Roscoe",image:"/assets/peds/Roscoe.png"},{name:"Velden",image:"/assets/peds/Velden.jpg"},{name:"Yuna",image:"/assets/peds/Yuna.jpg"}],pets:[],isPopular:!0,tiltDirection:"left",pattern:"circuit"}];return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(r.default,{}),(0,a.jsx)(n.A,{title:"VIP PACKAGES",subtitle:"Unlock exclusive features and enhance your roleplay experience with our premium VIP packages",primaryButtonText:"Contact Admin",primaryButtonHref:"https://discord.gg/GAMravHDnB",secondaryButtonText:"View Features",secondaryButtonHref:"#packages",backgroundImage:"/assets/vip.jpg"}),(0,a.jsx)("div",{className:"section-divider"}),(0,a.jsx)("section",{className:"py-12 md:py-20 relative bg-black/50",children:(0,a.jsxs)("div",{className:"container mx-auto px-4 md:px-6",children:[(0,a.jsxs)("div",{className:"text-center mb-12 md:mb-16",children:[(0,a.jsxs)("h2",{className:"text-2xl sm:text-3xl md:text-4xl font-display font-bold text-white mb-4 wave-heading-bg",children:["Why Choose ",(0,a.jsx)("span",{className:"text-neon-orange",children:"VIP"}),"?"]}),(0,a.jsx)("p",{className:"text-sm sm:text-base text-gray-400 max-w-3xl mx-auto px-2",children:"Our VIP packages are designed to enhance your roleplay experience with exclusive features, priority support, and unique customization options that set you apart in the TGRS community."})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 sm:gap-8 mb-12 md:mb-16",children:[(0,a.jsxs)("div",{className:"text-center group p-4",children:[(0,a.jsx)("div",{className:"w-12 h-12 sm:w-16 sm:h-16 rounded-full bg-neon-orange/20 flex items-center justify-center mx-auto mb-3 sm:mb-4 group-hover:bg-neon-orange/30 transition-colors duration-300 group-hover:scale-110 transform",children:(0,a.jsx)("i",{className:"fas fa-star text-neon-orange text-lg sm:text-2xl"})}),(0,a.jsx)("h3",{className:"text-white font-semibold mb-2 text-sm sm:text-base group-hover:text-neon-orange transition-colors duration-300",children:"Exclusive Features"}),(0,a.jsx)("p",{className:"text-gray-400 text-xs sm:text-sm",children:"Access unique customization options and premium content"})]}),(0,a.jsxs)("div",{className:"text-center group p-4",children:[(0,a.jsx)("div",{className:"w-12 h-12 sm:w-16 sm:h-16 rounded-full bg-neon-orange/20 flex items-center justify-center mx-auto mb-3 sm:mb-4 group-hover:bg-neon-orange/30 transition-colors duration-300 group-hover:scale-110 transform",children:(0,a.jsx)("i",{className:"fas fa-headset text-neon-orange text-lg sm:text-2xl"})}),(0,a.jsx)("h3",{className:"text-white font-semibold mb-2 text-sm sm:text-base group-hover:text-neon-orange transition-colors duration-300",children:"Priority Support"}),(0,a.jsx)("p",{className:"text-gray-400 text-xs sm:text-sm",children:"Get faster response times and dedicated assistance"})]}),(0,a.jsxs)("div",{className:"text-center group p-4",children:[(0,a.jsx)("div",{className:"w-12 h-12 sm:w-16 sm:h-16 rounded-full bg-neon-orange/20 flex items-center justify-center mx-auto mb-3 sm:mb-4 group-hover:bg-neon-orange/30 transition-colors duration-300 group-hover:scale-110 transform",children:(0,a.jsx)("i",{className:"fas fa-users text-neon-orange text-lg sm:text-2xl"})}),(0,a.jsx)("h3",{className:"text-white font-semibold mb-2 text-sm sm:text-base group-hover:text-neon-orange transition-colors duration-300",children:"VIP Community"}),(0,a.jsx)("p",{className:"text-gray-400 text-xs sm:text-sm",children:"Join an exclusive community of premium members"})]}),(0,a.jsxs)("div",{className:"text-center group p-4",children:[(0,a.jsx)("div",{className:"w-12 h-12 sm:w-16 sm:h-16 rounded-full bg-neon-orange/20 flex items-center justify-center mx-auto mb-3 sm:mb-4 group-hover:bg-neon-orange/30 transition-colors duration-300 group-hover:scale-110 transform",children:(0,a.jsx)("i",{className:"fas fa-gem text-neon-orange text-lg sm:text-2xl"})}),(0,a.jsx)("h3",{className:"text-white font-semibold mb-2 text-sm sm:text-base group-hover:text-neon-orange transition-colors duration-300",children:"Great Value"}),(0,a.jsx)("p",{className:"text-gray-400 text-xs sm:text-sm",children:"One-time purchase with lifetime benefits"})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4 sm:gap-6 md:gap-8",children:[(0,a.jsxs)("div",{className:"text-center p-3 sm:p-4",children:[(0,a.jsx)("div",{className:"text-2xl sm:text-3xl md:text-4xl font-display font-bold text-neon-orange mb-2",children:"500+"}),(0,a.jsx)("div",{className:"text-gray-400 text-xs sm:text-sm",children:"VIP Members"})]}),(0,a.jsxs)("div",{className:"text-center p-3 sm:p-4",children:[(0,a.jsx)("div",{className:"text-2xl sm:text-3xl md:text-4xl font-display font-bold text-neon-orange mb-2",children:"50+"}),(0,a.jsx)("div",{className:"text-gray-400 text-xs sm:text-sm",children:"Exclusive Peds"})]}),(0,a.jsxs)("div",{className:"text-center p-3 sm:p-4",children:[(0,a.jsx)("div",{className:"text-2xl sm:text-3xl md:text-4xl font-display font-bold text-neon-orange mb-2",children:"100+"}),(0,a.jsx)("div",{className:"text-gray-400 text-xs sm:text-sm",children:"Custom Vehicles"})]}),(0,a.jsxs)("div",{className:"text-center p-3 sm:p-4",children:[(0,a.jsx)("div",{className:"text-2xl sm:text-3xl md:text-4xl font-display font-bold text-neon-orange mb-2",children:"24/7"}),(0,a.jsx)("div",{className:"text-gray-400 text-xs sm:text-sm",children:"VIP Support"})]})]})]})}),(0,a.jsx)("div",{className:"section-divider"}),(0,a.jsxs)("section",{id:"packages",className:"py-12 md:py-20 relative bg-black",children:[(0,a.jsx)(o.default,{}),(0,a.jsxs)("div",{className:"container mx-auto px-4 sm:px-6 md:px-8 lg:px-12 relative z-10",children:[(0,a.jsxs)("div",{className:"text-center mb-12 md:mb-16",children:[(0,a.jsxs)("h2",{className:"text-2xl sm:text-3xl md:text-4xl font-display font-bold text-white mb-4 wave-heading-bg",children:["Choose Your ",(0,a.jsx)("span",{className:"text-neon-orange",children:"VIP Tier"})]}),(0,a.jsx)("p",{className:"text-sm sm:text-base text-gray-400 max-w-3xl mx-auto px-2",children:"Select the perfect VIP package that suits your needs and budget. All packages include lifetime access to their respective features."})]}),(0,a.jsxs)("div",{className:"space-y-8 sm:space-y-12 max-w-7xl mx-auto",children:[(0,a.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8 items-stretch",children:e.slice(0,3).map(e=>(0,a.jsx)(l.default,{tier:e.tier,price:e.price,title:e.title,description:e.description,icon:e.icon,mainFeatures:e.mainFeatures,timeFeatures:e.timeFeatures,clothing:e.clothing,peds:e.peds,isPopular:e.isPopular,tiltDirection:e.tiltDirection,pattern:e.pattern,className:"animate-slide-in-left"},e.tier))}),(0,a.jsx)("div",{className:"flex justify-center",children:(0,a.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-6 sm:gap-8 w-full max-w-5xl items-stretch",children:e.slice(3,5).map(e=>(0,a.jsx)(l.default,{tier:e.tier,price:e.price,title:e.title,description:e.description,icon:e.icon,mainFeatures:e.mainFeatures,timeFeatures:e.timeFeatures,clothing:e.clothing,peds:e.peds,isPopular:e.isPopular,tiltDirection:e.tiltDirection,pattern:e.pattern,className:"animate-slide-in-left"},e.tier))})})]})]})]}),(0,a.jsx)("div",{className:"section-divider"}),(0,a.jsxs)("section",{className:"py-12 md:py-20 relative bg-black",children:[(0,a.jsx)("div",{className:"absolute inset-0 pattern-hexagon opacity-5"}),(0,a.jsxs)("div",{className:"container mx-auto px-4 md:px-6 relative z-10",children:[(0,a.jsxs)("div",{className:"text-center mb-12 md:mb-16",children:[(0,a.jsxs)("h2",{className:"text-2xl sm:text-3xl md:text-4xl font-display font-bold text-white mb-4 wave-heading-bg",children:[(0,a.jsx)("i",{className:"fas fa-gavel text-neon-orange mr-2 sm:mr-4 text-lg sm:text-xl md:text-2xl"}),"VIP Rules & ",(0,a.jsx)("span",{className:"text-neon-orange",children:"Guidelines"})]}),(0,a.jsx)("p",{className:"text-sm sm:text-base text-gray-400 max-w-3xl mx-auto px-2",children:"Important information and guidelines for all VIP members. Please read carefully to ensure you understand all terms and conditions."})]}),(0,a.jsxs)("div",{className:"max-w-4xl mx-auto space-y-4 sm:space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-start space-x-3 sm:space-x-6 p-4 sm:p-6 rounded-xl bg-gray-900/40 border-l-4 border-neon-orange hover:bg-gray-900/60 transition-all duration-300 group",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("div",{className:"w-10 h-10 sm:w-12 sm:h-12 rounded-full bg-neon-orange/20 flex items-center justify-center group-hover:bg-neon-orange/30 transition-colors",children:(0,a.jsx)("i",{className:"fas fa-clock text-neon-orange text-sm sm:text-lg"})})}),(0,a.jsxs)("div",{className:"flex-grow",children:[(0,a.jsx)("h3",{className:"text-white font-bold text-base sm:text-lg mb-2",children:"VIP Duration Policy"}),(0,a.jsxs)("p",{className:"text-gray-300 leading-relaxed text-sm sm:text-base",children:["VIP benefits are active for ",(0,a.jsx)("span",{className:"text-neon-orange font-semibold",children:"1 month from the date of purchase"})," unless specified otherwise in your tier package."]})]})]}),(0,a.jsxs)("div",{className:"flex items-start space-x-6 p-6 rounded-xl bg-gray-900/40 border-l-4 border-neon-orange hover:bg-gray-900/60 transition-all duration-300 group",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("div",{className:"w-12 h-12 rounded-full bg-neon-orange/20 flex items-center justify-center group-hover:bg-neon-orange/30 transition-colors",children:(0,a.jsx)("i",{className:"fas fa-car text-neon-orange text-lg"})})}),(0,a.jsxs)("div",{className:"flex-grow",children:[(0,a.jsx)("h3",{className:"text-white font-bold text-lg mb-2",children:"Vehicle Selection Rules"}),(0,a.jsxs)("p",{className:"text-gray-300 leading-relaxed",children:["Custom vehicles must be selected from the ",(0,a.jsx)("span",{className:"text-neon-orange font-semibold",children:"TGRS category"})," within the specified price range for your VIP tier."]})]})]}),(0,a.jsxs)("div",{className:"flex items-start space-x-6 p-6 rounded-xl bg-gray-900/40 border-l-4 border-neon-orange hover:bg-gray-900/60 transition-all duration-300 group",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("div",{className:"w-12 h-12 rounded-full bg-neon-orange/20 flex items-center justify-center group-hover:bg-neon-orange/30 transition-colors",children:(0,a.jsx)("i",{className:"fas fa-mobile-alt text-neon-orange text-lg"})})}),(0,a.jsxs)("div",{className:"flex-grow",children:[(0,a.jsx)("h3",{className:"text-white font-bold text-lg mb-2",children:"Custom Numbers & Plates"}),(0,a.jsxs)("p",{className:"text-gray-300 leading-relaxed",children:["Custom phone numbers and numberplates are ",(0,a.jsx)("span",{className:"text-neon-orange font-semibold",children:"subject to availability"})," and must follow server guidelines for appropriate content."]})]})]}),(0,a.jsxs)("div",{className:"flex items-start space-x-6 p-6 rounded-xl bg-gray-900/40 border-l-4 border-red-500 hover:bg-gray-900/60 transition-all duration-300 group",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("div",{className:"w-12 h-12 rounded-full bg-red-500/20 flex items-center justify-center group-hover:bg-red-500/30 transition-colors",children:(0,a.jsx)("i",{className:"fas fa-exclamation-triangle text-red-400 text-lg"})})}),(0,a.jsxs)("div",{className:"flex-grow",children:[(0,a.jsx)("h3",{className:"text-white font-bold text-lg mb-2",children:"Phone Number Restrictions"}),(0,a.jsxs)("p",{className:"text-gray-300 leading-relaxed",children:["Custom phone numbers cannot use ",(0,a.jsx)("span",{className:"text-red-400 font-semibold",children:"emergency numbers (e.g., 911)"})," and are limited to the sequence length specified in your VIP tier."]})]})]}),(0,a.jsxs)("div",{className:"flex items-start space-x-6 p-6 rounded-xl bg-gray-900/40 border-l-4 border-neon-orange hover:bg-gray-900/60 transition-all duration-300 group",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("div",{className:"w-12 h-12 rounded-full bg-neon-orange/20 flex items-center justify-center group-hover:bg-neon-orange/30 transition-colors",children:(0,a.jsx)("i",{className:"fas fa-home text-neon-orange text-lg"})})}),(0,a.jsxs)("div",{className:"flex-grow",children:[(0,a.jsx)("h3",{className:"text-white font-bold text-lg mb-2",children:"Housing Benefits"}),(0,a.jsxs)("p",{className:"text-gray-300 leading-relaxed",children:["Some housing benefits expire after ",(0,a.jsx)("span",{className:"text-neon-orange font-semibold",children:"1 month"})," and do not include furniture or decorations unless specifically mentioned."]})]})]}),(0,a.jsxs)("div",{className:"flex items-start space-x-6 p-6 rounded-xl bg-gray-900/40 border-l-4 border-neon-orange hover:bg-gray-900/60 transition-all duration-300 group",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("div",{className:"w-12 h-12 rounded-full bg-neon-orange/20 flex items-center justify-center group-hover:bg-neon-orange/30 transition-colors",children:(0,a.jsx)("i",{className:"fas fa-shield-alt text-neon-orange text-lg"})})}),(0,a.jsxs)("div",{className:"flex-grow",children:[(0,a.jsx)("h3",{className:"text-white font-bold text-lg mb-2",children:"Server Rules Compliance"}),(0,a.jsxs)("p",{className:"text-gray-300 leading-relaxed",children:["All VIP members must adhere to server rules. ",(0,a.jsx)("span",{className:"text-neon-orange font-semibold",children:"VIP status does not exempt players"})," from following community guidelines."]})]})]})]}),(0,a.jsx)("div",{className:"mt-12 max-w-4xl mx-auto text-center",children:(0,a.jsxs)("div",{className:"glass rounded-xl p-6 border border-neon-orange/30",children:[(0,a.jsx)("h3",{className:"text-white font-bold text-xl mb-4",children:"Important Notice"}),(0,a.jsx)("p",{className:"text-gray-300 leading-relaxed mb-6",children:"By purchasing VIP, you agree to follow these rules and guidelines. The staff team reserves the right to modify these terms at any time."}),(0,a.jsxs)("a",{href:"https://discord.gg/GAMravHDnB",target:"_blank",rel:"noopener noreferrer",className:"inline-flex items-center px-6 py-3 bg-gradient-to-r from-neon-orange to-neon-red text-black font-semibold rounded-lg hover:shadow-neon-strong transition-all duration-300",children:[(0,a.jsx)("i",{className:"fab fa-discord mr-2"}),"Join Discord for Support"]})]})})]})]}),(0,a.jsx)("div",{className:"section-divider"}),(0,a.jsx)("section",{className:"py-8 sm:py-12 md:py-16 relative bg-black/50",children:(0,a.jsx)("div",{className:"container mx-auto px-4 md:px-6",children:(0,a.jsx)("div",{className:"text-center",children:(0,a.jsxs)("div",{className:"glass rounded-lg p-4 sm:p-6 md:p-8 border border-neon-orange/30 max-w-4xl mx-auto",children:[(0,a.jsx)("h3",{className:"text-lg sm:text-xl md:text-2xl font-display font-bold text-white mb-3 sm:mb-4",children:"Ready to Upgrade Your Experience?"}),(0,a.jsx)("p",{className:"text-xs sm:text-sm md:text-base text-gray-400 mb-4 sm:mb-6 px-2",children:"Contact our admin team on Discord to purchase your VIP package. Payment methods and additional details will be provided upon contact."}),(0,a.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-3 gap-3 sm:gap-4 md:gap-6",children:[(0,a.jsxs)("div",{className:"text-center p-2 sm:p-3",children:[(0,a.jsx)("div",{className:"w-8 h-8 sm:w-10 sm:h-10 md:w-12 md:h-12 rounded-full bg-neon-orange/20 flex items-center justify-center mx-auto mb-2 sm:mb-3",children:(0,a.jsx)("i",{className:"fas fa-bolt text-neon-orange text-xs sm:text-sm md:text-base"})}),(0,a.jsx)("h4",{className:"text-white font-semibold mb-1 sm:mb-2 text-xs sm:text-sm md:text-base",children:"Instant Activation"}),(0,a.jsx)("p",{className:"text-gray-400 text-xs sm:text-sm",children:"VIP features activated immediately after purchase"})]}),(0,a.jsxs)("div",{className:"text-center p-2 sm:p-3",children:[(0,a.jsx)("div",{className:"w-8 h-8 sm:w-10 sm:h-10 md:w-12 md:h-12 rounded-full bg-neon-orange/20 flex items-center justify-center mx-auto mb-2 sm:mb-3",children:(0,a.jsx)("i",{className:"fas fa-shield-alt text-neon-orange text-xs sm:text-sm md:text-base"})}),(0,a.jsx)("h4",{className:"text-white font-semibold mb-1 sm:mb-2 text-xs sm:text-sm md:text-base",children:"Secure Payment"}),(0,a.jsx)("p",{className:"text-gray-400 text-xs sm:text-sm",children:"Safe and secure payment processing"})]}),(0,a.jsxs)("div",{className:"text-center p-2 sm:p-3",children:[(0,a.jsx)("div",{className:"w-8 h-8 sm:w-10 sm:h-10 md:w-12 md:h-12 rounded-full bg-neon-orange/20 flex items-center justify-center mx-auto mb-2 sm:mb-3",children:(0,a.jsx)("i",{className:"fas fa-infinity text-neon-orange text-xs sm:text-sm md:text-base"})}),(0,a.jsx)("h4",{className:"text-white font-semibold mb-1 sm:mb-2 text-xs sm:text-sm md:text-base",children:"Lifetime Access"}),(0,a.jsx)("p",{className:"text-gray-400 text-xs sm:text-sm",children:"One-time purchase, lifetime benefits"})]})]})]})})})}),(0,a.jsx)(i.A,{})]})}},9211:(e,t,s)=>{"use strict";s.d(t,{default:()=>a});let a=(0,s(6760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\TGRS Website\\\\tgrs-website\\\\src\\\\components\\\\CashFlyingAnimation.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\TGRS Website\\tgrs-website\\src\\components\\CashFlyingAnimation.tsx","default")},3145:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});var a=s(2740);s(6301);let r=({title:e,subtitle:t,primaryButtonText:s,primaryButtonHref:r,secondaryButtonText:i,secondaryButtonHref:n,backgroundImage:l="/assets/image-3.jpg"})=>(0,a.jsxs)("div",{className:"relative min-h-screen flex items-center justify-center overflow-hidden",children:[(0,a.jsx)("div",{className:"absolute inset-0 z-0 bg-cover bg-center bg-no-repeat",style:{backgroundImage:`url('${l}')`}}),(0,a.jsxs)("div",{className:"absolute inset-0 z-10",children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-black opacity-60"}),(0,a.jsx)("div",{className:"absolute top-0 left-0 w-full h-full bg-gradient-to-b from-black/20 via-transparent to-black/80"}),(0,a.jsx)("div",{className:"absolute inset-0 flex flex-col opacity-5",children:Array.from({length:15}).map((e,t)=>(0,a.jsx)("div",{className:"h-px bg-neon-orange w-full animate-pulse-slow",style:{marginTop:`${7*t}vh`,animationDelay:`${.2*t}s`}},`h-${t}`))}),(0,a.jsx)("div",{className:"absolute inset-0 flex flex-row opacity-5",children:Array.from({length:15}).map((e,t)=>(0,a.jsx)("div",{className:"w-px bg-neon-orange h-full animate-pulse-slow",style:{marginLeft:`${7*t}vw`,animationDelay:`${.2*t}s`}},`v-${t}`))}),(0,a.jsx)("div",{className:"absolute top-1/4 left-1/4 w-96 h-96 rounded-full bg-neon-orange/10 blur-3xl animate-float"}),(0,a.jsx)("div",{className:"absolute bottom-1/4 right-1/4 w-64 h-64 rounded-full bg-neon-red/10 blur-3xl animate-float",style:{animationDelay:"3s"}})]}),(0,a.jsx)("div",{className:"container mx-auto px-4 md:px-6 relative z-20 text-center flex flex-col justify-center min-h-[75vh] pt-20 md:pt-36",children:(0,a.jsxs)("div",{className:"space-y-4 md:space-y-6",children:[(0,a.jsx)("h1",{className:"text-3xl sm:text-4xl md:text-6xl lg:text-7xl font-display font-bold tracking-wider text-white leading-tight",children:(0,a.jsx)("span",{className:"inline-block",children:e})}),(0,a.jsx)("p",{className:"text-lg sm:text-xl md:text-2xl text-gray-300 max-w-3xl mx-auto leading-relaxed px-2",children:t})]})}),(0,a.jsxs)("div",{className:"absolute bottom-6 md:bottom-10 left-1/2 transform -translate-x-1/2 flex flex-col items-center z-20",children:[(0,a.jsxs)("div",{className:"block md:hidden",children:[(0,a.jsx)("span",{className:"text-white text-xs mb-2",children:"Swipe Up"}),(0,a.jsx)("div",{className:"flex items-center justify-center",children:(0,a.jsx)("div",{className:"w-8 h-8 flex items-center justify-center",children:(0,a.jsxs)("svg",{className:"w-6 h-6 text-white animate-bounce",fill:"currentColor",viewBox:"0 0 24 24",children:[(0,a.jsx)("path",{d:"M12 2c-1.1 0-2 .9-2 2v8c0 .55-.45 1-1 1s-1-.45-1-1V8c0-1.1-.9-2-2-2s-2 .9-2 2v4c0 .55-.45 1-1 1s-1-.45-1-1V10c0-1.1-.9-2-2-2s-2 .9-2 2v8c0 3.31 2.69 6 6 6h6c3.31 0 6-2.69 6-6V4c0-1.1-.9-2-2-2s-2 .9-2 2v8c0 .55-.45 1-1 1s-1-.45-1-1V4c0-1.1-.9-2-2-2z"}),(0,a.jsx)("path",{d:"M12 8l-3 3h2v4h2v-4h2l-3-3z",fill:"white",opacity:"0.8"})]})})})]}),(0,a.jsxs)("div",{className:"hidden md:flex md:flex-col md:items-center",children:[(0,a.jsx)("span",{className:"text-white text-sm mb-2",children:"Scroll Down"}),(0,a.jsx)("div",{className:"w-6 h-10 border-2 border-white rounded-full flex justify-center items-start pt-2",children:(0,a.jsx)("div",{className:"w-1 h-2 bg-white rounded-full animate-bounce"})})]})]})]})},5966:(e,t,s)=>{"use strict";s.d(t,{default:()=>a});let a=(0,s(6760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\TGRS Website\\\\tgrs-website\\\\src\\\\components\\\\VIPCardNew.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\TGRS Website\\tgrs-website\\src\\components\\VIPCardNew.tsx","default")}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[638,413,666,685,540,225,328],()=>s(3971));module.exports=a})();